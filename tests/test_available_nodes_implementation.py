"""
Test suite for available_nodes field implementation in workflow and marketplace functions.

This test suite follows Test Driven Development (TDD) approach to ensure that the
available_nodes field is properly handled in both workflow creation/retrieval and
marketplace operations, aligning with the database schema.
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.services.workflow_functions import WorkflowFunctions
from app.services.marketplace_functions import WorkflowMarketplaceFunctions
from app.grpc_ import workflow_pb2


class TestAvailableNodesWorkflowFunctions:
    """Test available_nodes field in WorkflowFunctions"""

    def setup_method(self):
        """Setup test fixtures"""
        self.workflow_functions = WorkflowFunctions()
        self.mock_db = Mock(spec=Session)
        
        # Sample available_nodes data
        self.sample_available_nodes = [
            {
                "id": "node_1",
                "type": "mcp_node",
                "name": "Test MCP Node",
                "category": "data_processing"
            },
            {
                "id": "node_2", 
                "type": "component_node",
                "name": "Test Component Node",
                "category": "ai_processing"
            }
        ]

    @patch('app.services.workflow_functions.SessionLocal')
    @patch('app.utils.workflow_data_transformer.extract_mcp_and_component_nodes_exact_format')
    @patch('app.utils.file_upload.GCSUploadService')
    @patch('app.services.workflow_builder.workflow_schema_converter.convert_workflow_to_transition_schema')
    @patch('app.utils.json_validator.validate_transition_schema')
    def test_create_workflow_includes_available_nodes(
        self, mock_validate, mock_convert, mock_gcs, mock_extract_nodes, mock_session
    ):
        """Test that createWorkflow properly handles available_nodes field"""
        # Setup mocks
        mock_db = Mock()
        mock_session.return_value = mock_db
        mock_extract_nodes.return_value = self.sample_available_nodes
        mock_gcs_instance = Mock()
        mock_gcs.return_value = mock_gcs_instance
        mock_gcs_instance.upload_json_as_file.return_value = {"publicUrl": "https://test.url"}
        mock_convert.return_value = {"test": "data"}
        mock_validate.return_value = True

        # Create request
        request = workflow_pb2.CreateWorkflowRequest()
        request.name = "Test Workflow"
        request.workflow_data = '{"nodes": [], "edges": []}'
        request.start_nodes.extend(['{"id": "start", "type": "start"}'])
        request.owner.id = "test-user"
        request.owner_type = workflow_pb2.WorkflowOwnerType.USER

        context = Mock()
        
        # Execute
        response = self.workflow_functions.createWorkflow(request, context)
        
        # Verify available_nodes extraction was called
        mock_extract_nodes.assert_called_once()
        
        # Verify workflow creation includes available_nodes
        mock_db.add.assert_called()
        workflow_call = mock_db.add.call_args_list[0][0][0]  # First call, first argument
        assert hasattr(workflow_call, 'available_nodes')
        assert workflow_call.available_nodes == self.sample_available_nodes

    @patch('app.services.workflow_functions.SessionLocal')
    def test_create_workflow_version_includes_available_nodes(self, mock_session):
        """Test that WorkflowVersion creation includes available_nodes field"""
        # Setup mocks
        mock_db = Mock()
        mock_session.return_value = mock_db
        
        # Create a mock workflow with available_nodes
        mock_workflow = Mock(spec=Workflow)
        mock_workflow.id = "test-workflow-id"
        mock_workflow.available_nodes = self.sample_available_nodes
        mock_workflow.name = "Test Workflow"
        mock_workflow.description = "Test Description"
        mock_workflow.workflow_url = "https://test.url"
        mock_workflow.builder_url = "https://test-builder.url"
        mock_workflow.start_nodes = [{"id": "start"}]
        mock_workflow.category = "test"
        mock_workflow.tags = ["test"]
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_workflow
        mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = None
        
        # Create request
        request = workflow_pb2.CreateWorkflowVersionRequest()
        request.workflow_id = "test-workflow-id"
        request.user_id = "test-user"
        request.auto_increment = True
        request.name = "Test Version"
        request.description = "Test Version Description"
        request.changelog = "Test changelog"
        
        context = Mock()
        
        # Execute
        response = self.workflow_functions.createWorkflowVersion(request, context)
        
        # Verify WorkflowVersion creation includes available_nodes
        mock_db.add.assert_called()
        version_call = mock_db.add.call_args_list[0][0][0]  # First call, first argument
        assert hasattr(version_call, 'available_nodes')
        assert version_call.available_nodes == self.sample_available_nodes

    @patch('app.services.workflow_functions.SessionLocal')
    def test_workflow_to_protobuf_includes_available_nodes(self, mock_session):
        """Test that _workflow_to_protobuf includes available_nodes field"""
        # Setup mock workflow
        mock_workflow = Mock(spec=Workflow)
        mock_workflow.id = "test-id"
        mock_workflow.name = "Test Workflow"
        mock_workflow.description = "Test Description"
        mock_workflow.workflow_url = "https://test.url"
        mock_workflow.builder_url = "https://test-builder.url"
        mock_workflow.owner_id = "test-user"
        mock_workflow.user_ids = ["test-user"]
        mock_workflow.owner_type = "user"
        mock_workflow.start_nodes = [{"id": "start"}]
        mock_workflow.available_nodes = self.sample_available_nodes
        mock_workflow.workflow_template_id = None
        mock_workflow.template_owner_id = None
        mock_workflow.is_imported = False
        mock_workflow.is_changes_marketplace = False
        mock_workflow.is_customizable = True
        mock_workflow.version = "1.0.0"
        mock_workflow.visibility = "private"
        mock_workflow.category = "test"
        mock_workflow.tags = ["test"]
        mock_workflow.status = "active"
        mock_workflow.created_at = None
        mock_workflow.updated_at = None
        mock_workflow.current_version_id = None
        
        # Execute
        result = self.workflow_functions._workflow_to_protobuf(mock_workflow)
        
        # Verify available_nodes is included and properly serialized
        assert hasattr(result, 'available_nodes')
        assert len(result.available_nodes) == 2
        # Verify JSON serialization
        for i, node_json in enumerate(result.available_nodes):
            node_data = json.loads(node_json)
            assert node_data == self.sample_available_nodes[i]

    @patch('app.services.workflow_functions.SessionLocal')
    def test_use_workflow_includes_available_nodes_in_version(self, mock_session):
        """Test that useWorkflow includes available_nodes in created WorkflowVersion"""
        # Setup mocks
        mock_db = Mock()
        mock_session.return_value = mock_db
        
        # Mock marketplace listing
        mock_listing = Mock(spec=WorkflowMarketplaceListing)
        mock_listing.id = "listing-id"
        mock_listing.workflow_id = "source-workflow-id"
        mock_listing.workflow_version_id = "version-id"
        mock_listing.listed_by_user_id = "source-user"
        mock_listing.title = "Test Listing"
        mock_listing.description = "Test Description"
        mock_listing.category = "test"
        mock_listing.tags = ["test"]
        mock_listing.use_count = 0
        mock_listing.updated_at = None
        
        # Mock workflow version with available_nodes
        mock_version = Mock(spec=WorkflowVersion)
        mock_version.id = "version-id"
        mock_version.workflow_url = "https://test.url"
        mock_version.builder_url = "https://test-builder.url"
        mock_version.start_nodes = [{"id": "start"}]
        mock_version.available_nodes = self.sample_available_nodes
        
        # Mock source workflow
        mock_source_workflow = Mock(spec=Workflow)
        mock_source_workflow.is_customizable = True
        
        # Setup query chain
        query_mock = Mock()
        mock_db.query.return_value = query_mock
        filter_mock = Mock()
        query_mock.filter.return_value = filter_mock
        filter_mock.first.side_effect = [mock_listing, mock_version, mock_source_workflow]
        
        # Create request
        request = workflow_pb2.UseWorkflowRequest()
        request.workflow_id = "listing-id"
        request.user_id = "test-user"
        
        context = Mock()
        
        # Execute
        response = self.workflow_functions.useWorkflow(request, context)
        
        # Verify WorkflowVersion creation includes available_nodes
        add_calls = mock_db.add.call_args_list
        version_call = add_calls[1][0][0]  # Second call should be the version
        assert hasattr(version_call, 'available_nodes')
        assert version_call.available_nodes == self.sample_available_nodes


class TestAvailableNodesMarketplaceFunctions:
    """Test available_nodes field in WorkflowMarketplaceFunctions"""

    def setup_method(self):
        """Setup test fixtures"""
        self.marketplace_functions = WorkflowMarketplaceFunctions()
        self.mock_db = Mock(spec=Session)
        
        # Sample available_nodes data
        self.sample_available_nodes = [
            {
                "id": "marketplace_node_1",
                "type": "mcp_node",
                "name": "Marketplace MCP Node",
                "category": "data_processing"
            },
            {
                "id": "marketplace_node_2",
                "type": "component_node", 
                "name": "Marketplace Component Node",
                "category": "ai_processing"
            }
        ]

    @patch('app.services.marketplace_functions.SessionLocal')
    def test_marketplace_listing_to_protobuf_includes_available_nodes(self, mock_session):
        """Test that _marketplace_listing_to_protobuf includes available_nodes field"""
        # Setup mocks
        mock_db = Mock()
        mock_session.return_value = mock_db
        
        # Mock marketplace listing
        mock_listing = Mock(spec=WorkflowMarketplaceListing)
        mock_listing.id = "listing-id"
        mock_listing.title = "Test Marketplace Listing"
        mock_listing.description = "Test Description"
        mock_listing.workflow_version_id = "version-id"
        mock_listing.use_count = 5
        mock_listing.listed_by_user_id = "test-user"
        mock_listing.execution_count = 10
        mock_listing.category = "test"
        mock_listing.tags = ["test", "marketplace"]
        mock_listing.status = "active"
        mock_listing.created_at = None
        mock_listing.updated_at = None
        
        # Mock workflow version with available_nodes
        mock_version = Mock(spec=WorkflowVersion)
        mock_version.workflow_url = "https://test.url"
        mock_version.builder_url = "https://test-builder.url"
        mock_version.start_nodes = [{"id": "start"}]
        mock_version.available_nodes = self.sample_available_nodes
        mock_version.version_number = "1.0.0"
        
        # Setup query to return the version
        mock_db.query.return_value.filter.return_value.first.return_value = mock_version
        
        # Execute
        result = self.marketplace_functions._marketplace_listing_to_protobuf(mock_listing)
        
        # Verify available_nodes is included
        assert hasattr(result, 'available_nodes')
        assert len(result.available_nodes) == 2
        # Verify JSON serialization
        for i, node_json in enumerate(result.available_nodes):
            node_data = json.loads(node_json)
            assert node_data == self.sample_available_nodes[i]

    @patch('app.services.marketplace_functions.SessionLocal')
    def test_get_template_includes_available_nodes(self, mock_session):
        """Test that getTemplate includes available_nodes in response"""
        # Setup mocks
        mock_db = Mock()
        mock_session.return_value = mock_db
        
        # Mock marketplace listing
        mock_listing = Mock(spec=WorkflowMarketplaceListing)
        mock_listing.id = "listing-id"
        mock_listing.workflow_id = "workflow-id"
        mock_listing.workflow_version_id = "version-id"
        mock_listing.title = "Test Template"
        mock_listing.description = "Test Description"
        mock_listing.use_count = 3
        mock_listing.listed_by_user_id = "template-owner"
        mock_listing.execution_count = 7
        mock_listing.category = "test"
        mock_listing.tags = ["template"]
        mock_listing.status = "active"
        mock_listing.created_at = None
        mock_listing.updated_at = None
        
        # Mock workflow version with available_nodes
        mock_version = Mock(spec=WorkflowVersion)
        mock_version.workflow_url = "https://template.url"
        mock_version.builder_url = "https://template-builder.url"
        mock_version.start_nodes = [{"id": "template_start"}]
        mock_version.available_nodes = self.sample_available_nodes
        mock_version.version_number = "2.0.0"
        
        # Setup query chain
        query_calls = [mock_listing, mock_version, None]  # Third call for user workflows check
        mock_db.query.return_value.filter.return_value.first.side_effect = query_calls
        
        # Create request
        request = workflow_pb2.GetTemplateRequest()
        request.id = "listing-id"
        request.user_id = "test-user"
        
        context = Mock()
        
        # Execute
        response = self.marketplace_functions.getTemplate(request, context)
        
        # Verify response includes available_nodes
        assert response.success == True
        assert hasattr(response.template, 'available_nodes')
        assert len(response.template.available_nodes) == 2


class TestAvailableNodesIntegration:
    """Integration tests for available_nodes field across workflow and marketplace functions"""

    def setup_method(self):
        """Setup test fixtures"""
        self.sample_workflow_data = {
            "nodes": [
                {
                    "id": "node_1",
                    "type": "mcp_node",
                    "data": {"name": "Test MCP Node"}
                },
                {
                    "id": "node_2",
                    "type": "component_node", 
                    "data": {"name": "Test Component Node"}
                }
            ],
            "edges": []
        }
        
        self.expected_available_nodes = [
            {
                "id": "node_1",
                "type": "mcp_node",
                "name": "Test MCP Node",
                "category": "data_processing"
            },
            {
                "id": "node_2",
                "type": "component_node",
                "name": "Test Component Node", 
                "category": "ai_processing"
            }
        ]

    def test_available_nodes_consistency_across_services(self):
        """Test that available_nodes field is consistently handled across workflow and marketplace services"""
        # This test ensures that the available_nodes field structure is consistent
        # between workflow creation and marketplace listing conversion
        
        # Test data structure consistency
        workflow_functions = WorkflowFunctions()
        marketplace_functions = WorkflowMarketplaceFunctions()
        
        # Both services should handle the same available_nodes structure
        assert hasattr(workflow_functions, '_workflow_to_protobuf')
        assert hasattr(marketplace_functions, '_marketplace_listing_to_protobuf')
        
        # The field should be present in both protobuf conversion methods
        # This will be verified by the individual method tests above

    def test_available_nodes_database_schema_alignment(self):
        """Test that available_nodes field aligns with database schema"""
        # Verify that the Workflow and WorkflowVersion models have available_nodes field
        from app.models.workflow import Workflow, WorkflowVersion
        
        # Check Workflow model with required fields
        workflow = Workflow(
            name="Test Workflow",
            workflow_url="https://test.url",
            builder_url="https://test-builder.url",
            owner_id="test-user",
            owner_type="user"
        )
        assert hasattr(workflow, 'available_nodes')
        
        # Check WorkflowVersion model with required fields
        version = WorkflowVersion(
            workflow_id="test-workflow-id",
            name="Test Version",
            workflow_url="https://test.url",
            builder_url="https://test-builder.url"
        )
        assert hasattr(version, 'available_nodes')
        
        # Verify default values (when explicitly set, they should be empty lists)
        workflow.available_nodes = []
        version.available_nodes = []
        assert workflow.available_nodes == []
        assert version.available_nodes == []

        # Test setting values
        test_nodes = [{"id": "test", "type": "mcp"}]
        workflow.available_nodes = test_nodes
        version.available_nodes = test_nodes

        assert workflow.available_nodes == test_nodes
        assert version.available_nodes == test_nodes


def test_available_nodes_field_validation():
    """Test that available_nodes field accepts valid JSON data"""
    from app.models.workflow import Workflow, WorkflowVersion
    
    sample_nodes = [
        {"id": "test_node", "type": "mcp_node", "name": "Test Node"}
    ]
    
    # Test Workflow model
    workflow = Workflow(
        name="Test Workflow",
        workflow_url="https://test.url",
        builder_url="https://test-builder.url", 
        start_nodes=[],
        available_nodes=sample_nodes,
        owner_id="test-user",
        owner_type="user"
    )
    assert workflow.available_nodes == sample_nodes
    
    # Test WorkflowVersion model
    version = WorkflowVersion(
        workflow_id="test-workflow-id",
        version_number="1.0.0",
        name="Test Version",
        workflow_url="https://test.url",
        builder_url="https://test-builder.url",
        start_nodes=[],
        available_nodes=sample_nodes
    )
    assert version.available_nodes == sample_nodes


if __name__ == "__main__":
    pytest.main([__file__, "-v"])