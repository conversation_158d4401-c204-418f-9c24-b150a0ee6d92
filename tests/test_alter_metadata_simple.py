#!/usr/bin/env python3
"""
Simple test script for AlterMetadataComponent in workflow-service.
"""
import asyncio
import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.components.processing.alter_metadata import AlterMetadataComponent
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeStatus


async def test_workflow_alter_metadata():
    """Test the AlterMetadataComponent in workflow-service."""
    print("🧪 Testing AlterMetadataComponent (Workflow Service)")
    print("=" * 60)
    
    component = AlterMetadataComponent()
    
    # Test 1: Execute method with basic update
    print("\n📋 Test 1: Execute method - Basic metadata update")
    print("-" * 50)
    
    context = WorkflowContext(workflow_id="test_workflow", execution_id="test_execution")
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_metadata": {"name": "test", "version": "1.0"},
        "updates": {"version": "2.0", "author": "test_user"},
        "keys_to_remove": []
    }
    
    result = await component.execute(context)
    print(f"Status: {result.status}")
    print(f"Execution time: {result.execution_time}")
    
    if result.status == NodeStatus.SUCCESS:
        print(f"Output metadata: {result.outputs.get('output_metadata')}")
        expected = {"name": "test", "version": "2.0", "author": "test_user"}
        if result.outputs.get('output_metadata') == expected:
            print("✅ Test 1 PASSED")
        else:
            print(f"❌ Test 1 FAILED - Expected: {expected}")
    else:
        print(f"❌ Test 1 FAILED - Error: {result.error_message}")
    
    # Test 2: Execute method with key removal
    print("\n📋 Test 2: Execute method - Remove keys")
    print("-" * 50)
    
    context = WorkflowContext(workflow_id="test_workflow", execution_id="test_execution")
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_metadata": {"name": "test", "version": "1.0", "deprecated": True, "temp": "value"},
        "updates": {},
        "keys_to_remove": ["deprecated", "temp"]
    }
    
    result = await component.execute(context)
    print(f"Status: {result.status}")
    
    if result.status == NodeStatus.SUCCESS:
        print(f"Output metadata: {result.outputs.get('output_metadata')}")
        expected = {"name": "test", "version": "1.0"}
        if result.outputs.get('output_metadata') == expected:
            print("✅ Test 2 PASSED")
        else:
            print(f"❌ Test 2 FAILED - Expected: {expected}")
    else:
        print(f"❌ Test 2 FAILED - Error: {result.error_message}")
    
    # Test 3: Execute method with validation error
    print("\n📋 Test 3: Execute method - Validation error")
    print("-" * 50)
    
    context = WorkflowContext(workflow_id="test_workflow", execution_id="test_execution")
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_metadata": "not a dict",  # Invalid type
        "updates": {},
        "keys_to_remove": []
    }
    
    result = await component.execute(context)
    print(f"Status: {result.status}")
    
    if result.status == NodeStatus.ERROR:
        print(f"Error (expected): {result.error_message}")
        print("✅ Test 3 PASSED")
    else:
        print("❌ Test 3 FAILED - Expected error status")
    
    # Test 4: Legacy build method
    print("\n📋 Test 4: Legacy build method")
    print("-" * 50)
    
    kwargs = {
        "input_metadata": {"name": "test", "version": "1.0"},
        "updates": {"version": "2.0", "author": "test_user"},
        "keys_to_remove": []
    }
    
    result = component.build(**kwargs)
    print(f"Result keys: {list(result.keys())}")
    
    if "output_metadata" in result:
        print(f"Output metadata: {result['output_metadata']}")
        expected = {"name": "test", "version": "2.0", "author": "test_user"}
        if result["output_metadata"] == expected:
            print("✅ Test 4 PASSED")
        else:
            print(f"❌ Test 4 FAILED - Expected: {expected}")
    else:
        print(f"❌ Test 4 FAILED - No output_metadata in result: {result}")
    
    # Test 5: Component definition
    print("\n📋 Test 5: Component definition")
    print("-" * 50)
    
    definition = component.get_definition()
    print(f"Name: {definition['name']}")
    print(f"Display name: {definition['display_name']}")
    print(f"Category: {definition['category']}")
    print(f"Number of inputs: {len(definition['inputs'])}")
    print(f"Number of outputs: {len(definition['outputs'])}")
    
    # Check that all inputs are dual-purpose
    all_dual_purpose = all(inp['is_handle'] for inp in definition['inputs'])
    print(f"All inputs are dual-purpose: {all_dual_purpose}")
    
    if (definition['name'] == 'AlterMetadataComponent' and 
        len(definition['inputs']) == 3 and 
        len(definition['outputs']) == 2 and 
        all_dual_purpose):
        print("✅ Test 5 PASSED")
    else:
        print("❌ Test 5 FAILED - Component definition issues")
    
    print("\n🎯 All workflow service tests completed!")
    print(f"Context logs: {context.logs}")


if __name__ == "__main__":
    asyncio.run(test_workflow_alter_metadata())
