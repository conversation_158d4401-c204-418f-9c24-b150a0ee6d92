1.[ ] is_changes_marketplace field behavior
    I have a workflows table with a boolean field is_changes_marketplace. Here's the intended behavior:

    When a user creates a workflow by cloning it from a marketplace listing, the is_changes_marketplace field should be set to false. This indicates that the cloned workflow is in sync with the source.

    When the owner of the original (source) workflow updates it, the system should:

    Automatically update the corresponding marketplace listing.

    For all cloned workflows, compare the current state with the updated source.

    If changes are detected, set is_changes_marketplace = true for the cloned workflows.

    In the UI, we want to show a toggle or notification to the user of the cloned workflow saying:
    "The source workflow has new changes. You can pull the latest updates."

    After the user pulls the changes, reset is_changes_marketplace = false.

    Based on this logic, please:

    Update the relevant model or schema definitions if needed.

    Add or update database triggers or service layer functions to track changes from source workflows and update the is_changes_marketplace field accordingly.

    Suggest efficient ways to compare workflows (e.g., hashes, timestamps, versioning).

    Ensure that the UI or API exposes the proper flag (is_changes_marketplace) to show the update notification.

2.[ ] is_customizable field behavior
    I have a workflows table with a boolean field is_customizable. Here's the intended behavior:

    When a workflow is created, the is_customizable field should be set to true. This indicates that the workflow can be customized.

    when user can publish workflow to marketplace, the is_customizable field with is_customizable = true then only
    other users who cloned the workflow should be able to edit or make changes to it.
    
    if workflow is created from scratch and not cloned from marketplace then also is_customizable = false

    When a workflow is cloned from a marketplace listing, the is_customizable field should be set to false. This indicates that the workflow is a clone and cannot be customized.


3.[ ] auto_version_on_update field behavior
    I have a workflows table with a boolean field auto_version_on_update. Here's the intended behavior:

    When a workflow is created, only first version should created automatically and the auto_version_on_update field should be set to false. This indicates that the workflow does not auto-version on update.

    When a workflow is cloned from a marketplace listing, the auto_version_on_update field should be set to false. This indicates that the workflow does not auto-version on update.

    When a workflow is updated, if auto_version_on_update is false, the workflow should be updated in place without creating a new version.

    

    

