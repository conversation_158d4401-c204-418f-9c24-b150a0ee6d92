"""
Test cases for Switch Router component dual-purpose input handle behavior.

This module tests the specific scenarios for input handle visibility
based on configuration changes with the new dual-purpose input architecture.
"""

import pytest
from unittest.mock import Mock

from app.components.control_flow.conditionalNode import ConditionalNode


class TestSwitchRouterHandleBehavior:
    """Test the Switch Router component dual-purpose input handle behavior."""

    def setup_method(self):
        """Set up test fixtures."""
        self.node = ConditionalNode()

    def get_visible_input_handles(self, config):
        """
        Simulate frontend handle filtering logic to get visible input handles.
        
        Args:
            config: Configuration dictionary simulating node.data.config
            
        Returns:
            List of visible input handle names
        """
        visible_handles = []
        
        # Always include primary input (condition 1)
        primary_handle = next(
            (inp for inp in self.node.inputs if inp.name == "primary"),
            None
        )
        if primary_handle and primary_handle.is_handle:
            visible_handles.append("primary")

        # Check additional condition input handles (condition_2, condition_3, etc.)
        num_additional_conditions = int(config.get("num_additional_conditions", 0))
        total_conditions = 1 + num_additional_conditions  # Base 1 + additional

        for i in range(2, total_conditions + 1):
            handle_name = f"condition_{i}"
            condition_handle = next(
                (inp for inp in self.node.inputs if inp.name == handle_name),
                None
            )
            if condition_handle and condition_handle.is_handle:
                visible_handles.append(handle_name)
        
        return visible_handles

    def test_initial_state_default_configuration(self):
        """Test Initial State: Default configuration with 0 additional conditions."""
        config = {
            "num_additional_conditions": 0,
        }

        visible_handles = self.get_visible_input_handles(config)

        # Should display exactly 1 input handle (primary only)
        expected_handles = [
            "primary",  # Only condition 1 is visible with 0 additional conditions
        ]

        assert len(visible_handles) == 1, f"Expected 1 handle, got {len(visible_handles)}: {visible_handles}"
        assert set(visible_handles) == set(expected_handles), f"Expected {expected_handles}, got {visible_handles}"

    def test_adding_additional_conditions(self):
        """Test Adding Additional Conditions: 2 additional conditions (3 total)."""
        config = {
            "num_additional_conditions": 2,
        }

        visible_handles = self.get_visible_input_handles(config)

        # Should display exactly 3 input handles total
        expected_handles = [
            "primary",
            "condition_2",
            "condition_3"
        ]

        assert len(visible_handles) == 3, f"Expected 3 handles, got {len(visible_handles)}: {visible_handles}"
        assert set(visible_handles) == set(expected_handles), f"Expected {expected_handles}, got {visible_handles}"

    def test_additional_conditions_1(self):
        """Test with 1 additional condition (2 total conditions)."""
        config = {
            "num_additional_conditions": 1,
        }

        visible_handles = self.get_visible_input_handles(config)

        # Should display 2 input handles: primary + condition_2
        expected_handles = [
            "primary",
            "condition_2"
        ]

        assert len(visible_handles) == 2, f"Expected 2 handles, got {len(visible_handles)}: {visible_handles}"
        assert set(visible_handles) == set(expected_handles), f"Expected {expected_handles}, got {visible_handles}"

    def test_additional_conditions_5(self):
        """Test with 5 additional conditions (6 total conditions)."""
        config = {
            "num_additional_conditions": 5,
        }

        visible_handles = self.get_visible_input_handles(config)

        # Should display 6 input handles: primary + condition_2 through condition_6
        expected_handles = [
            "primary",
            "condition_2",
            "condition_3",
            "condition_4",
            "condition_5",
            "condition_6"
        ]

        assert len(visible_handles) == 6, f"Expected 6 handles, got {len(visible_handles)}: {visible_handles}"
        assert set(visible_handles) == set(expected_handles), f"Expected {expected_handles}, got {visible_handles}"

    def test_dynamic_condition_count_changes(self):
        """Test Dynamic Condition Count: Changing from 0 to 2 to 0 additional conditions."""
        # Start with 0 additional conditions
        config_0 = {"num_additional_conditions": 0}
        visible_handles_0 = self.get_visible_input_handles(config_0)

        expected_handles_0 = [
            "primary"
        ]
        assert set(visible_handles_0) == set(expected_handles_0)

        # Change to 2 additional conditions
        config_2 = {"num_additional_conditions": 2}
        visible_handles_2 = self.get_visible_input_handles(config_2)

        expected_handles_2 = [
            "primary",
            "condition_2",
            "condition_3"
        ]
        assert set(visible_handles_2) == set(expected_handles_2)

        # Change back to 0 additional conditions
        config_back_0 = {"num_additional_conditions": 0}
        visible_handles_back_0 = self.get_visible_input_handles(config_back_0)

        assert set(visible_handles_back_0) == set(expected_handles_0)

    def test_maximum_conditions(self):
        """Test Maximum Conditions: 9 additional conditions (10 total)."""
        config = {"num_additional_conditions": 9}
        visible_handles = self.get_visible_input_handles(config)

        # Should have 10 handles total (1 primary + 9 additional conditions)
        expected_handles = ["primary"] + [
            f"condition_{i}" for i in range(2, 11)
        ]

        assert len(visible_handles) == 10, f"Expected 10 handles, got {len(visible_handles)}: {visible_handles}"
        assert set(visible_handles) == set(expected_handles), f"Expected {expected_handles}, got {visible_handles}"


if __name__ == "__main__":
    pytest.main([__file__])
