#!/usr/bin/env python3
"""
Test script to reproduce the auto-versioning issue where latest changes
are not present in listVersions after updating a workflow with auto_version_on_update=True
"""

import sys
import os
import json
from datetime import datetime
import traceback

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

try:
    from app.services.workflow_functions import WorkflowFunctions
    from app.grpc_ import workflow_pb2
    from app.models.workflow import Workflow, WorkflowVersion
    from app.db.session import SessionLocal
    from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum, WorkflowOwnerTypeEnum
    from unittest.mock import Mock, patch
    print("✅ All imports successful")
except Exception as e:
    print(f"❌ Import failed: {str(e)}")
    traceback.print_exc()
    sys.exit(1)


def test_auto_versioning_issue():
    """Test to reproduce the auto-versioning issue"""
    
    print("=== Testing Auto-Versioning Issue ===")
    
    # Create a mock workflow with auto_version_on_update=True
    workflow_functions = WorkflowFunctions()
    
    # Mock database session
    db = SessionLocal()
    
    try:
        # Create a test workflow
        test_workflow = Workflow(
            id="test-workflow-123",
            name="Test Workflow",
            description="Original description",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start"}],
            owner_id="test-user-123",
            owner_type=WorkflowOwnerTypeEnum.USER,
            auto_version_on_update=True,  # This is the key setting
            visibility=WorkflowVisibilityEnum.PRIVATE,
            status=WorkflowStatusEnum.ACTIVE,
            category="automation",
            tags=["test"],
            version="1.0.0"
        )
        
        # Add to database
        db.add(test_workflow)
        db.flush()
        
        # Create initial version (v1.0.0)
        initial_version = WorkflowVersion(
            workflow_id=test_workflow.id,
            version_number="1.0.0",
            name=test_workflow.name,
            description=test_workflow.description,
            workflow_url=test_workflow.workflow_url,
            builder_url=test_workflow.builder_url,
            start_nodes=test_workflow.start_nodes,
            category=test_workflow.category,
            tags=test_workflow.tags,
            changelog="Initial version"
        )
        
        db.add(initial_version)
        db.flush()
        
        # Set current version
        test_workflow.current_version_id = initial_version.id
        db.commit()
        
        print(f"✅ Created test workflow: {test_workflow.id}")
        print(f"✅ Created initial version: {initial_version.version_number} (ID: {initial_version.id})")
        print(f"✅ Current version ID: {test_workflow.current_version_id}")
        
        # Now simulate updating the workflow with version-relevant changes
        print("\n=== Simulating Workflow Update ===")
        
        # Create update request
        update_request = workflow_pb2.UpdateWorkflowRequest()
        update_request.id = test_workflow.id
        update_request.name = "Updated Test Workflow"  # This should trigger version creation
        update_request.description = "Updated description"
        update_request.owner.id = "test-user-123"
        
        # Add fields to update mask
        update_request.update_mask.paths.append("name")
        update_request.update_mask.paths.append("description")
        
        context = Mock()
        
        # Mock the get_db method to return our test database session
        with patch.object(workflow_functions, 'get_db', return_value=db):
            # Call the update method
            response = workflow_functions.updateWorkflow(update_request, context)
            
            print(f"Update response: {response.success} - {response.message}")
            
            if response.success:
                # Refresh the workflow from database
                db.refresh(test_workflow)
                
                print(f"✅ Workflow updated successfully")
                print(f"✅ New current version ID: {test_workflow.current_version_id}")
                
                # Check if a new version was created
                versions = db.query(WorkflowVersion).filter(
                    WorkflowVersion.workflow_id == test_workflow.id
                ).order_by(WorkflowVersion.created_at.desc()).all()
                
                print(f"\n=== Versions Found ===")
                for i, version in enumerate(versions):
                    is_current = version.id == test_workflow.current_version_id
                    print(f"{i+1}. Version {version.version_number} (ID: {version.id}) - Current: {is_current}")
                    print(f"   Name: {version.name}")
                    print(f"   Description: {version.description}")
                    print(f"   Created: {version.created_at}")
                
                # Now test listVersions functionality
                print(f"\n=== Testing listVersions ===")
                
                list_request = workflow_pb2.ListWorkflowVersionsRequest()
                list_request.workflow_id = test_workflow.id
                list_request.user_id = "test-user-123"
                list_request.page = 1
                list_request.page_size = 10
                
                list_response = workflow_functions.listWorkflowVersions(list_request, context)
                
                print(f"List response: {list_response.success} - {list_response.message}")
                print(f"Total versions: {list_response.total}")
                print(f"Current version ID: {list_response.current_version_id}")
                
                print(f"\n=== Versions from listVersions ===")
                for i, version_proto in enumerate(list_response.versions):
                    print(f"{i+1}. Version {version_proto.version_number} (ID: {version_proto.id})")
                    print(f"   Name: {version_proto.name}")
                    print(f"   Description: {version_proto.description}")
                    print(f"   Is Current: {version_proto.is_current}")
                
                # Check if the latest changes are reflected
                if len(list_response.versions) >= 2:
                    latest_version = list_response.versions[0]  # Should be ordered by created_at desc
                    if latest_version.name == "Updated Test Workflow":
                        print("✅ Latest changes are present in listVersions")
                    else:
                        print("❌ Latest changes are NOT present in listVersions")
                        print(f"   Expected name: 'Updated Test Workflow'")
                        print(f"   Actual name: '{latest_version.name}'")
                else:
                    print("❌ Expected at least 2 versions, but found:", len(list_response.versions))
            else:
                print(f"❌ Update failed: {response.message}")
                
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up
        try:
            db.query(WorkflowVersion).filter(WorkflowVersion.workflow_id == "test-workflow-123").delete()
            db.query(Workflow).filter(Workflow.id == "test-workflow-123").delete()
            db.commit()
            print("\n✅ Cleanup completed")
        except:
            db.rollback()
        finally:
            db.close()


if __name__ == "__main__":
    test_auto_versioning_issue()
