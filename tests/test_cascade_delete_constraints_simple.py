"""
Test CASCADE DELETE constraints for workflow relationships using simplified models
"""
import pytest
from sqlalchemy.orm import sessionmaker, declarative_base, relationship
from sqlalchemy import create_engine, Column, String, ForeignKey, DateTime, Boolean, Text, Enum
from datetime import datetime
import uuid
from app.utils.constants.constants import (
    WorkflowOwnerTypeEnum,
    WorkflowVisibilityEnum,
    WorkflowStatusEnum,
    WorkflowCategoryEnum,
)

# Create simplified models for testing without ARRAY fields
TestBase = declarative_base()


class TestWorkflow(TestBase):
    __tablename__ = "test_workflows"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    workflow_url = Column(String, nullable=False)
    builder_url = Column(String, nullable=False)
    owner_id = Column(String, nullable=False)
    owner_type = Column(Enum(WorkflowOwnerTypeEnum), nullable=False)
    
    # Foreign key relationships with CASCADE DELETE behavior
    current_version_id = Column(String, ForeignKey("test_workflow_versions.id", ondelete="SET NULL"), nullable=True)
    workflow_template_id = Column(String, ForeignKey("test_workflows.id", ondelete="SET NULL"), nullable=True)
    source_version_id = Column(String, ForeignKey("test_workflow_versions.id", ondelete="SET NULL"), nullable=True)
    
    is_imported = Column(Boolean, default=False)
    visibility = Column(Enum(WorkflowVisibilityEnum), nullable=False, default=WorkflowVisibilityEnum.PRIVATE)
    status = Column(Enum(WorkflowStatusEnum), nullable=False, default=WorkflowStatusEnum.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    versions = relationship(
        "TestWorkflowVersion",
        back_populates="workflow",
        cascade="all, delete-orphan",
        foreign_keys="TestWorkflowVersion.workflow_id",
    )
    current_version = relationship(
        "TestWorkflowVersion", foreign_keys=[current_version_id], post_update=True
    )
    marketplace_listings = relationship(
        "TestWorkflowMarketplaceListing",
        back_populates="workflow",
        foreign_keys="TestWorkflowMarketplaceListing.workflow_id",
        cascade="all, delete-orphan",
        passive_deletes=True,
    )
    derived_workflows = relationship(
        "TestWorkflow",
        foreign_keys="TestWorkflow.workflow_template_id",
        remote_side="TestWorkflow.id",
        backref="source_workflow",
        passive_deletes=True,
    )
    source_version = relationship(
        "TestWorkflowVersion",
        foreign_keys=[source_version_id],
        post_update=True
    )


class TestWorkflowVersion(TestBase):
    __tablename__ = "test_workflow_versions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    workflow_id = Column(String, ForeignKey("test_workflows.id", ondelete="CASCADE"), nullable=False)
    version_number = Column(String(50), nullable=False, default="1.0.0")
    name = Column(String(255), nullable=False)
    workflow_url = Column(String, nullable=False)
    builder_url = Column(String, nullable=False)
    status = Column(Enum(WorkflowStatusEnum), nullable=False, default=WorkflowStatusEnum.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    workflow = relationship("TestWorkflow", back_populates="versions", foreign_keys=[workflow_id])
    marketplace_listings = relationship(
        "TestWorkflowMarketplaceListing",
        back_populates="workflow_version",
        cascade="all, delete-orphan",
        passive_deletes=True,
    )


class TestWorkflowMarketplaceListing(TestBase):
    __tablename__ = "test_workflow_marketplace_listings"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    workflow_version_id = Column(String, ForeignKey("test_workflow_versions.id", ondelete="CASCADE"), nullable=False)
    workflow_id = Column(String, ForeignKey("test_workflows.id", ondelete="CASCADE"), nullable=False)
    listed_by_user_id = Column(String, nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    visibility = Column(Enum(WorkflowVisibilityEnum), nullable=False, default=WorkflowVisibilityEnum.PUBLIC)
    status = Column(Enum(WorkflowStatusEnum), nullable=False, default=WorkflowStatusEnum.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    workflow_version = relationship("TestWorkflowVersion", back_populates="marketplace_listings")
    workflow = relationship("TestWorkflow", back_populates="marketplace_listings", foreign_keys=[workflow_id])


@pytest.fixture
def db_session():
    """Create an in-memory SQLite database for testing"""
    engine = create_engine("sqlite:///:memory:")
    TestBase.metadata.create_all(engine)
    Session = sessionmaker(bind=engine)
    session = Session()
    yield session
    session.close()


def test_delete_cloned_workflow_without_affecting_source(db_session):
    """
    Test that deleting a cloned workflow doesn't affect the source workflow
    """
    # Create source workflow
    source_workflow = TestWorkflow(
        name="Source Workflow",
        description="Original workflow",
        workflow_url="http://example.com/source",
        builder_url="http://example.com/source/builder",
        owner_id="source_owner",
        owner_type=WorkflowOwnerTypeEnum.USER,
        visibility=WorkflowVisibilityEnum.PUBLIC,
    )
    db_session.add(source_workflow)
    db_session.commit()
    
    # Create source version
    source_version = TestWorkflowVersion(
        workflow_id=source_workflow.id,
        name="Source Workflow",
        workflow_url="http://example.com/source",
        builder_url="http://example.com/source/builder",
        version_number="1.0.0",
    )
    db_session.add(source_version)
    db_session.commit()
    
    # Update source workflow to reference current version
    source_workflow.current_version_id = source_version.id
    db_session.commit()
    
    # Create cloned workflow
    cloned_workflow = TestWorkflow(
        name="Cloned Workflow",
        description="Cloned from source",
        workflow_url="http://example.com/cloned",
        builder_url="http://example.com/cloned/builder",
        owner_id="cloned_owner",
        owner_type=WorkflowOwnerTypeEnum.USER,
        is_imported=True,
        workflow_template_id=source_workflow.id,
        source_version_id=source_version.id,
        visibility=WorkflowVisibilityEnum.PRIVATE,
    )
    db_session.add(cloned_workflow)
    db_session.commit()
    
    # Create cloned version
    cloned_version = TestWorkflowVersion(
        workflow_id=cloned_workflow.id,
        name="Cloned Workflow",
        workflow_url="http://example.com/cloned",
        builder_url="http://example.com/cloned/builder",
        version_number="1.0.0",
    )
    db_session.add(cloned_version)
    db_session.commit()
    
    # Update cloned workflow to reference current version
    cloned_workflow.current_version_id = cloned_version.id
    db_session.commit()
    
    # Verify relationships are set up correctly
    assert cloned_workflow.workflow_template_id == source_workflow.id
    assert cloned_workflow.source_version_id == source_version.id
    assert len(source_workflow.derived_workflows) == 1
    assert source_workflow.derived_workflows[0].id == cloned_workflow.id
    
    # Delete the cloned workflow
    db_session.delete(cloned_workflow)
    db_session.commit()
    
    # Verify source workflow and version still exist
    remaining_source = db_session.query(TestWorkflow).filter_by(id=source_workflow.id).first()
    remaining_source_version = db_session.query(TestWorkflowVersion).filter_by(id=source_version.id).first()
    
    assert remaining_source is not None
    assert remaining_source_version is not None
    assert remaining_source.name == "Source Workflow"
    assert remaining_source_version.version_number == "1.0.0"
    
    # Verify cloned workflow is deleted
    deleted_cloned = db_session.query(TestWorkflow).filter_by(id=cloned_workflow.id).first()
    assert deleted_cloned is None


def test_delete_source_workflow_sets_references_to_null(db_session):
    """
    Test that deleting a source workflow sets workflow_template_id to NULL in cloned workflows
    """
    # Create source workflow
    source_workflow = TestWorkflow(
        name="Source Workflow",
        description="Original workflow",
        workflow_url="http://example.com/source",
        builder_url="http://example.com/source/builder",
        owner_id="source_owner",
        owner_type=WorkflowOwnerTypeEnum.USER,
        visibility=WorkflowVisibilityEnum.PUBLIC,
    )
    db_session.add(source_workflow)
    db_session.commit()
    
    # Create source version
    source_version = TestWorkflowVersion(
        workflow_id=source_workflow.id,
        name="Source Workflow",
        workflow_url="http://example.com/source",
        builder_url="http://example.com/source/builder",
        version_number="1.0.0",
    )
    db_session.add(source_version)
    db_session.commit()
    
    # Create cloned workflow
    cloned_workflow = TestWorkflow(
        name="Cloned Workflow",
        description="Cloned from source",
        workflow_url="http://example.com/cloned",
        builder_url="http://example.com/cloned/builder",
        owner_id="cloned_owner",
        owner_type=WorkflowOwnerTypeEnum.USER,
        is_imported=True,
        workflow_template_id=source_workflow.id,
        source_version_id=source_version.id,
        visibility=WorkflowVisibilityEnum.PRIVATE,
    )
    db_session.add(cloned_workflow)
    db_session.commit()
    
    # Store cloned workflow ID for later verification
    cloned_id = cloned_workflow.id
    
    # Verify relationships are set up
    assert cloned_workflow.workflow_template_id == source_workflow.id
    assert cloned_workflow.source_version_id == source_version.id
    
    # Delete the source workflow (this should cascade delete the source version too)
    db_session.delete(source_workflow)
    db_session.commit()
    
    # Refresh the cloned workflow from database
    cloned_workflow = db_session.query(TestWorkflow).filter_by(id=cloned_id).first()
    
    # Verify cloned workflow still exists but references are set to NULL
    assert cloned_workflow is not None
    assert cloned_workflow.workflow_template_id is None
    assert cloned_workflow.source_version_id is None
    assert cloned_workflow.name == "Cloned Workflow"  # Cloned workflow should still exist


def test_delete_version_sets_current_version_to_null(db_session):
    """
    Test that deleting a version sets current_version_id to NULL in workflows
    """
    # Create workflow
    workflow = TestWorkflow(
        name="Test Workflow",
        description="Test workflow",
        workflow_url="http://example.com/test",
        builder_url="http://example.com/test/builder",
        owner_id="test_owner",
        owner_type=WorkflowOwnerTypeEnum.USER,
        visibility=WorkflowVisibilityEnum.PRIVATE,
    )
    db_session.add(workflow)
    db_session.commit()
    
    # Create version
    version = TestWorkflowVersion(
        workflow_id=workflow.id,
        name="Test Workflow",
        workflow_url="http://example.com/test",
        builder_url="http://example.com/test/builder",
        version_number="1.0.0",
    )
    db_session.add(version)
    db_session.commit()
    
    # Set current version
    workflow.current_version_id = version.id
    db_session.commit()
    
    # Store workflow ID for later verification
    workflow_id = workflow.id
    
    # Verify current version is set
    assert workflow.current_version_id == version.id
    
    # Delete the version directly (not through workflow cascade)
    db_session.delete(version)
    db_session.commit()
    
    # Refresh workflow from database
    workflow = db_session.query(TestWorkflow).filter_by(id=workflow_id).first()
    
    # Verify current_version_id is set to NULL
    assert workflow is not None
    assert workflow.current_version_id is None
    assert workflow.name == "Test Workflow"  # Workflow should still exist


def test_cascade_delete_workflow_versions_and_listings(db_session):
    """
    Test that deleting a workflow cascades to delete its versions and marketplace listings
    """
    # Create workflow
    workflow = TestWorkflow(
        name="Test Workflow",
        description="Test workflow",
        workflow_url="http://example.com/test",
        builder_url="http://example.com/test/builder",
        owner_id="test_owner",
        owner_type=WorkflowOwnerTypeEnum.USER,
        visibility=WorkflowVisibilityEnum.PUBLIC,
    )
    db_session.add(workflow)
    db_session.commit()
    
    # Create multiple versions
    version1 = TestWorkflowVersion(
        workflow_id=workflow.id,
        name="Test Workflow",
        workflow_url="http://example.com/test",
        builder_url="http://example.com/test/builder",
        version_number="1.0.0",
    )
    version2 = TestWorkflowVersion(
        workflow_id=workflow.id,
        name="Test Workflow",
        workflow_url="http://example.com/test",
        builder_url="http://example.com/test/builder",
        version_number="2.0.0",
    )
    db_session.add_all([version1, version2])
    db_session.commit()
    
    # Create marketplace listings
    listing1 = TestWorkflowMarketplaceListing(
        workflow_id=workflow.id,
        workflow_version_id=version1.id,
        listed_by_user_id="test_user",
        title="Test Listing 1",
        description="Test listing for version 1",
    )
    listing2 = TestWorkflowMarketplaceListing(
        workflow_id=workflow.id,
        workflow_version_id=version2.id,
        listed_by_user_id="test_user",
        title="Test Listing 2",
        description="Test listing for version 2",
    )
    db_session.add_all([listing1, listing2])
    db_session.commit()
    
    # Store IDs for verification
    workflow_id = workflow.id
    
    # Verify everything exists
    assert len(workflow.versions) == 2
    assert len(workflow.marketplace_listings) == 2
    
    # Delete the workflow
    db_session.delete(workflow)
    db_session.commit()
    
    # Verify everything is cascade deleted
    remaining_versions = db_session.query(TestWorkflowVersion).filter_by(workflow_id=workflow_id).all()
    remaining_listings = db_session.query(TestWorkflowMarketplaceListing).filter_by(workflow_id=workflow_id).all()
    
    assert len(remaining_versions) == 0
    assert len(remaining_listings) == 0


if __name__ == "__main__":
    pytest.main([__file__])