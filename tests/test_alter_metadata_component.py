"""
Test cases for the AlterMetadataComponent in workflow-service.
"""
import pytest
import asyncio
from app.components.processing.alter_metadata import AlterMetadataComponent
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeStatus


@pytest.fixture
def alter_metadata_component():
    """Create an AlterMetadataComponent instance for testing."""
    return AlterMetadataComponent()


@pytest.fixture
def workflow_context():
    """Create a WorkflowContext instance for testing."""
    return WorkflowContext(workflow_id="test_workflow", execution_id="test_execution")


@pytest.mark.asyncio
async def test_alter_metadata_execute_basic_update(alter_metadata_component, workflow_context):
    """Test basic metadata update functionality using execute method."""
    # Set up context
    workflow_context.current_node_id = "test_node"
    workflow_context.node_outputs["test_node"] = {
        "input_metadata": {"name": "test", "version": "1.0"},
        "updates": {"version": "2.0", "author": "test_user"},
        "keys_to_remove": []
    }
    
    # Execute the component
    result = await alter_metadata_component.execute(workflow_context)
    
    # Check the result
    assert result.status == NodeStatus.SUCCESS
    assert "output_metadata" in result.outputs
    
    expected_metadata = {"name": "test", "version": "2.0", "author": "test_user"}
    assert result.outputs["output_metadata"] == expected_metadata
    assert result.execution_time is not None


@pytest.mark.asyncio
async def test_alter_metadata_execute_remove_keys(alter_metadata_component, workflow_context):
    """Test metadata key removal functionality using execute method."""
    # Set up context
    workflow_context.current_node_id = "test_node"
    workflow_context.node_outputs["test_node"] = {
        "input_metadata": {"name": "test", "version": "1.0", "deprecated": True, "temp": "value"},
        "updates": {},
        "keys_to_remove": ["deprecated", "temp"]
    }
    
    # Execute the component
    result = await alter_metadata_component.execute(workflow_context)
    
    # Check the result
    assert result.status == NodeStatus.SUCCESS
    assert "output_metadata" in result.outputs
    
    expected_metadata = {"name": "test", "version": "1.0"}
    assert result.outputs["output_metadata"] == expected_metadata


@pytest.mark.asyncio
async def test_alter_metadata_execute_combined_operations(alter_metadata_component, workflow_context):
    """Test combined update and removal functionality using execute method."""
    # Set up context
    workflow_context.current_node_id = "test_node"
    workflow_context.node_outputs["test_node"] = {
        "input_metadata": {"name": "test", "version": "1.0", "deprecated": True},
        "updates": {"version": "2.0", "author": "test_user"},
        "keys_to_remove": ["deprecated"]
    }
    
    # Execute the component
    result = await alter_metadata_component.execute(workflow_context)
    
    # Check the result
    assert result.status == NodeStatus.SUCCESS
    assert "output_metadata" in result.outputs
    
    expected_metadata = {"name": "test", "version": "2.0", "author": "test_user"}
    assert result.outputs["output_metadata"] == expected_metadata


@pytest.mark.asyncio
async def test_alter_metadata_execute_validation_errors(alter_metadata_component, workflow_context):
    """Test validation error cases using execute method."""
    
    # Test missing input_metadata
    workflow_context.current_node_id = "test_node"
    workflow_context.node_outputs["test_node"] = {
        "updates": {"version": "2.0"},
        "keys_to_remove": []
    }
    
    result = await alter_metadata_component.execute(workflow_context)
    assert result.status == NodeStatus.ERROR
    assert "missing" in result.error_message.lower()
    
    # Test invalid input_metadata type
    workflow_context.node_outputs["test_node"] = {
        "input_metadata": "not a dict",
        "updates": {},
        "keys_to_remove": []
    }
    
    result = await alter_metadata_component.execute(workflow_context)
    assert result.status == NodeStatus.ERROR
    assert "dictionary" in result.error_message.lower()
    
    # Test invalid updates type
    workflow_context.node_outputs["test_node"] = {
        "input_metadata": {"name": "test"},
        "updates": "not a dict",
        "keys_to_remove": []
    }
    
    result = await alter_metadata_component.execute(workflow_context)
    assert result.status == NodeStatus.ERROR
    assert "dictionary" in result.error_message.lower()
    
    # Test invalid keys_to_remove type
    workflow_context.node_outputs["test_node"] = {
        "input_metadata": {"name": "test"},
        "updates": {},
        "keys_to_remove": "not a list"
    }
    
    result = await alter_metadata_component.execute(workflow_context)
    assert result.status == NodeStatus.ERROR
    assert "list" in result.error_message.lower()


def test_alter_metadata_build_legacy_method(alter_metadata_component):
    """Test the legacy build method for backward compatibility."""
    # Test basic functionality
    kwargs = {
        "input_metadata": {"name": "test", "version": "1.0"},
        "updates": {"version": "2.0", "author": "test_user"},
        "keys_to_remove": []
    }
    
    result = alter_metadata_component.build(**kwargs)
    
    assert "output_metadata" in result
    expected_metadata = {"name": "test", "version": "2.0", "author": "test_user"}
    assert result["output_metadata"] == expected_metadata
    
    # Test with key removal
    kwargs = {
        "input_metadata": {"name": "test", "version": "1.0", "deprecated": True},
        "updates": {"version": "2.0"},
        "keys_to_remove": ["deprecated"]
    }
    
    result = alter_metadata_component.build(**kwargs)
    
    assert "output_metadata" in result
    expected_metadata = {"name": "test", "version": "2.0"}
    assert result["output_metadata"] == expected_metadata


def test_alter_metadata_build_validation_errors(alter_metadata_component):
    """Test validation errors in the legacy build method."""
    
    # Test missing input_metadata
    kwargs = {
        "updates": {"version": "2.0"},
        "keys_to_remove": []
    }
    
    result = alter_metadata_component.build(**kwargs)
    assert "error" in result
    assert "missing" in result["error"].lower()
    
    # Test invalid input_metadata type
    kwargs = {
        "input_metadata": "not a dict",
        "updates": {},
        "keys_to_remove": []
    }
    
    result = alter_metadata_component.build(**kwargs)
    assert "error" in result
    assert "dictionary" in result["error"].lower()


def test_alter_metadata_component_definition():
    """Test that the component definition is properly configured."""
    definition = AlterMetadataComponent.get_definition()
    
    assert definition is not None
    assert definition["name"] == "AlterMetadataComponent"
    assert definition["display_name"] == "Alter Metadata"
    assert definition["description"] == "Modifies metadata dictionary keys."
    assert definition["category"] == "Processing"
    assert definition["icon"] == "Tag"
    
    # Check inputs
    assert len(definition["inputs"]) == 3
    input_names = [inp["name"] for inp in definition["inputs"]]
    assert "input_metadata" in input_names
    assert "updates" in input_names
    assert "keys_to_remove" in input_names
    
    # Check that all inputs are dual-purpose (have is_handle=True)
    for inp in definition["inputs"]:
        assert inp["is_handle"] == True
    
    # Check outputs
    assert len(definition["outputs"]) == 2
    output_names = [out["name"] for out in definition["outputs"]]
    assert "output_metadata" in output_names
    assert "error" in output_names


if __name__ == "__main__":
    # Run a simple test
    async def run_test():
        component = AlterMetadataComponent()
        context = WorkflowContext(workflow_id="test", execution_id="test")
        
        # Set up test data
        context.current_node_id = "test_node"
        context.node_outputs["test_node"] = {
            "input_metadata": {"name": "test", "version": "1.0", "deprecated": True},
            "updates": {"version": "2.0", "author": "test_user"},
            "keys_to_remove": ["deprecated"]
        }
        
        # Execute
        result = await component.execute(context)
        print(f"Status: {result.status}")
        print(f"Outputs: {result.outputs}")
        print(f"Execution time: {result.execution_time}")
        print(f"Logs: {context.logs}")
    
    asyncio.run(run_test())
