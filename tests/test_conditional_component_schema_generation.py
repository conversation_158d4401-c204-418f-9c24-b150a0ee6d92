"""
Test suite for conditional component schema generation in workflow service.

This module tests the component-based conditional routing functionality:
- Generation of component-based conditional routing format
- Feature flag control for routing mode selection
- Backward compatibility with embedded routing
- Handle management for conditional components

Following TDD methodology - Phase 3 Cycle 1: Component-Based Schema Generation
"""

import pytest
import os
from unittest.mock import patch
from typing import Dict, Any

from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
    create_conditional_component_routing,
    should_use_component_based_routing
)


class TestConditionalComponentSchemaGeneration:
    """Test suite for component-based conditional routing schema generation."""
    
    @pytest.fixture
    def sample_conditional_workflow(self):
        """Fixture providing a sample workflow with conditional node."""
        return {
            "nodes": [
                {
                    "id": "start-1",
                    "data": {
                        "originalType": "StartNode",
                        "definition": {"name": "StartNode"}
                    }
                },
                {
                    "id": "processor-1", 
                    "data": {
                        "type": "component",
                        "definition": {"name": "data_processor"}
                    }
                },
                {
                    "id": "conditional-1",
                    "data": {
                        "originalType": "ConditionalNode",
                        "definition": {"name": "ConditionalNode"},
                        "config": {
                            "num_conditions": 2,
                            "condition_1_operator": "equals",
                            "condition_1_expected_value": "success",
                            "condition_1_source": "node_output",
                            "condition_2_operator": "contains", 
                            "condition_2_expected_value": "error",
                            "condition_2_source": "node_output"
                        }
                    }
                },
                {
                    "id": "success-node",
                    "data": {
                        "type": "component", 
                        "definition": {"name": "success_handler"}
                    }
                },
                {
                    "id": "error-node",
                    "data": {
                        "type": "component",
                        "definition": {"name": "error_handler"}
                    }
                },
                {
                    "id": "default-node",
                    "data": {
                        "type": "component",
                        "definition": {"name": "default_handler"}
                    }
                }
            ],
            "edges": [
                {"source": "start-1", "target": "processor-1"},
                {"source": "processor-1", "target": "conditional-1"},
                {"source": "conditional-1", "target": "success-node", "sourceHandle": "condition_1_output"},
                {"source": "conditional-1", "target": "error-node", "sourceHandle": "condition_2_output"},
                {"source": "conditional-1", "target": "default-node", "sourceHandle": "default_output"}
            ],
            "mcp_configs": []
        }
    
    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_generates_component_based_conditional_routing(self, sample_conditional_workflow):
        """
        Test generation of component-based conditional routing format.

        Expected to FAIL initially until component-based routing is implemented.
        """
        import time
        start_time = time.time()

        result = convert_workflow_to_transition_schema(sample_conditional_workflow)

        end_time = time.time()
        conversion_time_ms = (end_time - start_time) * 1000

        # Performance target: <50ms schema conversion
        assert conversion_time_ms < 50, f"Schema conversion took {conversion_time_ms:.2f}ms, should be <50ms"

        # Should generate separate conditional transition in component mode
        conditional_transition = next(
            (t for t in result["transitions"] if "conditional-1" in t["id"]),
            None
        )

        assert conditional_transition is not None, "Conditional transition should exist in component mode"
        assert conditional_transition["execution_type"] == "Components"

        # Verify conditional component is in the conditional transition
        tools = conditional_transition["node_info"]["tools_to_use"]
        conditional_tool = next((tool for tool in tools if tool["tool_name"] == "conditional"), None)
        assert conditional_tool is not None, "Conditional tool should be present in conditional transition"
        assert conditional_tool["server_id"] == "node-executor-service"

        # Verify processor transition exists and has no conditional logic
        processor_transition = next(
            (t for t in result["transitions"] if "processor-1" in t["id"]),
            None
        )
        assert processor_transition is not None, "Processor transition should exist"
        assert "conditional_routing" not in processor_transition, "Processor should not have embedded conditional_routing in component mode"

        # Processor should only have its own tool, not conditional component
        processor_tools = processor_transition["node_info"]["tools_to_use"]
        processor_conditional_tools = [tool for tool in processor_tools if tool["tool_name"] == "conditional"]
        assert len(processor_conditional_tools) == 0, "Processor should not have conditional component in separate transition mode"

        # Verify conditional component parameters (using tool_params format)
        tool_params = conditional_tool["tool_params"]
        assert "items" in tool_params

        # Extract conditions and default_transition from items
        conditions = None
        default_transition = None
        for item in tool_params["items"]:
            if item["field_name"] == "conditions":
                conditions = item["field_value"]
            elif item["field_name"] == "default_transition":
                default_transition = item["field_value"]

        assert conditions is not None, "Should have conditions in tool_params"
        assert len(conditions) == 2
        assert default_transition is not None, "Should have default_transition in tool_params"

        # Verify first condition
        condition1 = conditions[0]
        assert condition1["operator"] == "equals"
        assert condition1["expected_value"] == "success"
        assert condition1["source"] == "node_output"
        assert condition1["next_transition"] == "transition-success-node"

        # Verify second condition
        condition2 = conditions[1]
        assert condition2["operator"] == "contains"
        assert condition2["expected_value"] == "error"
        assert condition2["source"] == "node_output"
        assert condition2["next_transition"] == "transition-error-node"

        # Verify default transition
        assert default_transition == "transition-default-node"
    
    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "embedded"})
    def test_maintains_embedded_routing_when_flag_disabled(self, sample_conditional_workflow):
        """
        Test that embedded routing is maintained when feature flag is disabled.
        
        Expected to FAIL initially until feature flag logic is implemented.
        """
        result = convert_workflow_to_transition_schema(sample_conditional_workflow)
        
        # Should generate embedded conditional routing
        processor_transition = next(
            (t for t in result["transitions"] if "processor-1" in t["id"]),
            None
        )
        
        assert processor_transition is not None, "Processor transition should exist"
        assert "conditional_routing" in processor_transition
        assert "cases" in processor_transition["conditional_routing"]
        assert len(processor_transition["conditional_routing"]["cases"]) == 2
        
        # Should NOT have conditional component in tools
        tools = processor_transition["node_info"]["tools_to_use"]
        conditional_tools = [tool for tool in tools if tool["tool_name"] == "conditional"]
        assert len(conditional_tools) == 0, "Should not have conditional component in embedded mode"
    
    def test_feature_flag_function_detects_component_mode(self):
        """
        Test that feature flag function correctly detects component mode.
        
        Expected to FAIL initially until feature flag function is implemented.
        """
        with patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"}):
            assert should_use_component_based_routing() is True
        
        with patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "embedded"}):
            assert should_use_component_based_routing() is False
        
        with patch.dict(os.environ, {}, clear=True):
            # Default is now component mode since the feature is complete and enabled in production
            # The .env file has CONDITIONAL_ROUTING_MODE=component set
            assert should_use_component_based_routing() is True
    
    def test_create_conditional_component_routing_function(self):
        """
        Test the create_conditional_component_routing function directly.
        
        Expected to FAIL initially until function is implemented.
        """
        conditional_node = {
            "id": "conditional-test",
            "data": {
                "config": {
                    "num_conditions": 2,
                    "condition_1_operator": "equals",
                    "condition_1_expected_value": "approved",
                    "condition_1_source": "node_output",
                    "condition_2_operator": "greater_than",
                    "condition_2_expected_value": "100",
                    "condition_2_source": "global_context",
                    "condition_2_variable": "order_total"
                }
            }
        }
        
        edges = [
            {"source": "conditional-test", "target": "approval-flow", "sourceHandle": "condition_1_output"},
            {"source": "conditional-test", "target": "high-value-flow", "sourceHandle": "condition_2_output"},
            {"source": "conditional-test", "target": "standard-flow", "sourceHandle": "default_output"}
        ]
        
        result = create_conditional_component_routing(conditional_node, edges)
        
        assert result["tool_name"] == "conditional"
        assert result["server_id"] == "node-executor-service"

        # Extract parameters from tool_params format
        tool_params = result["tool_params"]
        conditions = None
        default_transition = None
        for item in tool_params["items"]:
            if item["field_name"] == "conditions":
                conditions = item["field_value"]
            elif item["field_name"] == "default_transition":
                default_transition = item["field_value"]

        assert conditions is not None
        assert len(conditions) == 2

        # Test first condition
        condition1 = conditions[0]
        assert condition1["operator"] == "equals"
        assert condition1["expected_value"] == "approved"
        assert condition1["source"] == "node_output"
        assert condition1["next_transition"] == "transition-approval-flow"

        # Test second condition with global context
        condition2 = conditions[1]
        assert condition2["operator"] == "greater_than"
        assert condition2["expected_value"] == "100"
        assert condition2["source"] == "global_context"
        assert condition2["variable_name"] == "order_total"
        assert condition2["next_transition"] == "transition-high-value-flow"

        # Test default transition
        assert default_transition == "transition-standard-flow"
    
    @patch.dict(os.environ, {"CONDITIONAL_ROUTING_MODE": "component"})
    def test_component_mode_removes_conditional_routing_field(self, sample_conditional_workflow):
        """
        Test that component mode does not include conditional_routing field in processor transitions.

        In separate transition mode, conditional logic is in dedicated conditional transitions.
        """
        result = convert_workflow_to_transition_schema(sample_conditional_workflow)

        processor_transition = next(
            (t for t in result["transitions"] if "processor-1" in t["id"]),
            None
        )

        assert processor_transition is not None
        assert "conditional_routing" not in processor_transition, \
            "Component mode should not include conditional_routing field in processor transitions"

        # Verify conditional logic is in separate conditional transition
        conditional_transition = next(
            (t for t in result["transitions"] if "conditional-1" in t["id"]),
            None
        )

        assert conditional_transition is not None, "Should have separate conditional transition"
        tools = conditional_transition["node_info"]["tools_to_use"]
        conditional_tool = next((tool for tool in tools if tool["tool_name"] == "conditional"), None)
        assert conditional_tool is not None, "Conditional transition should have conditional component"
    
    def test_global_context_variable_handling(self):
        """
        Test proper handling of global context variables in component format.
        
        Expected to FAIL initially until global context handling is implemented.
        """
        conditional_node = {
            "id": "conditional-global",
            "data": {
                "config": {
                    "num_conditions": 1,
                    "condition_1_operator": "equals",
                    "condition_1_expected_value": "premium",
                    "condition_1_source": "global_context",
                    "condition_1_variable": "subscription_type"
                }
            }
        }
        
        edges = [
            {"source": "conditional-global", "target": "premium-flow", "sourceHandle": "condition_1_output"},
            {"source": "conditional-global", "target": "basic-flow", "sourceHandle": "default_output"}
        ]
        
        result = create_conditional_component_routing(conditional_node, edges)

        # Extract conditions from tool_params format
        tool_params = result["tool_params"]
        conditions = None
        for item in tool_params["items"]:
            if item["field_name"] == "conditions":
                conditions = item["field_value"]
                break

        assert conditions is not None
        condition = conditions[0]
        assert condition["source"] == "global_context"
        assert condition["variable_name"] == "subscription_type"
        assert condition["expected_value"] == "premium"
        assert condition["operator"] == "equals"
    
    def test_handles_missing_configuration_gracefully(self):
        """
        Test graceful handling of missing or incomplete configuration.
        
        Expected to FAIL initially until error handling is implemented.
        """
        # Test with minimal configuration
        minimal_node = {
            "id": "conditional-minimal",
            "data": {
                "config": {
                    "num_conditions": 1
                    # Missing condition configuration
                }
            }
        }
        
        edges = [
            {"source": "conditional-minimal", "target": "default-flow", "sourceHandle": "default_output"}
        ]
        
        result = create_conditional_component_routing(minimal_node, edges)

        # Should handle gracefully with empty conditions
        assert result["tool_name"] == "conditional"

        # Extract from tool_params format
        tool_params = result["tool_params"]
        conditions = None
        default_transition = None
        for item in tool_params["items"]:
            if item["field_name"] == "conditions":
                conditions = item["field_value"]
            elif item["field_name"] == "default_transition":
                default_transition = item["field_value"]

        assert conditions is not None
        assert len(conditions) == 0
        assert default_transition == "transition-default-flow"
    
    def test_handles_edge_cases_in_condition_numbering(self):
        """
        Test handling of edge cases in condition numbering and configuration.
        
        Expected to FAIL initially until edge case handling is implemented.
        """
        edge_case_node = {
            "id": "conditional-edge",
            "data": {
                "config": {
                    "num_conditions": 3,
                    # Only configure condition 1 and 3, skip 2
                    "condition_1_operator": "equals",
                    "condition_1_expected_value": "test1",
                    "condition_1_source": "node_output",
                    "condition_3_operator": "contains",
                    "condition_3_expected_value": "test3",
                    "condition_3_source": "node_output"
                }
            }
        }
        
        edges = [
            {"source": "conditional-edge", "target": "flow1", "sourceHandle": "condition_1_output"},
            {"source": "conditional-edge", "target": "flow3", "sourceHandle": "condition_3_output"},
            {"source": "conditional-edge", "target": "default-flow", "sourceHandle": "default_output"}
        ]
        
        result = create_conditional_component_routing(edge_case_node, edges)

        # Extract from tool_params format
        tool_params = result["tool_params"]
        conditions = None
        for item in tool_params["items"]:
            if item["field_name"] == "conditions":
                conditions = item["field_value"]
                break

        # Should only include configured conditions
        assert conditions is not None
        assert len(conditions) == 2

        # Verify conditions are properly mapped
        condition_values = [c["expected_value"] for c in conditions]
        assert "test1" in condition_values
        assert "test3" in condition_values


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
