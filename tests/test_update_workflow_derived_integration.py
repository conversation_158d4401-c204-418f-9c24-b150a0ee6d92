import pytest
import json
from datetime import datetime, timezone
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session
from google.protobuf.field_mask_pb2 import FieldMask
from app.services.workflow_functions import WorkflowFunctions
from app.models.workflow import Workflow, WorkflowVersion
from app.grpc_ import workflow_pb2
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum


class TestUpdateWorkflowDerivedIntegration:
    """Test the integration between updateWorkflow and _update_derived_workflows_change_status"""

    @pytest.fixture
    def workflow_service(self):
        return WorkflowFunctions()

    @pytest.fixture
    def mock_db_session(self):
        return Mock(spec=Session)

    @pytest.fixture
    def source_workflow(self):
        """Create a source workflow that is public and can be cloned"""
        return Workflow(
            id="source-workflow-123",
            name="Source Workflow",
            description="Original workflow",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start"}],
            available_nodes=[{"id": "node1", "type": "action"}],
            owner_id="owner-123",
            user_ids=["owner-123"],
            owner_type="user",
            visibility=WorkflowVisibilityEnum.PUBLIC,
            status=WorkflowStatusEnum.ACTIVE,
            current_version_id="version-123",
            workflow_template_id=None,
            template_owner_id=None,
            is_imported=False,
            is_changes_marketplace=True,
            is_customizable=True,
            auto_version_on_update=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )

    @pytest.fixture
    def derived_workflow(self, source_workflow):
        """Create a derived workflow that was cloned from the source"""
        return Workflow(
            id="derived-workflow-456",
            name="Cloned Workflow",
            description="Cloned from source",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start"}],
            available_nodes=[{"id": "node1", "type": "action"}],
            owner_id="user-456",
            user_ids=["user-456"],
            owner_type="user",
            visibility=WorkflowVisibilityEnum.PRIVATE,
            status=WorkflowStatusEnum.ACTIVE,
            current_version_id="derived-version-456",
            workflow_template_id=source_workflow.id,
            template_owner_id=source_workflow.owner_id,
            is_imported=True,
            is_changes_marketplace=False,  # Initially in sync
            is_customizable=True,
            auto_version_on_update=False,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )

    @patch('app.services.workflow_functions.GCSUploadService')
    @patch('app.services.workflow_functions.convert_workflow_to_transition_schema')
    @patch('app.services.workflow_functions.validate_transition_schema')
    def test_update_workflow_name_triggers_derived_update(
        self,
        mock_validate,
        mock_convert,
        mock_gcs,
        workflow_service,
        mock_db_session,
        source_workflow,
        derived_workflow,
    ):
        """Test that updating a workflow name triggers derived workflow updates"""
        
        # Setup mocks
        mock_db_session.query.return_value.filter.return_value.first.return_value = source_workflow
        mock_db_session.query.return_value.filter.return_value.all.return_value = [derived_workflow]
        mock_db_session.is_active = True
        
        # Mock the workflow service's get_db method
        workflow_service.get_db = Mock(return_value=mock_db_session)
        
        # Create update request for name change
        update_mask = FieldMask(paths=["name"])
        owner = workflow_pb2.Owner(id="owner-123")
        
        request = workflow_pb2.UpdateWorkflowRequest(
            id=source_workflow.id,
            name="Updated Source Workflow Name",
            update_mask=update_mask,
            owner=owner,
        )
        
        context = Mock()
        
        # Execute the update
        response = workflow_service.updateWorkflow(request, context)
        
        # Verify the response is successful
        assert response.success is True
        assert "updated successfully" in response.message
        
        # Verify that the source workflow name was updated
        assert source_workflow.name == "Updated Source Workflow Name"
        
        # Verify that the derived workflow's is_changes_marketplace flag was set to True
        assert derived_workflow.is_changes_marketplace is True
        
        # Verify database operations
        mock_db_session.add.assert_called()
        mock_db_session.commit.assert_called()
        mock_db_session.refresh.assert_called()

    @patch('app.services.workflow_functions.GCSUploadService')
    @patch('app.services.workflow_functions.convert_workflow_to_transition_schema')
    @patch('app.services.workflow_functions.validate_transition_schema')
    def test_update_workflow_description_triggers_derived_update(
        self,
        mock_validate,
        mock_convert,
        mock_gcs,
        workflow_service,
        mock_db_session,
        source_workflow,
        derived_workflow,
    ):
        """Test that updating a workflow description triggers derived workflow updates"""
        
        # Setup mocks
        mock_db_session.query.return_value.filter.return_value.first.return_value = source_workflow
        mock_db_session.query.return_value.filter.return_value.all.return_value = [derived_workflow]
        mock_db_session.is_active = True
        
        # Mock the workflow service's get_db method
        workflow_service.get_db = Mock(return_value=mock_db_session)
        
        # Create update request for description change
        update_mask = FieldMask(paths=["description"])
        owner = workflow_pb2.Owner(id="owner-123")
        
        request = workflow_pb2.UpdateWorkflowRequest(
            id=source_workflow.id,
            description="Updated description for the source workflow",
            update_mask=update_mask,
            owner=owner,
        )
        
        context = Mock()
        
        # Execute the update
        response = workflow_service.updateWorkflow(request, context)
        
        # Verify the response is successful
        assert response.success is True
        assert "updated successfully" in response.message
        
        # Verify that the source workflow description was updated
        assert source_workflow.description == "Updated description for the source workflow"
        
        # Verify that the derived workflow's is_changes_marketplace flag was set to True
        assert derived_workflow.is_changes_marketplace is True
        
        # Verify database operations
        mock_db_session.add.assert_called()
        mock_db_session.commit.assert_called()
        mock_db_session.refresh.assert_called()

    @patch('app.services.workflow_functions.GCSUploadService')
    @patch('app.services.workflow_functions.convert_workflow_to_transition_schema')
    @patch('app.services.workflow_functions.validate_transition_schema')
    def test_update_private_workflow_does_not_trigger_derived_update(
        self,
        mock_validate,
        mock_convert,
        mock_gcs,
        workflow_service,
        mock_db_session,
        source_workflow,
        derived_workflow,
    ):
        """Test that updating a private workflow does not trigger derived workflow updates"""
        
        # Make the source workflow private
        source_workflow.visibility = WorkflowVisibilityEnum.PRIVATE
        
        # Setup mocks
        mock_db_session.query.return_value.filter.return_value.first.return_value = source_workflow
        mock_db_session.query.return_value.filter.return_value.all.return_value = [derived_workflow]
        mock_db_session.is_active = True
        
        # Mock the workflow service's get_db method
        workflow_service.get_db = Mock(return_value=mock_db_session)
        
        # Create update request for name change
        update_mask = FieldMask(paths=["name"])
        owner = workflow_pb2.Owner(id="owner-123")
        
        request = workflow_pb2.UpdateWorkflowRequest(
            id=source_workflow.id,
            name="Updated Private Workflow Name",
            update_mask=update_mask,
            owner=owner,
        )
        
        context = Mock()
        
        # Execute the update
        response = workflow_service.updateWorkflow(request, context)
        
        # Verify the response is successful
        assert response.success is True
        assert "updated successfully" in response.message
        
        # Verify that the source workflow name was updated
        assert source_workflow.name == "Updated Private Workflow Name"
        
        # Verify that the derived workflow's is_changes_marketplace flag was NOT changed
        assert derived_workflow.is_changes_marketplace is False
        
        # Verify database operations
        mock_db_session.add.assert_called()
        mock_db_session.commit.assert_called()
        mock_db_session.refresh.assert_called()

    @patch('app.services.workflow_functions.GCSUploadService')
    @patch('app.services.workflow_functions.convert_workflow_to_transition_schema')
    @patch('app.services.workflow_functions.validate_transition_schema')
    def test_update_non_template_relevant_field_does_not_trigger_derived_update(
        self,
        mock_validate,
        mock_convert,
        mock_gcs,
        workflow_service,
        mock_db_session,
        source_workflow,
        derived_workflow,
    ):
        """Test that updating non-template-relevant fields does not trigger derived workflow updates"""
        
        # Setup mocks
        mock_db_session.query.return_value.filter.return_value.first.return_value = source_workflow
        mock_db_session.query.return_value.filter.return_value.all.return_value = [derived_workflow]
        mock_db_session.is_active = True
        
        # Mock the workflow service's get_db method
        workflow_service.get_db = Mock(return_value=mock_db_session)
        
        # Create update request for auto_version_on_update change (non-template-relevant)
        update_mask = FieldMask(paths=["auto_version_on_update"])
        owner = workflow_pb2.Owner(id="owner-123")
        
        request = workflow_pb2.UpdateWorkflowRequest(
            id=source_workflow.id,
            auto_version_on_update=False,
            update_mask=update_mask,
            owner=owner,
        )
        
        context = Mock()
        
        # Execute the update
        response = workflow_service.updateWorkflow(request, context)
        
        # Verify the response is successful
        assert response.success is True
        assert "updated successfully" in response.message
        
        # Verify that the source workflow auto_version_on_update was updated
        assert source_workflow.auto_version_on_update is False
        
        # Verify that the derived workflow's is_changes_marketplace flag was NOT changed
        assert derived_workflow.is_changes_marketplace is False
        
        # Verify database operations
        mock_db_session.add.assert_called()
        mock_db_session.commit.assert_called()
        mock_db_session.refresh.assert_called()

    def test_update_derived_workflows_change_status_with_multiple_derived_workflows(
        self,
        workflow_service,
        mock_db_session,
        source_workflow,
    ):
        """Test that multiple derived workflows are updated correctly"""
        
        # Create multiple derived workflows
        derived_workflow_1 = Workflow(
            id="derived-1",
            name="Derived 1",
            workflow_template_id=source_workflow.id,
            is_imported=True,
            is_changes_marketplace=False,
            updated_at=datetime.now(timezone.utc),
        )
        
        derived_workflow_2 = Workflow(
            id="derived-2",
            name="Derived 2",
            workflow_template_id=source_workflow.id,
            is_imported=True,
            is_changes_marketplace=False,
            updated_at=datetime.now(timezone.utc),
        )
        
        # Setup mock to return multiple derived workflows
        mock_db_session.query.return_value.filter.return_value.all.return_value = [
            derived_workflow_1,
            derived_workflow_2,
        ]
        
        # Execute the method
        workflow_service._update_derived_workflows_change_status(mock_db_session, source_workflow)
        
        # Verify both derived workflows were updated
        assert derived_workflow_1.is_changes_marketplace is True
        assert derived_workflow_2.is_changes_marketplace is True
        
        # Verify database operations
        mock_db_session.commit.assert_called_once()

    def test_update_derived_workflows_change_status_no_derived_workflows(
        self,
        workflow_service,
        mock_db_session,
        source_workflow,
    ):
        """Test that the method handles the case where no derived workflows exist"""
        
        # Setup mock to return no derived workflows
        mock_db_session.query.return_value.filter.return_value.all.return_value = []
        
        # Execute the method - should not raise any exceptions
        workflow_service._update_derived_workflows_change_status(mock_db_session, source_workflow)
        
        # Verify database operations
        mock_db_session.commit.assert_called_once()

    def test_update_derived_workflows_change_status_handles_exceptions(
        self,
        workflow_service,
        mock_db_session,
        source_workflow,
    ):
        """Test that the method handles database exceptions gracefully"""
        
        # Setup mock to raise an exception
        mock_db_session.query.side_effect = Exception("Database error")
        
        # Execute the method - should not raise exceptions but should log error
        workflow_service._update_derived_workflows_change_status(mock_db_session, source_workflow)
        
        # Verify rollback was called
        mock_db_session.rollback.assert_called_once()