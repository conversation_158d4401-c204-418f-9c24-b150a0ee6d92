#!/usr/bin/env python3
"""
Test for Smart Field Search enhancement in SelectDataComponent.

This test follows Test-Driven Development principles to ensure robust implementation
of the smart search functionality for dynamic JSON structures.
"""

import asyncio
import sys
import os
import pytest

# Add the app directory to sys.path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), "app"))

# Import the components
try:
    from components.processing.select_data import SelectDataComponent
    from models.workflow_builder.context import WorkflowContext
    print("✓ Successfully imported workflow-service components")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


class TestSelectDataSmartSearch:
    """Test class for Smart Field Search functionality."""

    @pytest.fixture
    def component(self):
        """Create a SelectDataComponent instance for testing."""
        return SelectDataComponent()

    @pytest.fixture
    def sample_nested_data(self):
        """Sample nested JSON data for testing smart search."""
        return {
            "user": {
                "profile": {
                    "email": "<EMAIL>",
                    "name": "<PERSON>"
                },
                "settings": {
                    "theme": "dark",
                    "notifications": True
                }
            },
            "metadata": {
                "timestamp": "2024-01-01",
                "version": "1.0"
            },
            "contact": {
                "email": "<EMAIL>",  # Duplicate field name
                "phone": "+1234567890"
            }
        }

    @pytest.fixture
    def context_factory(self):
        """Factory function to create WorkflowContext with given inputs."""
        def _create_context(input_data, selector, search_mode="Exact Path", data_type="Auto-Detect"):
            context = WorkflowContext()
            context.current_node_id = "test_node"
            context.node_outputs["test_node"] = {
                "input_data": input_data,
                "selector": selector,
                "search_mode": search_mode,
                "data_type": data_type
            }
            return context
        return _create_context

    @pytest.mark.asyncio
    async def test_smart_search_finds_nested_field(self, component, sample_nested_data, context_factory):
        """Test that smart search finds a field nested deep in the structure."""
        # Arrange
        context = context_factory(sample_nested_data, "email", "Smart Search")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] == "<EMAIL>"  # Should find first occurrence

    @pytest.mark.asyncio
    async def test_smart_search_returns_first_match(self, component, sample_nested_data, context_factory):
        """Test that smart search returns the first occurrence when multiple fields exist."""
        # Arrange
        context = context_factory(sample_nested_data, "email", "Smart Search")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        # Should return first occurrence (user.profile.email), not contact.email
        assert result.outputs["output_data"] == "<EMAIL>"

    @pytest.mark.asyncio
    async def test_smart_search_field_not_found(self, component, sample_nested_data, context_factory):
        """Test that smart search returns None when field is not found."""
        # Arrange
        context = context_factory(sample_nested_data, "nonexistent", "Smart Search")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] is None

    @pytest.mark.asyncio
    async def test_exact_path_mode_unchanged(self, component, sample_nested_data, context_factory):
        """Test that exact path mode still works as before."""
        # Arrange
        context = context_factory(sample_nested_data, "user.profile.email", "Exact Path")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] == "<EMAIL>"

    @pytest.mark.asyncio
    async def test_smart_search_with_simple_structure(self, component, context_factory):
        """Test smart search with a simple flat structure."""
        # Arrange
        simple_data = {"name": "Alice", "age": 30, "city": "New York"}
        context = context_factory(simple_data, "name", "Smart Search")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] == "Alice"

    @pytest.mark.asyncio
    async def test_smart_search_with_array_in_structure(self, component, context_factory):
        """Test smart search when arrays are present in the structure."""
        # Arrange
        data_with_array = {
            "users": [
                {"email": "<EMAIL>", "name": "User 1"},
                {"email": "<EMAIL>", "name": "User 2"}
            ],
            "admin": {
                "email": "<EMAIL>"
            }
        }
        context = context_factory(data_with_array, "email", "Smart Search")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        # Should find first email in the structure (from users array)
        assert result.outputs["output_data"] == "<EMAIL>"

    def test_legacy_build_method_with_smart_search(self, component, sample_nested_data):
        """Test that legacy build method supports smart search mode."""
        # Arrange
        inputs = {
            "input_data": sample_nested_data,
            "selector": "email",
            "search_mode": "Smart Search",
            "data_type": "Auto-Detect"
        }
        
        # Act
        result = component.build(**inputs)
        
        # Assert
        assert result["error"] is None
        assert result["output_data"] == "<EMAIL>"


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
