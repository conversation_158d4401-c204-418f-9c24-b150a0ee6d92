import unittest
from unittest.mock import MagicMock, patch
from app.services.workflow_functions import WorkflowFunctions
from app.grpc_ import workflow_pb2
from app.models.workflow import Workflow, WorkflowMarketplaceListing, WorkflowVersion
from app.utils.constants.constants import WorkflowVisibilityEnum, WorkflowStatusEnum
import grpc


class TestSyncWorkflowToMarketplace(unittest.TestCase):
    def setUp(self):
        self.workflow_functions = WorkflowFunctions()

    def test_sync_workflow_to_marketplace_success(self):
        """Test successful sync of workflow changes to marketplace"""
        
        # Mock database session
        mock_db = MagicMock()
        
        # Mock workflow with changes to sync
        mock_workflow = MagicMock()
        mock_workflow.id = "workflow-123"
        mock_workflow.name = "Updated Workflow"
        mock_workflow.description = "Updated description"
        mock_workflow.owner_id = "user-123"
        mock_workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        mock_workflow.is_changes_marketplace = True  # Has pending changes
        mock_workflow.current_version_id = "version-456"
        mock_workflow.workflow_url = "https://example.com/workflow.json"
        mock_workflow.builder_url = "https://example.com/builder.json"
        mock_workflow.start_nodes = []
        mock_workflow.available_nodes = []
        mock_workflow.category = "automation"
        mock_workflow.tags = ["test"]
        
        # Mock current version
        mock_version = MagicMock()
        mock_version.id = "version-456"
        mock_version.name = "Updated Workflow"
        mock_version.description = "Updated description"
        mock_version.workflow_url = "https://example.com/workflow.json"
        mock_version.builder_url = "https://example.com/builder.json"
        mock_version.start_nodes = []
        mock_version.available_nodes = []
        mock_version.category = "automation"
        mock_version.tags = ["test"]
        mock_version.version_number = "1.1.0"
        
        # Mock existing marketplace listing
        mock_listing = MagicMock()
        mock_listing.id = "listing-789"
        mock_listing.workflow_id = "workflow-123"
        mock_listing.workflow_version_id = "old-version-123"
        mock_listing.listed_by_user_id = "user-123"
        
        # Setup database queries
        mock_db.query().filter().first.side_effect = [
            mock_workflow,  # First query for workflow
            mock_version,   # Second query for current version
            mock_listing    # Third query for marketplace listing
        ]
        
        # Mock SessionLocal to return our mock database
        with patch('app.services.workflow_functions.SessionLocal', return_value=mock_db):
            # Create request
            request = workflow_pb2.SyncWorkflowToMarketplaceRequest(
                workflow_id="workflow-123",
                user_id="user-123"
            )
            context = MagicMock()
            
            # Call the method
            response = self.workflow_functions.syncWorkflowToMarketplace(request, context)
            
            # Verify response
            self.assertTrue(response.success)
            self.assertIn("successfully synced", response.message.lower())
            
            # Verify workflow.is_changes_marketplace was reset to False
            self.assertFalse(mock_workflow.is_changes_marketplace)
            
            # Verify marketplace listing was updated
            self.assertEqual(mock_listing.workflow_version_id, "version-456")
            self.assertEqual(mock_listing.title, "Updated Workflow")
            self.assertEqual(mock_listing.description, "Updated description")
            
            # Verify database operations
            mock_db.add.assert_called()
            mock_db.commit.assert_called()

    def test_sync_workflow_to_marketplace_no_changes(self):
        """Test sync when workflow has no pending changes"""
        
        # Mock database session
        mock_db = MagicMock()
        
        # Mock workflow without changes
        mock_workflow = MagicMock()
        mock_workflow.id = "workflow-123"
        mock_workflow.owner_id = "user-123"
        mock_workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        mock_workflow.is_changes_marketplace = False  # No pending changes
        
        mock_db.query().filter().first.return_value = mock_workflow
        
        with patch('app.services.workflow_functions.SessionLocal', return_value=mock_db):
            request = workflow_pb2.SyncWorkflowToMarketplaceRequest(
                workflow_id="workflow-123",
                user_id="user-123"
            )
            context = MagicMock()
            
            response = self.workflow_functions.syncWorkflowToMarketplace(request, context)
            
            # Should return success but indicate no changes
            self.assertTrue(response.success)
            self.assertIn("no changes to sync", response.message.lower())

    def test_sync_workflow_to_marketplace_not_owner(self):
        """Test sync fails when user is not the owner"""
        
        mock_db = MagicMock()
        
        # Mock workflow with different owner
        mock_workflow = MagicMock()
        mock_workflow.id = "workflow-123"
        mock_workflow.owner_id = "different-user"
        mock_workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        
        mock_db.query().filter().first.return_value = mock_workflow
        
        with patch('app.services.workflow_functions.SessionLocal', return_value=mock_db):
            request = workflow_pb2.SyncWorkflowToMarketplaceRequest(
                workflow_id="workflow-123",
                user_id="user-123"
            )
            context = MagicMock()
            
            response = self.workflow_functions.syncWorkflowToMarketplace(request, context)
            
            # Should fail with permission denied
            self.assertFalse(response.success)
            self.assertIn("permission denied", response.message.lower())
            context.set_code.assert_called_with(grpc.StatusCode.PERMISSION_DENIED)

    def test_sync_workflow_to_marketplace_private_workflow(self):
        """Test sync fails when workflow is private"""
        
        mock_db = MagicMock()
        
        # Mock private workflow
        mock_workflow = MagicMock()
        mock_workflow.id = "workflow-123"
        mock_workflow.owner_id = "user-123"
        mock_workflow.visibility = WorkflowVisibilityEnum.PRIVATE
        
        mock_db.query().filter().first.return_value = mock_workflow
        
        with patch('app.services.workflow_functions.SessionLocal', return_value=mock_db):
            request = workflow_pb2.SyncWorkflowToMarketplaceRequest(
                workflow_id="workflow-123",
                user_id="user-123"
            )
            context = MagicMock()
            
            response = self.workflow_functions.syncWorkflowToMarketplace(request, context)
            
            # Should fail with failed precondition
            self.assertFalse(response.success)
            self.assertIn("only public workflows", response.message.lower())
            context.set_code.assert_called_with(grpc.StatusCode.FAILED_PRECONDITION)

    def test_sync_workflow_to_marketplace_workflow_not_found(self):
        """Test sync fails when workflow doesn't exist"""
        
        mock_db = MagicMock()
        mock_db.query().filter().first.return_value = None
        
        with patch('app.services.workflow_functions.SessionLocal', return_value=mock_db):
            request = workflow_pb2.SyncWorkflowToMarketplaceRequest(
                workflow_id="nonexistent-workflow",
                user_id="user-123"
            )
            context = MagicMock()
            
            response = self.workflow_functions.syncWorkflowToMarketplace(request, context)
            
            # Should fail with not found
            self.assertFalse(response.success)
            self.assertIn("not found", response.message.lower())
            context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)

    def test_sync_workflow_to_marketplace_no_listing(self):
        """Test sync fails when no marketplace listing exists"""
        
        mock_db = MagicMock()
        
        # Mock workflow with changes but no marketplace listing
        mock_workflow = MagicMock()
        mock_workflow.id = "workflow-123"
        mock_workflow.owner_id = "user-123"
        mock_workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        mock_workflow.is_changes_marketplace = True
        mock_workflow.current_version_id = "version-456"
        
        # Mock current version
        mock_version = MagicMock()
        mock_version.id = "version-456"
        
        # Setup database queries - no marketplace listing found
        mock_db.query().filter().first.side_effect = [
            mock_workflow,  # First query for workflow
            mock_version,   # Second query for current version
            None            # Third query for marketplace listing returns None
        ]
        
        with patch('app.services.workflow_functions.SessionLocal', return_value=mock_db):
            request = workflow_pb2.SyncWorkflowToMarketplaceRequest(
                workflow_id="workflow-123",
                user_id="user-123"
            )
            context = MagicMock()
            
            response = self.workflow_functions.syncWorkflowToMarketplace(request, context)
            
            # Should fail with failed precondition
            self.assertFalse(response.success)
            self.assertIn("no marketplace listing found", response.message.lower())
            context.set_code.assert_called_with(grpc.StatusCode.FAILED_PRECONDITION)


if __name__ == "__main__":
    unittest.main()