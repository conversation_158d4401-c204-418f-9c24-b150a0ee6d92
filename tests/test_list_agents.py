#!/usr/bin/env python3

import grpc
import sys
import os

# Add the parent directory to the Python path to find the app module
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from app.grpc import agent_pb2, agent_pb2_grpc


def test_list_agents():
    # Connect to the gRPC server
    channel = grpc.insecure_channel("localhost:50057")
    stub = agent_pb2_grpc.AgentServiceStub(channel)

    # Create a test request
    request = agent_pb2.ListAgentsRequest(
        page=1, page_size=10, owner_id="test-owner"  # You might need to adjust this
    )

    try:
        # Call the listAgents method
        response = stub.listAgents(request)
        print(f"Success: {response.success}")
        print(f"Total agents: {response.total}")
        print(f"Page: {response.page}")
        print(f"Total pages: {response.total_pages}")
        print(f"Number of agents returned: {len(response.agents)}")

        for i, agent in enumerate(response.agents):
            print(f"Agent {i+1}: {agent.name} (ID: {agent.id})")

    except grpc.RpcError as e:
        print(f"gRPC Error: {e.code()} - {e.details()}")
    except Exception as e:
        print(f"Error: {e}")

    channel.close()


if __name__ == "__main__":
    test_list_agents()
