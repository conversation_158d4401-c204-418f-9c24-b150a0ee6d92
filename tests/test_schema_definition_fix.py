"""
Quick test to verify schema_definition fix
"""

from app.schemas.integration import UserIntegrationStatus

def test_schema_definition_types():
    """Test different schema_definition types."""
    
    # Test with list (correct type)
    user_integration_list = UserIntegrationStatus(
        user_id="user_123",
        integration_id="integration_456",
        integration_name="Test API",
        is_connected=True,
        last_used_at="2023-01-03T12:00:00",
        created_at="2023-01-01T12:00:00",
        scopes=[],
        connection_type="api_key",
        schema_definition=[{"name": "api_key", "required": True}]
    )
    print("✅ List schema_definition works")
    
    # Test with empty list
    user_integration_empty = UserIntegrationStatus(
        user_id="user_123",
        integration_id="integration_456",
        integration_name="Test API",
        is_connected=True,
        last_used_at="2023-01-03T12:00:00",
        created_at="2023-01-01T12:00:00",
        scopes=[],
        connection_type="api_key",
        schema_definition=[]
    )
    print("✅ Empty list schema_definition works")
    
    # Test with None
    user_integration_none = UserIntegrationStatus(
        user_id="user_123",
        integration_id="integration_456",
        integration_name="Test API",
        is_connected=True,
        last_used_at="2023-01-03T12:00:00",
        created_at="2023-01-01T12:00:00",
        scopes=[],
        connection_type="oauth",
        schema_definition=None
    )
    print("✅ None schema_definition works")
    
    print("All tests passed! 🎉")

if __name__ == "__main__":
    test_schema_definition_types()
