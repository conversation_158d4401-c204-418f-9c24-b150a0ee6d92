#!/usr/bin/env python3
"""
Simple test script to verify the sync models functionality.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.provider_functions import ProviderFunctionsService
from app.grpc import provider_pb2
from unittest.mock import Mock

def test_sync_models():
    """Test the sync models functionality."""
    print("Testing sync models functionality...")
    
    # Create service instance
    service = ProviderFunctionsService()
    
    # Create mock context
    context = Mock()
    
    # Create sync request
    request = provider_pb2.SyncModelsRequest()
    
    try:
        # Call sync method
        print("Calling syncModels...")
        response = service.syncModels(request, context)
        
        # Print results
        print(f"Success: {response.success}")
        print(f"Message: {response.message}")
        
        if response.success:
            stats = response.stats
            print(f"Stats:")
            print(f"  Providers added: {stats.providersAdded}")
            print(f"  Providers updated: {stats.providersUpdated}")
            print(f"  Providers removed: {stats.providersRemoved}")
            print(f"  Models added: {stats.modelsAdded}")
            print(f"  Models updated: {stats.modelsUpdated}")
            print(f"  Models removed: {stats.modelsRemoved}")
            print(f"  Total processed: {stats.totalProcessed}")
        
        return response.success
        
    except Exception as e:
        print(f"Error during sync: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_sync_models()
    if success:
        print("\n✅ Sync test completed successfully!")
    else:
        print("\n❌ Sync test failed!")
        sys.exit(1)
        
        
        
        
        
    #     def syncModels(self, request: provider_pb2.SyncModelsRequest, context: grpc.ServicerContext) -> provider_pb2.SyncModelsResponse:
    #     """Sync models from the external API with the database."""
    #     try:
    #         db = self._get_db_session()
            
    #         # Initialize stats
    #         stats = {
    #             'providers_added': 0,
    #             'providers_updated': 0,
    #             'providers_removed': 0,
    #             'models_added': 0,
    #             'models_updated': 0,
    #             'models_removed': 0,
    #             'total_processed': 0
    #         }
            
    #         # Fetch data from the external API
    #         api_url = "https://api.requesty.ai/router/models"
    #         logger.info(f"Fetching models from API: {api_url}")
            
    #         try:
    #             response = requests.get(api_url, timeout=30)
    #             response.raise_for_status()
    #             api_data = response.json()
    #             logger.info(f"Successfully fetched {len(api_data)} models from API")
    #         except requests.RequestException as e:
    #             logger.error(f"Failed to fetch data from API: {str(e)}")
    #             return provider_pb2.SyncModelsResponse(
    #                 success=False,
    #                 message=f"Failed to fetch data from API: {str(e)}",
    #                 stats=provider_pb2.SyncStats()
    #             )
            
    #         # Process the API data
    #         api_providers = {}
    #         api_models = {}
            
    #         for item in api_data:
    #             provider_name = item.get("provider")
    #             model_name = item.get("model")
                
    #             if not provider_name or not model_name:
    #                 continue
                
    #             # Group by provider
    #             if provider_name not in api_providers:
    #                 api_providers[provider_name] = {
    #                     'provider': provider_name,
    #                     'description': f"{provider_name.capitalize()} is an integrated model provider using Requestly router.",
    #                     'base_url': "https://router.requesty.ai/v1",
    #                     'models': []
    #                 }
                
    #             # Add model to provider
    #             model_data = {
    #                 'model': model_name,
    #                 'model_id': model_name,
    #                 'description': item.get('description', f"{provider_name.capitalize()} provides high-performance models like {model_name} for AI tasks."),
    #                 'input_price_per_token': self._safe_float_conversion(item.get('input_tokens_price_per_million')),
    #                 'output_price_per_token': self._safe_float_conversion(item.get('output_tokens_price_per_million')),
    #                 'max_tokens': item.get('max_output_tokens'),
    #                 'context_window': item.get('context_window'),
    #                 'provider_type': 'chat'
    #             }
                
    #             api_providers[provider_name]['models'].append(model_data)
    #             api_models[f"{provider_name}:{model_name}"] = model_data
            
    #         stats['total_processed'] = len(api_data)
            
    #         # Get existing providers and models from database
    #         existing_providers = {p.provider: p for p in db.query(Provider).all()}
    #         existing_models = {}
    #         for model in db.query(Model).options(joinedload(Model.provider)).all():
    #             key = f"{model.provider.provider}:{model.model}"
    #             existing_models[key] = model
            
    #         # Sync providers
    #         for provider_name, provider_data in api_providers.items():
    #             if provider_name in existing_providers:
    #                 # Update existing provider if needed
    #                 provider = existing_providers[provider_name]
    #                 updated = False
                    
    #                 if provider.description != provider_data['description']:
    #                     provider.description = provider_data['description']
    #                     updated = True
                    
    #                 if provider.base_url != provider_data['base_url']:
    #                     provider.base_url = provider_data['base_url']
    #                     updated = True
                    
    #                 if updated:
    #                     provider.updated_at = datetime.utcnow()
    #                     stats['providers_updated'] += 1
    #                     logger.info(f"Updated provider: {provider_name}")
    #             else:
    #                 # Create new provider
    #                 new_provider = Provider(
    #                     provider=provider_name,
    #                     description=provider_data['description'],
    #                     base_url=provider_data['base_url'],
    #                     is_active=True,
    #                     is_default=False
    #                 )
    #                 db.add(new_provider)
    #                 db.flush()  # Get the ID
    #                 existing_providers[provider_name] = new_provider
    #                 stats['providers_added'] += 1
    #                 logger.info(f"Added new provider: {provider_name}")
            
    #         # Sync models
    #         for model_key, model_data in api_models.items():
    #             provider_name, model_name = model_key.split(':', 1)
    #             provider = existing_providers[provider_name]
                
    #             if model_key in existing_models:
    #                 # Update existing model if needed
    #                 model = existing_models[model_key]
    #                 updated = False
                    
    #                 if model.description != model_data['description']:
    #                     model.description = model_data['description']
    #                     updated = True
                    
    #                 if model.input_price_per_token != model_data['input_price_per_token']:
    #                     model.input_price_per_token = model_data['input_price_per_token']
    #                     updated = True
                    
    #                 if model.output_price_per_token != model_data['output_price_per_token']:
    #                     model.output_price_per_token = model_data['output_price_per_token']
    #                     updated = True
                    
    #                 if model.max_tokens != model_data['max_tokens']:
    #                     model.max_tokens = model_data['max_tokens']
    #                     updated = True
                    
    #                 if model.context_window != model_data['context_window']:
    #                     model.context_window = model_data['context_window']
    #                     updated = True
                    
    #                 if updated:
    #                     model.updated_at = datetime.utcnow()
    #                     stats['models_updated'] += 1
    #                     logger.info(f"Updated model: {model_key}")
    #             else:
    #                 # Create new model
    #                 new_model = Model(
    #                     provider_id=provider.id,
    #                     model=model_data['model'],
    #                     model_id=model_data['model_id'],
    #                     description=model_data['description'],
    #                     input_price_per_token=model_data['input_price_per_token'],
    #                     output_price_per_token=model_data['output_price_per_token'],
    #                     max_tokens=model_data['max_tokens'],
    #                     context_window=model_data['context_window'],
    #                     temperature=0.7,
    #                     provider_type=model_data['provider_type'],
    #                     is_active=True,
    #                     is_default=False
    #                 )
    #                 db.add(new_model)
    #                 stats['models_added'] += 1
    #                 logger.info(f"Added new model: {model_key}")
            
    #         # Remove models that are no longer available in the API
    #         for model_key, model in existing_models.items():
    #             if model_key not in api_models:
    #                 db.delete(model)
    #                 stats['models_removed'] += 1
    #                 logger.info(f"Removed model: {model_key}")
            
    #         # Remove providers that have no models left
    #         for provider_name, provider in existing_providers.items():
    #             if provider_name not in api_providers:
    #                 # Check if provider has any remaining models
    #                 remaining_models = db.query(func.count(Model.id)).filter(Model.provider_id == provider.id).scalar()
    #                 if remaining_models == 0:
    #                     db.delete(provider)
    #                     stats['providers_removed'] += 1
    #                     logger.info(f"Removed provider: {provider_name}")
            
    #         # Commit all changes
    #         db.commit()
            
    #         # Create response
    #         sync_stats = provider_pb2.SyncStats(
    #             providersAdded=stats['providers_added'],
    #             providersUpdated=stats['providers_updated'],
    #             providersRemoved=stats['providers_removed'],
    #             modelsAdded=stats['models_added'],
    #             modelsUpdated=stats['models_updated'],
    #             modelsRemoved=stats['models_removed'],
    #             totalProcessed=stats['total_processed']
    #         )
            
    #         message = (f"Sync completed successfully. "
    #                   f"Providers: {stats['providers_added']} added, {stats['providers_updated']} updated, {stats['providers_removed']} removed. "
    #                   f"Models: {stats['models_added']} added, {stats['models_updated']} updated, {stats['models_removed']} removed. "
    #                   f"Total processed: {stats['total_processed']}")
            
    #         logger.info(message)
    #         return provider_pb2.SyncModelsResponse(
    #             success=True,
    #             message=message,
    #             stats=sync_stats
    #         )
            
    #     except Exception as e:
    #         logger.error(f"Error during sync: {str(e)}")
    #         db.rollback()
    #         return provider_pb2.SyncModelsResponse(
    #             success=False,
    #             message=f"Sync failed: {str(e)}",
    #             stats=provider_pb2.SyncStats()
    #         )
    #     finally:
    #         db.close()
    
    # def _safe_float_conversion(self, value: Any) -> Optional[float]:
    #     """Safely convert a value to float, handling per-million pricing."""
    #     if value is None:
    #         return None
    #     try:
    #         # Convert from per-million to per-token
    #         return float(value) / 1_000_000
    #     except (ValueError, TypeError):
    #         return None