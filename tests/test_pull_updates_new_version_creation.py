"""
Test for enhanced pullUpdatesFromSource functionality that creates new versions.

This test verifies that pullUpdatesFromSource creates a new version with source changes
instead of overwriting the user's current version, preserving their work and maintaining
proper version history.
"""

import pytest
import json
from datetime import datetime, timezone
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.utils.constants.constants import (
    WorkflowOwnerTypeEnum, 
    WorkflowStatusEnum, 
    WorkflowVisibilityEnum,
    WorkflowCategoryEnum
)
from app.services.marketplace_functions import WorkflowMarketplaceFunctions
from app.grpc_ import workflow_pb2


class TestPullUpdatesNewVersionCreation:
    """Test the enhanced pullUpdatesFromSource that creates new versions."""

    def test_pull_updates_creates_new_version(self):
        """
        Test that pullUpdatesFromSource creates a new version instead of overwriting current version.
        
        Scenario:
        1. User has cloned workflow based on source v1.0.0
        2. User has made customizations (current version is v1.1.0)
        3. Source workflow publishes v2.0.0 with new features
        4. User pulls updates - should create v1.2.0 with source changes
        5. User's v1.1.0 work should be preserved
        """
        
        # Mock database session
        mock_db = Mock(spec=Session)
        
        # Step 1: Setup source workflow with latest version v2.0.0
        source_workflow = Workflow(
            id="source-workflow-123",
            name="Enhanced AI Processor",
            description="Enhanced AI processing with new features",
            workflow_url="https://storage.googleapis.com/workflows/source-v2.json",
            builder_url="https://storage.googleapis.com/builders/source-v2.json",
            start_nodes=[{"id": "start", "type": "start"}],
            available_nodes=[
                {"id": "ai-node", "type": "ai"},
                {"id": "new-feature", "type": "enhancement"}
            ],
            owner_id="owner-456",
            owner_type=WorkflowOwnerTypeEnum.USER,
            current_version_id="source-version-v2-888",
            category=WorkflowCategoryEnum.LLM_ORCHESTRATION,
            tags=["ai", "enhanced", "v2"],
            visibility=WorkflowVisibilityEnum.PUBLIC,
            is_imported=False,
        )
        
        source_v2_version = WorkflowVersion(
            id="source-version-v2-888",
            workflow_id="source-workflow-123",
            version_number="2.0.0",
            name="Enhanced AI Processor",
            description="Enhanced AI processing with new features",
            workflow_url="https://storage.googleapis.com/workflows/source-v2.json",
            builder_url="https://storage.googleapis.com/builders/source-v2.json",
            start_nodes=[{"id": "start", "type": "start"}],
            available_nodes=[
                {"id": "ai-node", "type": "ai"},
                {"id": "new-feature", "type": "enhancement"}
            ],
            category=WorkflowCategoryEnum.LLM_ORCHESTRATION,
            tags=["ai", "enhanced", "v2"],
            changelog="Added new enhancement features and improved AI processing",
            status=WorkflowStatusEnum.ACTIVE,
        )
        
        # Step 2: Setup user's cloned workflow (based on source v1.0.0, but user has v1.1.0)
        cloned_workflow = Workflow(
            id="cloned-workflow-456",
            name="My Custom AI Processor",  # User customized name
            description="My customized AI processor with personal tweaks",  # User customized
            workflow_url="https://storage.googleapis.com/workflows/cloned-custom.json",
            builder_url="https://storage.googleapis.com/builders/cloned-custom.json",
            start_nodes=[{"id": "start", "type": "start"}],
            available_nodes=[
                {"id": "ai-node", "type": "ai"},
                {"id": "custom-node", "type": "user-addition"}  # User added this
            ],
            owner_id="user-789",
            owner_type=WorkflowOwnerTypeEnum.USER,
            current_version_id="cloned-version-v1-1-999",  # User's current version
            workflow_template_id="source-workflow-123",  # Points to source
            template_owner_id="owner-456",
            source_version_id="source-version-v1-777",  # Based on source v1.0.0
            is_imported=True,
            visibility=WorkflowVisibilityEnum.PRIVATE,
            is_updated=True,  # Has pending updates
            category=WorkflowCategoryEnum.LLM_ORCHESTRATION,
            tags=["ai", "custom"],
        )
        
        # User's current version (their customizations)
        cloned_current_version = WorkflowVersion(
            id="cloned-version-v1-1-999",
            workflow_id="cloned-workflow-456",
            version_number="1.1.0",  # User's customized version
            name="My Custom AI Processor",
            description="My customized AI processor with personal tweaks",
            workflow_url="https://storage.googleapis.com/workflows/cloned-custom.json",
            builder_url="https://storage.googleapis.com/builders/cloned-custom.json",
            start_nodes=[{"id": "start", "type": "start"}],
            available_nodes=[
                {"id": "ai-node", "type": "ai"},
                {"id": "custom-node", "type": "user-addition"}
            ],
            category=WorkflowCategoryEnum.LLM_ORCHESTRATION,
            tags=["ai", "custom"],
            changelog="Added custom node and personalized settings",
            status=WorkflowStatusEnum.ACTIVE,
        )
        
        # Original source version that user's workflow was based on
        source_v1_version = WorkflowVersion(
            id="source-version-v1-777",
            workflow_id="source-workflow-123",
            version_number="1.0.0",
            name="AI Processor",
            description="Basic AI processing",
            changelog="Initial version",
        )
        
        # Step 3: Test pullUpdatesFromSource
        marketplace_service = WorkflowMarketplaceFunctions()
        
        # Mock database queries - simpler approach
        workflow_queries = []
        version_queries = []
        
        def mock_query_side_effect(model):
            mock_query = Mock()
            if model == Workflow:
                workflow_queries.append(mock_query)
                mock_filter = Mock()
                mock_query.filter.return_value = mock_filter
                # First call gets cloned workflow, second gets source workflow
                mock_filter.first.side_effect = [cloned_workflow, source_workflow]
                return mock_query
            elif model == WorkflowVersion:
                version_queries.append(mock_query)
                mock_filter = Mock()
                mock_query.filter.return_value = mock_filter
                # First call gets source latest version, second gets cloned current version
                mock_filter.first.side_effect = [source_v2_version, cloned_current_version]
                return mock_query
            return Mock()
        
        mock_db.query.side_effect = mock_query_side_effect
        
        # Mock add and flush operations
        added_objects = []
        def mock_add(obj):
            added_objects.append(obj)
            if isinstance(obj, WorkflowVersion):
                # Simulate database assigning ID
                obj.id = f"new-version-{len(added_objects)}"
        
        mock_db.add = mock_add
        mock_db.flush = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()
        
        with patch.object(marketplace_service, 'get_db', return_value=mock_db):
            # Create request
            pull_request = workflow_pb2.PullUpdatesFromSourceRequest(
                workflow_id="cloned-workflow-456",
                user_id="user-789"
            )
            
            # Mock context
            mock_context = Mock()
            
            # Execute pullUpdatesFromSource
            response = marketplace_service.pullUpdatesFromSource(pull_request, mock_context)
            
            # Step 4: Verify the results
            
            # Debug: Print response details
            print(f"Response success: {response.success}")
            print(f"Response message: {response.message}")
            
            # Should be successful
            assert response.success == True
            assert "Successfully created version" in response.message
            assert "1.2.0" in response.message or "2.1.0" in response.message  # Should create incremented version
            assert "Your previous work is preserved" in response.message
            
            # Verify new version was created
            new_versions = [obj for obj in added_objects if isinstance(obj, WorkflowVersion)]
            assert len(new_versions) == 1, "Should create exactly one new version"
            
            new_version = new_versions[0]
            assert new_version.workflow_id == "cloned-workflow-456"
            # Version should be incremented from user's current version
            assert new_version.version_number in ["1.2.0", "2.1.0"]  # Depends on version parsing logic
            
            # New version should have source workflow content
            assert new_version.name == "Enhanced AI Processor"  # From source v2.0.0
            assert new_version.description == "Enhanced AI processing with new features"
            assert new_version.workflow_url == "https://storage.googleapis.com/workflows/source-v2.json"
            assert new_version.available_nodes == [
                {"id": "ai-node", "type": "ai"},
                {"id": "new-feature", "type": "enhancement"}
            ]
            assert "Synced with source workflow version 2.0.0" in new_version.changelog
            
            # Verify workflow was updated
            workflow_updates = [obj for obj in added_objects if isinstance(obj, Workflow)]
            assert len(workflow_updates) == 1, "Should update the workflow"
            
            updated_workflow = workflow_updates[0]
            assert updated_workflow.id == "cloned-workflow-456"
            assert updated_workflow.current_version_id == new_version.id  # Points to new version
            assert updated_workflow.source_version_id == "source-version-v2-888"  # Updated tracking
            assert updated_workflow.is_updated == False  # Reset flag
            
            # Workflow main fields should be updated with source content
            assert updated_workflow.name == "Enhanced AI Processor"
            assert updated_workflow.description == "Enhanced AI processing with new features"

    def test_pull_updates_already_up_to_date(self):
        """Test pullUpdatesFromSource when workflow is already up to date."""
        
        mock_db = Mock(spec=Session)
        
        # Setup workflow that's already synced with latest source
        cloned_workflow = Workflow(
            id="cloned-workflow-456",
            owner_id="user-789",
            workflow_template_id="source-workflow-123",
            source_version_id="source-version-latest-888",  # Already on latest
            is_imported=True,
        )
        
        source_workflow = Workflow(
            id="source-workflow-123",
            current_version_id="source-version-latest-888",  # Same as cloned
        )
        
        latest_version = WorkflowVersion(
            id="source-version-latest-888",
            version_number="2.0.0",
            name="Latest Version",
        )
        
        # Mock database queries
        def mock_query_side_effect(model):
            mock_query = Mock()
            if model == Workflow:
                mock_filter = Mock()
                mock_query.filter.return_value = mock_filter
                # First call gets cloned workflow, second gets source workflow
                mock_filter.first.side_effect = [cloned_workflow, source_workflow]
                return mock_query
            elif model == WorkflowVersion:
                mock_filter = Mock()
                mock_query.filter.return_value = mock_filter
                mock_filter.first.return_value = latest_version
                return mock_query
            return Mock()
        
        mock_db.query.side_effect = mock_query_side_effect
        
        marketplace_service = WorkflowMarketplaceFunctions()
        
        with patch.object(marketplace_service, 'get_db', return_value=mock_db):
            pull_request = workflow_pb2.PullUpdatesFromSourceRequest(
                workflow_id="cloned-workflow-456",
                user_id="user-789"
            )
            
            mock_context = Mock()
            response = marketplace_service.pullUpdatesFromSource(pull_request, mock_context)
            
            # Debug: Print response details
            print(f"Second test - Response success: {response.success}")
            print(f"Second test - Response message: {response.message}")
            
            # Should indicate already up to date
            assert response.success == True
            assert "already up to date" in response.message
            assert "2.0.0" in response.message

    def test_pull_updates_permission_denied(self):
        """Test pullUpdatesFromSource with wrong user."""
        
        mock_db = Mock(spec=Session)
        
        cloned_workflow = Workflow(
            id="cloned-workflow-456",
            owner_id="different-user-999",  # Different owner
        )
        
        mock_db.query.return_value.filter.return_value.first.return_value = cloned_workflow
        
        marketplace_service = WorkflowMarketplaceFunctions()
        
        with patch.object(marketplace_service, 'get_db', return_value=mock_db):
            pull_request = workflow_pb2.PullUpdatesFromSourceRequest(
                workflow_id="cloned-workflow-456",
                user_id="user-789"  # Wrong user
            )
            
            mock_context = Mock()
            response = marketplace_service.pullUpdatesFromSource(pull_request, mock_context)
            
            assert response.success == False
            assert "Permission denied" in response.message

    def test_pull_updates_not_cloned_workflow(self):
        """Test pullUpdatesFromSource on non-cloned workflow."""
        
        mock_db = Mock(spec=Session)
        
        original_workflow = Workflow(
            id="original-workflow-456",
            owner_id="user-789",
            is_imported=False,  # Not cloned
            workflow_template_id=None,
        )
        
        mock_db.query.return_value.filter.return_value.first.return_value = original_workflow
        
        marketplace_service = WorkflowMarketplaceFunctions()
        
        with patch.object(marketplace_service, 'get_db', return_value=mock_db):
            pull_request = workflow_pb2.PullUpdatesFromSourceRequest(
                workflow_id="original-workflow-456",
                user_id="user-789"
            )
            
            mock_context = Mock()
            response = marketplace_service.pullUpdatesFromSource(pull_request, mock_context)
            
            assert response.success == False
            assert "not cloned from a source workflow" in response.message


if __name__ == "__main__":
    test = TestPullUpdatesNewVersionCreation()
    test.test_pull_updates_creates_new_version()
    print("Main pullUpdatesFromSource new version creation test passed!")
    
    # Additional tests can be run separately if needed
    # test.test_pull_updates_already_up_to_date()
    # test.test_pull_updates_permission_denied()
    # test.test_pull_updates_not_cloned_workflow()