#!/usr/bin/env python3

import grpc
import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

from app.grpc import agent_pb2, agent_pb2_grpc


def test_get_agent():
    # Connect to the gRPC server
    channel = grpc.insecure_channel("localhost:50057")
    stub = agent_pb2_grpc.AgentServiceStub(channel)

    # Create a test request - you'll need to replace with an actual agent ID
    request = agent_pb2.GetAgentRequest(id="test-agent-id")

    try:
        # Call the getAgent method
        response = stub.getAgent(request)
        print(f"Success: {response.success}")
        print(f"Message: {response.message}")
        if response.agent:
            print(f"Agent Name: {response.agent.name}")
    except grpc.RpcError as e:
        print(f"gRPC Error: {e.code()} - {e.details()}")
    except Exception as e:
        print(f"Error: {e}")

    channel.close()


if __name__ == "__main__":
    test_get_agent()
