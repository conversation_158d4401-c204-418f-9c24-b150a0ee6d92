"""
Run all tests in the tests directory.
"""

import unittest
import sys
import os

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))


def run_tests():
    """Run all tests in the tests directory."""
    # Get the path to the tests directory
    tests_dir = os.path.dirname(os.path.abspath(__file__))

    # Discover and run all tests
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover(tests_dir, pattern="test_*.py")

    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)

    # Return appropriate exit code
    return 0 if result.wasSuccessful() else 1


if __name__ == "__main__":
    sys.exit(run_tests())
