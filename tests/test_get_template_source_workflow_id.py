import pytest
import grpc
from unittest.mock import Mock, patch
from app.services.marketplace_functions import WorkflowMarketplaceFunctions
from app.grpc_ import workflow_pb2
from app.models.workflow import WorkflowMarketplaceListing, WorkflowVersion, Workflow
from sqlalchemy.orm import Session


class TestGetTemplateSourceWorkflowId:
    """Test suite for adding source_workflow_id to getTemplate function"""

    def setup_method(self):
        """Set up test fixtures"""
        self.service = WorkflowMarketplaceFunctions()
        self.mock_db = Mock(spec=Session)
        
    @patch.object(WorkflowMarketplaceFunctions, 'get_db')
    def test_get_template_includes_source_workflow_id(self, mock_get_db):
        """Test that getTemplate response includes source_workflow_id"""
        # Arrange
        mock_get_db.return_value = self.mock_db
        
        # Create mock marketplace listing with source workflow
        mock_listing = Mock(spec=WorkflowMarketplaceListing)
        mock_listing.id = "listing-123"
        mock_listing.title = "Test Workflow"
        mock_listing.description = "Test Description"
        mock_listing.workflow_id = "source-workflow-456"  # This should be the source workflow ID
        mock_listing.workflow_version_id = "version-789"
        mock_listing.use_count = 10
        mock_listing.listed_by_user_id = "user-123"
        mock_listing.execution_count = 5
        mock_listing.category = "automation"
        mock_listing.tags = ["tag1", "tag2"]
        mock_listing.status = "active"
        mock_listing.created_at = None
        mock_listing.updated_at = None
        
        # Create mock workflow version
        mock_version = Mock(spec=WorkflowVersion)
        mock_version.workflow_url = ""
        mock_version.builder_url = ""
        mock_version.start_nodes = []
        mock_version.available_nodes = []
        mock_version.version_number = "1.0.0"
        
        # Set up database query mocks
        self.mock_db.query.return_value.filter.return_value.first.side_effect = [
            mock_listing,  # First call for marketplace listing
            mock_version   # Second call for workflow version
        ]
        
        # Create request
        request = workflow_pb2.GetTemplateRequest(id="listing-123")
        context = Mock()
        
        # Act
        response = self.service.getTemplate(request, context)
        
        # Assert
        assert response.success is True
        assert response.template.id == "listing-123"
        assert hasattr(response.template, 'source_workflow_id')
        assert response.template.source_workflow_id == "source-workflow-456"
        
    @patch.object(WorkflowMarketplaceFunctions, 'get_db')
    def test_get_template_handles_missing_source_workflow_id(self, mock_get_db):
        """Test that getTemplate handles cases where source_workflow_id is None"""
        # Arrange
        mock_get_db.return_value = self.mock_db
        
        # Create mock marketplace listing without source workflow
        mock_listing = Mock(spec=WorkflowMarketplaceListing)
        mock_listing.id = "listing-123"
        mock_listing.title = "Test Workflow"
        mock_listing.description = "Test Description"
        mock_listing.workflow_id = None  # No source workflow
        mock_listing.workflow_version_id = "version-789"
        mock_listing.use_count = 10
        mock_listing.listed_by_user_id = "user-123"
        mock_listing.execution_count = 5
        mock_listing.category = "automation"
        mock_listing.tags = ["tag1", "tag2"]
        mock_listing.status = "active"
        mock_listing.created_at = None
        mock_listing.updated_at = None
        
        # Create mock workflow version
        mock_version = Mock(spec=WorkflowVersion)
        mock_version.workflow_url = ""
        mock_version.builder_url = ""
        mock_version.start_nodes = []
        mock_version.available_nodes = []
        mock_version.version_number = "1.0.0"
        
        # Set up database query mocks
        self.mock_db.query.return_value.filter.return_value.first.side_effect = [
            mock_listing,  # First call for marketplace listing
            mock_version   # Second call for workflow version
        ]
        
        # Create request
        request = workflow_pb2.GetTemplateRequest(id="listing-123")
        context = Mock()
        
        # Act
        response = self.service.getTemplate(request, context)
        
        # Assert
        assert response.success is True
        assert response.template.id == "listing-123"
        assert hasattr(response.template, 'source_workflow_id')
        assert response.template.source_workflow_id == ""  # Should be empty string when None
        
    @patch.object(WorkflowMarketplaceFunctions, 'get_db')
    def test_get_template_not_found(self, mock_get_db):
        """Test that getTemplate handles marketplace listing not found"""
        # Arrange
        mock_get_db.return_value = self.mock_db
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Create request
        request = workflow_pb2.GetTemplateRequest(id="non-existent-123")
        context = Mock()
        
        # Act
        response = self.service.getTemplate(request, context)
        
        # Assert
        assert response.success is False
        assert "not found" in response.message.lower()