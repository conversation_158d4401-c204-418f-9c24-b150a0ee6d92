"""
Simple test script for the workflow schema converter.
"""

import json
import os
import sys
import unittest
from pathlib import Path

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from app.services.workflow_schema_converter import convert_workflow_to_transition_schema


class TestConverter(unittest.TestCase):
    """Test case for workflow schema converter."""

    def setUp(self):
        """Set up test fixtures."""
        # Path to sample workflow
        sample_workflow_path = Path(__file__).parent.parent / "testing" / "sample_workflow.json"

        # Load the sample workflow
        with open(sample_workflow_path, "r") as f:
            self.sample_workflow = json.load(f)

    def test_conversion(self):
        """Test that the workflow can be converted to transition schema."""
        # Convert the sample workflow to transition schema format
        converted_schema = convert_workflow_to_transition_schema(self.sample_workflow)

        # Assert that the schema has nodes and transitions
        self.assertIn("nodes", converted_schema, "Schema should have nodes")
        self.assertIn("transitions", converted_schema, "Schema should have transitions")

        # Assert that there are nodes and transitions
        self.assertGreater(
            len(converted_schema["nodes"]), 0, "Schema should have at least one node"
        )
        self.assertGreater(
            len(converted_schema["transitions"]), 0, "Schema should have at least one transition"
        )


if __name__ == "__main__":
    unittest.main()
