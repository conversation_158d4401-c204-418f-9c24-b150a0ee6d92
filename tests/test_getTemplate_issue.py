import pytest
import json
from datetime import datetime
from unittest.mock import Mock, patch
from app.services.marketplace_functions import WorkflowMarketplaceFunctions
from app.models.workflow import WorkflowMarketplaceListing, WorkflowVersion
from app.grpc_ import workflow_pb2
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum, WorkflowCategoryEnum


class TestGetTemplateIssue:
    """Test the getTemplate endpoint to ensure it returns current version and available_nodes"""

    def setup_method(self):
        """Setup test fixtures"""
        self.marketplace_functions = WorkflowMarketplaceFunctions()
        
        # Mock workflow version with available_nodes
        self.mock_workflow_version = Mock(spec=WorkflowVersion)
        self.mock_workflow_version.id = "version-123"
        self.mock_workflow_version.version_number = "2.1.0"
        self.mock_workflow_version.workflow_url = "https://example.com/workflow"
        self.mock_workflow_version.builder_url = "https://example.com/builder"
        self.mock_workflow_version.start_nodes = [{"type": "start", "id": "start-1"}]
        self.mock_workflow_version.available_nodes = [
            {"type": "ai", "name": "OpenAI GPT", "id": "openai-1"},
            {"type": "data", "name": "Data Processor", "id": "data-1"},
            {"type": "api", "name": "API Call", "id": "api-1"}
        ]
        
        # Mock marketplace listing
        self.mock_marketplace_listing = Mock(spec=WorkflowMarketplaceListing)
        self.mock_marketplace_listing.id = "listing-123"
        self.mock_marketplace_listing.title = "Test Workflow"
        self.mock_marketplace_listing.description = "A test workflow"
        self.mock_marketplace_listing.workflow_version_id = "version-123"
        self.mock_marketplace_listing.workflow_id = "workflow-123"
        self.mock_marketplace_listing.listed_by_user_id = "user-123"
        self.mock_marketplace_listing.use_count = 5
        self.mock_marketplace_listing.execution_count = 10
        self.mock_marketplace_listing.category = WorkflowCategoryEnum.LLM_ORCHESTRATION
        self.mock_marketplace_listing.tags = ["ai", "automation"]
        self.mock_marketplace_listing.status = WorkflowStatusEnum.ACTIVE
        self.mock_marketplace_listing.created_at = datetime(2024, 1, 1)
        self.mock_marketplace_listing.updated_at = datetime(2024, 1, 2)

    @patch('app.services.marketplace_functions.SessionLocal')
    def test_getTemplate_returns_current_version_and_available_nodes(self, mock_session_local):
        """Test that getTemplate returns the current version and available_nodes"""
        
        # Setup mock database session
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        
        # Mock the marketplace listing query
        mock_db.query.return_value.filter.return_value.first.return_value = self.mock_marketplace_listing
        
        # Mock the workflow version query in the helper function
        with patch('app.helpers.workflow_to_protobuf.get_db') as mock_get_db:
            mock_helper_db = Mock()
            mock_get_db.return_value = mock_helper_db
            mock_helper_db.query.return_value.filter.return_value.first.return_value = self.mock_workflow_version
            
            # Create request
            request = workflow_pb2.GetTemplateRequest(id="listing-123")
            context = Mock()
            
            # Call the method
            response = self.marketplace_functions.getTemplate(request, context)
            
            # Assertions
            assert response.success is True
            assert response.template is not None
            
            # Check that version is returned correctly
            assert response.template.version == "2.1.0"
            
            # Check that available_nodes are returned
            assert len(response.template.available_nodes) == 3
            
            # Parse the JSON strings back to verify content
            available_nodes = [json.loads(node) for node in response.template.available_nodes]
            assert available_nodes[0]["type"] == "ai"
            assert available_nodes[0]["name"] == "OpenAI GPT"
            assert available_nodes[1]["type"] == "data"
            assert available_nodes[2]["type"] == "api"
            
            # Check other fields
            assert response.template.workflow_url == "https://example.com/workflow"
            assert response.template.builder_url == "https://example.com/builder"

    @patch('app.services.marketplace_functions.SessionLocal')
    def test_getTemplate_handles_missing_workflow_version(self, mock_session_local):
        """Test that getTemplate handles missing workflow version gracefully"""
        
        # Setup mock database session
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        
        # Mock the marketplace listing query
        mock_db.query.return_value.filter.return_value.first.return_value = self.mock_marketplace_listing
        
        # Mock the workflow version query to return None (missing version)
        with patch('app.helpers.workflow_to_protobuf.get_db') as mock_get_db:
            mock_helper_db = Mock()
            mock_get_db.return_value = mock_helper_db
            mock_helper_db.query.return_value.filter.return_value.first.return_value = None
            
            # Create request
            request = workflow_pb2.GetTemplateRequest(id="listing-123")
            context = Mock()
            
            # Call the method
            response = self.marketplace_functions.getTemplate(request, context)
            
            # Assertions
            assert response.success is True
            assert response.template is not None
            
            # Should fallback to default version
            assert response.template.version == "1.0.0"
            
            # Should have empty available_nodes
            assert len(response.template.available_nodes) == 0

    @patch('app.services.marketplace_functions.SessionLocal')
    def test_getTemplate_with_user_id_checks_is_added(self, mock_session_local):
        """Test that getTemplate correctly sets is_added when user_id is provided"""
        
        # Setup mock database session
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        
        # Mock the marketplace listing query
        mock_db.query.return_value.filter.return_value.first.return_value = self.mock_marketplace_listing
        
        # Mock user workflow query to simulate user has used this workflow
        mock_user_workflow = Mock()
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            self.mock_marketplace_listing,  # First call for marketplace listing
            mock_user_workflow  # Second call for user workflow check
        ]
        
        # Mock the workflow version query in the helper function
        with patch('app.helpers.workflow_to_protobuf.get_db') as mock_get_db:
            mock_helper_db = Mock()
            mock_get_db.return_value = mock_helper_db
            mock_helper_db.query.return_value.filter.return_value.first.return_value = self.mock_workflow_version
            
            # Create request with user_id
            request = workflow_pb2.GetTemplateRequest(id="listing-123", user_id="user-456")
            context = Mock()
            
            # Call the method
            response = self.marketplace_functions.getTemplate(request, context)
            
            # Assertions
            assert response.success is True
            assert response.template.is_added is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])