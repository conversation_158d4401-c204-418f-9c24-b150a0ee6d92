"""
Test for _update_derived_workflows_change_status functionality.

This test verifies:
1. The function correctly identifies derived workflows
2. It properly detects changes between source and derived workflows
3. It updates the is_changes_marketplace flag appropriately
4. It handles edge cases and error conditions
"""

import pytest
from unittest.mock import MagicMock, patch
from datetime import datetime, timezone
from sqlalchemy.orm import Session

from app.services.workflow_functions import WorkflowFunctions
from app.models.workflow import Workflow
from app.utils.constants.constants import WorkflowVisibilityEnum


class TestUpdateDerivedWorkflowsChangeStatus:
    """Test cases for _update_derived_workflows_change_status method"""

    def setup_method(self):
        """Set up test fixtures"""
        self.workflow_service = WorkflowFunctions()

    def test_update_derived_workflows_change_status_basic_functionality(self):
        """Test basic functionality of updating derived workflows"""
        # Create mock database session
        mock_db = MagicMock(spec=Session)
        
        # Create source workflow
        source_workflow = MagicMock(spec=Workflow)
        source_workflow.id = "source-123"
        source_workflow.updated_at = datetime.now(timezone.utc)
        source_workflow.description = "Updated source description"
        source_workflow.workflow_url = "http://example.com/updated"
        source_workflow.builder_url = "http://example.com/builder/updated"
        source_workflow.start_nodes = [{"id": "node1", "type": "start"}]
        source_workflow.category = "automation"
        source_workflow.tags = ["updated", "template"]
        
        # Create derived workflows
        derived_workflow_1 = MagicMock(spec=Workflow)
        derived_workflow_1.id = "derived-1"
        derived_workflow_1.workflow_template_id = "source-123"
        derived_workflow_1.is_imported = True
        derived_workflow_1.is_changes_marketplace = False
        derived_workflow_1.updated_at = datetime(2024, 1, 1, tzinfo=timezone.utc)  # Older timestamp
        derived_workflow_1.description = "Old description"
        
        derived_workflow_2 = MagicMock(spec=Workflow)
        derived_workflow_2.id = "derived-2"
        derived_workflow_2.workflow_template_id = "source-123"
        derived_workflow_2.is_imported = True
        derived_workflow_2.is_changes_marketplace = False
        derived_workflow_2.updated_at = datetime.now(timezone.utc)  # Same timestamp as source
        derived_workflow_2.description = "Updated source description"  # Same as source
        derived_workflow_2.workflow_url = "http://example.com/updated"
        derived_workflow_2.builder_url = "http://example.com/builder/updated"
        derived_workflow_2.start_nodes = [{"id": "node1", "type": "start"}]
        derived_workflow_2.category = "automation"
        derived_workflow_2.tags = ["updated", "template"]
        
        # Mock database query to return derived workflows
        mock_query = MagicMock()
        mock_query.filter.return_value.all.return_value = [derived_workflow_1, derived_workflow_2]
        mock_db.query.return_value = mock_query
        
        # Mock the _detect_workflow_changes method
        with patch.object(self.workflow_service, '_detect_workflow_changes') as mock_detect:
            # First derived workflow has changes, second doesn't
            mock_detect.side_effect = [True, False]
            
            # Call the method
            self.workflow_service._update_derived_workflows_change_status(mock_db, source_workflow)
            
            # Verify database query was called correctly
            mock_db.query.assert_called_once_with(Workflow)
            mock_query.filter.assert_called_once()
            
            # Verify _detect_workflow_changes was called for each derived workflow
            assert mock_detect.call_count == 2
            mock_detect.assert_any_call(derived_workflow_1, source_workflow)
            mock_detect.assert_any_call(derived_workflow_2, source_workflow)
            
            # Verify is_changes_marketplace was updated correctly
            assert derived_workflow_1.is_changes_marketplace == True
            # derived_workflow_2 should remain False since no changes detected
            
            # Verify commit was called
            mock_db.commit.assert_called_once()

    def test_detect_workflow_changes_timestamp_comparison(self):
        """Test _detect_workflow_changes method with timestamp comparison"""
        # Create source workflow with recent timestamp
        source_workflow = MagicMock(spec=Workflow)
        source_workflow.updated_at = datetime.now(timezone.utc)
        source_workflow.description = "Source description"
        source_workflow.workflow_url = "http://example.com/source"
        source_workflow.builder_url = "http://example.com/builder/source"
        source_workflow.start_nodes = [{"id": "node1"}]
        source_workflow.category = "automation"
        source_workflow.tags = ["source"]
        
        # Create derived workflow with older timestamp
        derived_workflow = MagicMock(spec=Workflow)
        derived_workflow.updated_at = datetime(2024, 1, 1, tzinfo=timezone.utc)
        derived_workflow.description = "Source description"  # Same content
        derived_workflow.workflow_url = "http://example.com/source"
        derived_workflow.builder_url = "http://example.com/builder/source"
        derived_workflow.start_nodes = [{"id": "node1"}]
        derived_workflow.category = "automation"
        derived_workflow.tags = ["source"]
        
        # Should detect changes due to timestamp difference
        result = self.workflow_service._detect_workflow_changes(derived_workflow, source_workflow)
        assert result == True

    def test_detect_workflow_changes_content_comparison(self):
        """Test _detect_workflow_changes method with content comparison"""
        base_time = datetime.now(timezone.utc)
        
        # Create source workflow
        source_workflow = MagicMock(spec=Workflow)
        source_workflow.updated_at = base_time
        source_workflow.description = "Updated description"
        source_workflow.workflow_url = "http://example.com/updated"
        source_workflow.builder_url = "http://example.com/builder/updated"
        source_workflow.start_nodes = [{"id": "node1", "updated": True}]
        source_workflow.category = "automation"
        source_workflow.tags = ["updated"]
        
        # Create derived workflow with same timestamp but different content
        derived_workflow = MagicMock(spec=Workflow)
        derived_workflow.updated_at = base_time
        derived_workflow.description = "Old description"  # Different
        derived_workflow.workflow_url = "http://example.com/old"  # Different
        derived_workflow.builder_url = "http://example.com/builder/old"  # Different
        derived_workflow.start_nodes = [{"id": "node1", "updated": False}]  # Different
        derived_workflow.category = "old_category"  # Different
        derived_workflow.tags = ["old"]  # Different
        
        # Should detect changes due to content differences
        result = self.workflow_service._detect_workflow_changes(derived_workflow, source_workflow)
        assert result == True

    def test_detect_workflow_changes_no_changes(self):
        """Test _detect_workflow_changes method when no changes exist"""
        base_time = datetime.now(timezone.utc)
        
        # Create identical workflows
        source_workflow = MagicMock(spec=Workflow)
        source_workflow.updated_at = base_time
        source_workflow.description = "Same description"
        source_workflow.workflow_url = "http://example.com/same"
        source_workflow.builder_url = "http://example.com/builder/same"
        source_workflow.start_nodes = [{"id": "node1"}]
        source_workflow.category = "automation"
        source_workflow.tags = ["same"]
        
        derived_workflow = MagicMock(spec=Workflow)
        derived_workflow.updated_at = base_time
        derived_workflow.description = "Same description"
        derived_workflow.workflow_url = "http://example.com/same"
        derived_workflow.builder_url = "http://example.com/builder/same"
        derived_workflow.start_nodes = [{"id": "node1"}]
        derived_workflow.category = "automation"
        derived_workflow.tags = ["same"]
        
        # Should not detect changes
        result = self.workflow_service._detect_workflow_changes(derived_workflow, source_workflow)
        assert result == False

    def test_update_derived_workflows_no_derived_workflows(self):
        """Test when there are no derived workflows"""
        mock_db = MagicMock(spec=Session)
        
        source_workflow = MagicMock(spec=Workflow)
        source_workflow.id = "source-123"
        
        # Mock database query to return empty list
        mock_query = MagicMock()
        mock_query.filter.return_value.all.return_value = []
        mock_db.query.return_value = mock_query
        
        # Should not raise any errors
        self.workflow_service._update_derived_workflows_change_status(mock_db, source_workflow)
        
        # Verify commit was still called
        mock_db.commit.assert_called_once()

    def test_update_derived_workflows_error_handling(self):
        """Test error handling in _update_derived_workflows_change_status"""
        mock_db = MagicMock(spec=Session)
        
        source_workflow = MagicMock(spec=Workflow)
        source_workflow.id = "source-123"
        
        # Mock database query to raise an exception
        mock_db.query.side_effect = Exception("Database error")
        
        # Should handle the error gracefully
        self.workflow_service._update_derived_workflows_change_status(mock_db, source_workflow)
        
        # Verify rollback was called
        mock_db.rollback.assert_called_once()

    def test_detect_workflow_changes_error_handling(self):
        """Test error handling in _detect_workflow_changes"""
        # Create workflows with problematic data
        source_workflow = MagicMock(spec=Workflow)
        source_workflow.updated_at = None  # This could cause issues
        
        derived_workflow = MagicMock(spec=Workflow)
        derived_workflow.updated_at = datetime.now(timezone.utc)
        
        # Should handle the error and return True (safe default)
        result = self.workflow_service._detect_workflow_changes(derived_workflow, source_workflow)
        assert result == True

    def test_integration_with_update_workflow(self):
        """Test integration with updateWorkflow method"""
        # This test verifies that _update_derived_workflows_change_status
        # is called correctly from the updateWorkflow method
        
        mock_db = MagicMock(spec=Session)
        source_workflow = MagicMock(spec=Workflow)
        source_workflow.id = "source-123"
        source_workflow.visibility = WorkflowVisibilityEnum.PUBLIC
        
        # Mock the method to verify it's called
        with patch.object(self.workflow_service, '_update_derived_workflows_change_status') as mock_update:
            # Simulate the condition where the method should be called
            template_relevant_fields_changed = True
            
            if (template_relevant_fields_changed and 
                source_workflow.visibility == WorkflowVisibilityEnum.PUBLIC):
                self.workflow_service._update_derived_workflows_change_status(mock_db, source_workflow)
            
            # Verify the method was called
            mock_update.assert_called_once_with(mock_db, source_workflow)


def test_run_all_tests():
    """Run all tests for _update_derived_workflows_change_status functionality"""
    print("🧪 Testing _update_derived_workflows_change_status functionality...")
    
    test_instance = TestUpdateDerivedWorkflowsChangeStatus()
    test_instance.setup_method()
    
    try:
        test_instance.test_update_derived_workflows_change_status_basic_functionality()
        print("✅ Basic functionality test passed")
        
        test_instance.test_detect_workflow_changes_timestamp_comparison()
        print("✅ Timestamp comparison test passed")
        
        test_instance.test_detect_workflow_changes_content_comparison()
        print("✅ Content comparison test passed")
        
        test_instance.test_detect_workflow_changes_no_changes()
        print("✅ No changes detection test passed")
        
        test_instance.test_update_derived_workflows_no_derived_workflows()
        print("✅ No derived workflows test passed")
        
        test_instance.test_update_derived_workflows_error_handling()
        print("✅ Error handling test passed")
        
        test_instance.test_detect_workflow_changes_error_handling()
        print("✅ Change detection error handling test passed")
        
        test_instance.test_integration_with_update_workflow()
        print("✅ Integration test passed")
        
        print("\n🎉 All tests passed! The _update_derived_workflows_change_status functionality is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False


if __name__ == "__main__":
    test_run_all_tests()