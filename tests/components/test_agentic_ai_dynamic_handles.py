"""
Tests for AgenticAI component DynamicHandleInput implementation
"""

import pytest
from unittest.mock import Mock, patch
from typing import Dict, Any

from app.components.ai.agentic_ai import AgenticAI
from app.models.workflow_builder.components import DynamicHandleInput
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult


class TestAgenticAIDynamicHandles:
    """Test AgenticAI component with DynamicHandleInput for workflow components"""

    def test_agentic_ai_has_workflow_components_input(self):
        """Test that AgenticAI component has workflow_components DynamicHandleInput"""
        component = AgenticAI()
        
        # Find the workflow_components input
        workflow_components_input = None
        for input_def in component.inputs:
            if input_def.name == "workflow_components":
                workflow_components_input = input_def
                break
        
        # Should have workflow_components input
        assert workflow_components_input is not None, "AgenticAI should have workflow_components input"
        
        # Should be DynamicHandleInput type
        assert isinstance(workflow_components_input, DynamicHandleInput), "workflow_components should be DynamicHandleInput"
        
        # Check configuration
        assert workflow_components_input.display_name == "Workflow Components (Tools)"
        assert workflow_components_input.base_name == "tool"
        assert workflow_components_input.base_display_name == "Tool"
        assert workflow_components_input.min_handles == 0
        assert workflow_components_input.max_handles == 10
        assert workflow_components_input.default_handles == 0
        assert workflow_components_input.input_types == ["Any"]
        assert workflow_components_input.allow_direct_input == False
        assert workflow_components_input.show_add_button == True
        assert workflow_components_input.show_remove_button == True

    def test_agentic_ai_no_manual_tools_input(self):
        """Test that AgenticAI component no longer has manual tools input"""
        component = AgenticAI()
        
        # Should not have tools input
        tools_input = None
        for input_def in component.inputs:
            if input_def.name == "tools":
                tools_input = input_def
                break
        
        assert tools_input is None, "AgenticAI should not have manual tools input"

    def test_dynamic_handle_generation_performance(self):
        """Test that dynamic handle generation meets performance requirements"""
        import time
        
        component = AgenticAI()
        
        # Find the workflow_components input
        workflow_components_input = None
        for input_def in component.inputs:
            if input_def.name == "workflow_components":
                workflow_components_input = input_def
                break
        
        assert workflow_components_input is not None
        
        # Test handle generation performance
        start_time = time.time()
        
        # Simulate generating maximum handles
        for i in range(workflow_components_input.max_handles):
            handle_name = f"{workflow_components_input.base_name}_{i+1}"
            handle_display_name = f"{workflow_components_input.base_display_name} {i+1}"
            # Simulate handle creation overhead
            pass
        
        end_time = time.time()
        generation_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should generate handles in less than 5ms per handle
        max_time_per_handle = 5  # ms
        max_total_time = max_time_per_handle * workflow_components_input.max_handles
        
        assert generation_time < max_total_time, f"Handle generation took {generation_time:.2f}ms, should be < {max_total_time}ms"

    def test_get_agent_config_extracts_workflow_components(self):
        """Test that get_agent_config extracts connected workflow components"""
        component = AgenticAI()
        
        # Create mock context with connected workflow components
        context = Mock(spec=WorkflowContext)
        context.current_node_id = "agentic-ai-1"
        context.node_outputs = {
            "agentic-ai-1": {
                "tool_1": {
                    "component_id": "data-processor-1",
                    "component_type": "DataProcessor",
                    "component_name": "Data Processing Tool",
                    "component_schema": {
                        "name": "process_data",
                        "description": "Process data",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "data": {"type": "string"}
                            }
                        }
                    }
                },
                "tool_2": {
                    "component_id": "text-analyzer-1", 
                    "component_type": "TextAnalyzer",
                    "component_name": "Text Analysis Tool",
                    "component_schema": {
                        "name": "analyze_text",
                        "description": "Analyze text",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "text": {"type": "string"}
                            }
                        }
                    }
                }
            }
        }
        
        # Mock get_input_value to return default values
        def mock_get_input_value(name, ctx, default=None):
            defaults = {
                "description": "Test agent",
                "execution_type": "response",
                "query": "Test query",
                "system_message": "Test system message",
                "termination_condition": "",
                "max_tokens": 1000,
                "model_provider": "OpenAI",
                "model_name": "gpt-4",
                "temperature": 0.7,
                "name": "Test Agent",
                "id": "agentic-ai-1"
            }
            return defaults.get(name, default)
        
        component.get_input_value = mock_get_input_value
        
        # Get agent config
        agent_config = component.get_agent_config(context)
        
        # Should include tools from connected workflow components
        assert "tools" in agent_config
        assert len(agent_config["tools"]) == 2
        
        # Check first tool
        tool1 = agent_config["tools"][0]
        assert tool1["tool_type"] == "workflow_component"
        assert tool1["component"]["component_id"] == "data-processor-1"
        assert tool1["component"]["component_type"] == "DataProcessor"
        assert tool1["component"]["component_name"] == "Data Processing Tool"
        assert "component_schema" in tool1["component"]
        
        # Check second tool
        tool2 = agent_config["tools"][1]
        assert tool2["tool_type"] == "workflow_component"
        assert tool2["component"]["component_id"] == "text-analyzer-1"
        assert tool2["component"]["component_type"] == "TextAnalyzer"
        assert tool2["component"]["component_name"] == "Text Analysis Tool"
        assert "component_schema" in tool2["component"]

    def test_get_agent_config_handles_mcp_components(self):
        """Test that get_agent_config properly handles MCP marketplace components"""
        component = AgenticAI()
        
        # Create mock context with MCP component
        context = Mock(spec=WorkflowContext)
        context.current_node_id = "agentic-ai-1"
        context.node_outputs = {
            "agentic-ai-1": {
                "tool_1": {
                    "component_id": "mcp-translate-1",
                    "component_type": "MCPMarketplace",
                    "component_name": "Translation Tool",
                    "component_schema": {
                        "name": "translate",
                        "description": "Translate text between languages",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "text": {"type": "string"},
                                "target_language": {"type": "string"}
                            }
                        }
                    },
                    "mcp_metadata": {
                        "server_url": "http://localhost:8000",
                        "tool_name": "translate"
                    }
                }
            }
        }
        
        # Mock get_input_value
        def mock_get_input_value(name, ctx, default=None):
            defaults = {
                "description": "Test agent",
                "execution_type": "response", 
                "query": "Test query",
                "system_message": "Test system message",
                "termination_condition": "",
                "max_tokens": 1000,
                "model_provider": "OpenAI",
                "model_name": "gpt-4",
                "temperature": 0.7,
                "name": "Test Agent",
                "id": "agentic-ai-1"
            }
            return defaults.get(name, default)
        
        component.get_input_value = mock_get_input_value
        
        # Get agent config
        agent_config = component.get_agent_config(context)
        
        # Should include MCP tool
        assert "tools" in agent_config
        assert len(agent_config["tools"]) == 1
        
        tool = agent_config["tools"][0]
        assert tool["tool_type"] == "workflow_component"
        assert tool["component"]["component_type"] == "MCPMarketplace"
        assert "mcp_metadata" in tool["component"]
        assert tool["component"]["mcp_metadata"]["server_url"] == "http://localhost:8000"

    def test_get_agent_config_performance(self):
        """Test that get_agent_config generation meets performance requirements"""
        import time
        
        component = AgenticAI()
        
        # Create mock context with multiple tools
        context = Mock(spec=WorkflowContext)
        context.current_node_id = "agentic-ai-1"
        
        # Create 10 connected tools
        tools = {}
        for i in range(10):
            tools[f"tool_{i+1}"] = {
                "component_id": f"component-{i+1}",
                "component_type": "DataProcessor",
                "component_name": f"Tool {i+1}",
                "component_schema": {
                    "name": f"process_{i+1}",
                    "description": f"Process data {i+1}",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "string"}
                        }
                    }
                }
            }
        
        context.node_outputs = {"agentic-ai-1": tools}
        
        # Mock get_input_value
        def mock_get_input_value(name, ctx, default=None):
            defaults = {
                "description": "Test agent",
                "execution_type": "response",
                "query": "Test query", 
                "system_message": "Test system message",
                "termination_condition": "",
                "max_tokens": 1000,
                "model_provider": "OpenAI",
                "model_name": "gpt-4",
                "temperature": 0.7,
                "name": "Test Agent",
                "id": "agentic-ai-1"
            }
            return defaults.get(name, default)
        
        component.get_input_value = mock_get_input_value
        
        # Measure config generation time
        start_time = time.time()
        agent_config = component.get_agent_config(context)
        end_time = time.time()
        
        generation_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should generate config in less than 50ms
        assert generation_time < 50, f"Config generation took {generation_time:.2f}ms, should be < 50ms"
        
        # Should have extracted all 10 tools
        assert len(agent_config["tools"]) == 10

    def test_agent_config_format_matches_orchestration_expectations(self):
        """Test that agent config format matches orchestration engine expectations"""
        component = AgenticAI()

        # Create mock context with connected tools
        context = Mock(spec=WorkflowContext)
        context.current_node_id = "agentic-ai-1"
        context.node_outputs = {
            "agentic-ai-1": {
                "tool_1": {
                    "component_id": "data-processor-1",
                    "component_type": "DataProcessor",
                    "component_name": "Data Processing Tool",
                    "component_schema": {
                        "name": "process_data",
                        "description": "Process data",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "data": {"type": "string"}
                            }
                        }
                    }
                }
            }
        }

        # Mock get_input_value
        def mock_get_input_value(name, ctx, default=None):
            defaults = {
                "description": "Test agent for data processing",
                "execution_type": "response",
                "query": "Process the provided data",
                "system_message": "You are a data processing assistant",
                "termination_condition": "",
                "max_tokens": 2000,
                "model_provider": "OpenAI",
                "model_name": "gpt-4",
                "temperature": 0.5,
                "name": "Data Processing Agent",
                "id": "agent-data-processor-1"
            }
            return defaults.get(name, default)

        component.get_input_value = mock_get_input_value

        # Get agent config
        agent_config = component.get_agent_config(context)

        # Verify orchestration engine expected format
        assert "id" in agent_config
        assert "name" in agent_config
        assert "description" in agent_config
        assert "agent_type" in agent_config
        assert "execution_type" in agent_config
        assert "query" in agent_config
        assert "system_message" in agent_config
        assert "tools" in agent_config  # Key field for orchestration
        assert "agent_config" in agent_config  # Nested model config

        # Verify agent_type is always "component"
        assert agent_config["agent_type"] == "component"

        # Verify tools format matches schema
        assert isinstance(agent_config["tools"], list)
        assert len(agent_config["tools"]) == 1

        tool = agent_config["tools"][0]
        assert tool["tool_type"] == "workflow_component"
        assert "component" in tool
        assert "component_id" in tool["component"]
        assert "component_type" in tool["component"]
        assert "component_name" in tool["component"]
        assert "component_schema" in tool["component"]

        # Verify nested agent_config structure
        nested_config = agent_config["agent_config"]
        assert "model_provider" in nested_config
        assert "model_name" in nested_config
        assert "temperature" in nested_config
        assert "max_tokens" in nested_config

    def test_agent_config_tool_schema_inclusion(self):
        """Test that tool schemas are properly included in agent config"""
        component = AgenticAI()

        # Create mock context with MCP component
        context = Mock(spec=WorkflowContext)
        context.current_node_id = "agentic-ai-1"
        context.node_outputs = {
            "agentic-ai-1": {
                "tool_1": {
                    "component_id": "mcp-translate-1",
                    "component_type": "MCPMarketplace",
                    "component_name": "Translation Tool",
                    "component_schema": {
                        "name": "translate",
                        "description": "Translate text between languages",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "text": {"type": "string", "description": "Text to translate"},
                                "target_language": {"type": "string", "description": "Target language code"}
                            },
                            "required": ["text", "target_language"]
                        }
                    },
                    "mcp_metadata": {
                        "server_url": "http://localhost:8000",
                        "tool_name": "translate"
                    }
                }
            }
        }

        # Mock get_input_value
        def mock_get_input_value(name, ctx, default=None):
            defaults = {
                "description": "Translation agent",
                "execution_type": "response",
                "query": "Translate the text",
                "system_message": "You are a translation assistant",
                "termination_condition": "",
                "max_tokens": 1000,
                "model_provider": "OpenAI",
                "model_name": "gpt-4",
                "temperature": 0.3,
                "name": "Translation Agent",
                "id": "agent-translator-1"
            }
            return defaults.get(name, default)

        component.get_input_value = mock_get_input_value

        # Get agent config
        agent_config = component.get_agent_config(context)

        # Verify tool schema is included
        tool = agent_config["tools"][0]
        assert "component_schema" in tool["component"]

        schema = tool["component"]["component_schema"]
        assert schema["name"] == "translate"
        assert "parameters" in schema
        assert "properties" in schema["parameters"]
        assert "text" in schema["parameters"]["properties"]
        assert "target_language" in schema["parameters"]["properties"]

        # Verify MCP metadata is preserved
        assert "mcp_metadata" in tool["component"]
        mcp_metadata = tool["component"]["mcp_metadata"]
        assert mcp_metadata["server_url"] == "http://localhost:8000"
        assert mcp_metadata["tool_name"] == "translate"

    def test_tool_schema_generation_integration(self):
        """Test that tool schema generation is properly integrated with AgenticAI"""
        component = AgenticAI()

        # Create mock context with component that needs schema generation
        context = Mock(spec=WorkflowContext)
        context.current_node_id = "agentic-ai-1"
        context.node_outputs = {
            "agentic-ai-1": {
                "tool_1": {
                    "component_id": "data-processor-1",
                    "component_type": "DataProcessor",
                    "component_name": "Data Processing Tool",
                    "inputs": [
                        {
                            "name": "input_data",
                            "display_name": "Input Data",
                            "input_type": "string",
                            "required": True,
                            "info": "Data to be processed"
                        },
                        {
                            "name": "processing_mode",
                            "display_name": "Processing Mode",
                            "input_type": "dropdown",
                            "required": False,
                            "options": ["batch", "stream"],
                            "value": "batch",
                            "info": "How to process the data"
                        }
                    ]
                    # Note: No component_schema provided - should be generated automatically
                }
            }
        }

        # Mock context.log method
        context.log = Mock()

        # Mock get_input_value
        def mock_get_input_value(name, ctx, default=None):
            defaults = {
                "description": "Test agent with schema generation",
                "execution_type": "response",
                "query": "Process the provided data",
                "system_message": "You are a data processing assistant",
                "termination_condition": "",
                "max_tokens": 2000,
                "model_provider": "OpenAI",
                "model_name": "gpt-4",
                "temperature": 0.5,
                "name": "Schema Generation Test Agent",
                "id": "agent-schema-test-1"
            }
            return defaults.get(name, default)

        component.get_input_value = mock_get_input_value

        # Get agent config - this should trigger schema generation
        agent_config = component.get_agent_config(context)

        # Verify that schema was generated
        assert len(agent_config["tools"]) == 1
        tool = agent_config["tools"][0]

        # Verify tool structure
        assert tool["tool_type"] == "workflow_component"
        assert "component" in tool
        assert "component_schema" in tool["component"]

        # Verify the generated schema is AutoGen-compatible
        schema = tool["component"]["component_schema"]
        assert "name" in schema
        assert "description" in schema
        assert "parameters" in schema

        # Verify schema structure
        assert schema["name"] == "data_processing_tool"
        assert "Data Processing Tool" in schema["description"]
        assert schema["parameters"]["type"] == "object"
        assert "properties" in schema["parameters"]
        assert "required" in schema["parameters"]
        assert schema["parameters"]["additionalProperties"] == False

        # Verify parameters were correctly converted
        properties = schema["parameters"]["properties"]
        assert "input_data" in properties
        assert "processing_mode" in properties

        # Verify required fields
        assert "input_data" in schema["parameters"]["required"]
        assert "processing_mode" not in schema["parameters"]["required"]

        # Verify parameter details
        input_data_param = properties["input_data"]
        assert input_data_param["type"] == "string"
        assert input_data_param["description"] == "Data to be processed"
        assert input_data_param["title"] == "Input Data"

        processing_mode_param = properties["processing_mode"]
        assert processing_mode_param["type"] == "string"
        assert processing_mode_param["enum"] == ["batch", "stream"]

    def test_backward_compatibility_no_connected_tools(self):
        """Test backward compatibility when no tools are connected"""
        component = AgenticAI()
        
        # Create mock context with no connected tools
        context = Mock(spec=WorkflowContext)
        context.current_node_id = "agentic-ai-1"
        context.node_outputs = {"agentic-ai-1": {}}
        
        # Mock get_input_value
        def mock_get_input_value(name, ctx, default=None):
            defaults = {
                "description": "Test agent",
                "execution_type": "response",
                "query": "Test query",
                "system_message": "Test system message", 
                "termination_condition": "",
                "max_tokens": 1000,
                "model_provider": "OpenAI",
                "model_name": "gpt-4",
                "temperature": 0.7,
                "name": "Test Agent",
                "id": "agentic-ai-1"
            }
            return defaults.get(name, default)
        
        component.get_input_value = mock_get_input_value
        
        # Get agent config
        agent_config = component.get_agent_config(context)
        
        # Should have empty tools list
        assert "tools" in agent_config
        assert agent_config["tools"] == []
        
        # Should still have all other required fields
        assert agent_config["id"] == "agentic-ai-1"
        assert agent_config["name"] == "Test Agent"
        assert agent_config["query"] == "Test query"
