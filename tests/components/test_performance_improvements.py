"""
Performance improvement tests for legacy code cleanup
"""

import pytest
import time
import asyncio
from typing import Dict, Any

from app.components.ai.agentic_ai import AgenticAI
from app.models.workflow_builder.context import WorkflowContext


class TestPerformanceImprovements:
    """Test performance improvements after legacy code cleanup"""

    def test_component_initialization_performance(self):
        """Test that component initialization is fast after cleanup"""
        # Measure initialization time for multiple components
        start_time = time.time()
        
        components = []
        for i in range(100):
            component = AgenticAI()
            components.append(component)
        
        end_time = time.time()
        total_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should initialize 100 components in less than 100ms
        assert total_time < 100, f"Component initialization took {total_time:.2f}ms for 100 components, should be < 100ms"
        
        # Verify all components are properly initialized
        for component in components:
            assert component.name == "AgenticAI"
            assert component.display_name == "AI Agent Executor"
            assert len(component.inputs) > 0

    def test_tool_extraction_performance(self):
        """Test that tool extraction is optimized after cleanup"""
        component = AgenticAI()
        
        # Create context with multiple tools
        context = WorkflowContext(
            workflow_id="perf-test-workflow",
            execution_id="perf-test-execution"
        )
        
        # Mock node inputs with 10 tools
        tool_inputs = {}
        for i in range(10):
            tool_inputs[f"tool_{i+1}"] = {
                "component_id": f"comp-{i+1}",
                "component_type": f"TestComponent{i+1}",
                "component_name": f"Test Component {i+1}",
                "component_schema": {
                    "name": f"test_tool_{i+1}",
                    "description": f"Test tool {i+1}",
                    "parameters": {
                        "type": "object",
                        "properties": {"input": {"type": "string"}},
                        "required": ["input"]
                    }
                }
            }
        
        context.node_outputs = {"test-node": tool_inputs}
        context.current_node_id = "test-node"
        
        # Measure tool extraction time
        start_time = time.time()
        
        for _ in range(50):  # Extract tools 50 times
            tools = component._extract_connected_workflow_components(context)
        
        end_time = time.time()
        extraction_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should extract tools in less than 50ms for 50 iterations
        assert extraction_time < 50, f"Tool extraction took {extraction_time:.2f}ms for 50 iterations, should be < 50ms"
        
        # Verify tools are extracted correctly
        tools = component._extract_connected_workflow_components(context)
        assert len(tools) == 10, "Should extract all 10 tools"
        
        for i, tool in enumerate(tools):
            assert tool["tool_type"] == "workflow_component"
            assert tool["component"]["component_id"] == f"comp-{i+1}"

    def test_input_processing_performance(self):
        """Test that input processing is optimized"""
        component = AgenticAI()
        
        # Create context
        context = WorkflowContext(
            workflow_id="input-perf-test",
            execution_id="input-perf-execution"
        )
        
        # Mock node inputs
        context.node_outputs = {
            "test-node": {
                "query": "Test query",
                "execution_type": "response",
                "model_provider": "OpenAI",
                "model_name": "gpt-4-turbo",
                "temperature": 0.7,
                "description": "Test description",
                "system_message": "Test system message"
            }
        }
        context.current_node_id = "test-node"
        
        # Measure input value retrieval time
        start_time = time.time()
        
        for _ in range(100):
            # Get multiple input values
            query = component.get_input_value("query", context)
            execution_type = component.get_input_value("execution_type", context)
            model_provider = component.get_input_value("model_provider", context)
            model_name = component.get_input_value("model_name", context)
            temperature = component.get_input_value("temperature", context)
        
        end_time = time.time()
        processing_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should process inputs in less than 20ms for 100 iterations
        assert processing_time < 20, f"Input processing took {processing_time:.2f}ms for 100 iterations, should be < 20ms"

    def test_agent_config_generation_performance(self):
        """Test that agent config generation is optimized"""
        component = AgenticAI()
        
        # Create context with comprehensive inputs
        context = WorkflowContext(
            workflow_id="config-perf-test",
            execution_id="config-perf-execution"
        )
        
        # Mock comprehensive node inputs
        context.node_outputs = {
            "test-node": {
                "query": "Generate a comprehensive report",
                "execution_type": "response",
                "description": "Test agent for performance testing",
                "system_message": "You are a helpful assistant",
                "model_provider": "OpenAI",
                "model_name": "gpt-4-turbo",
                "temperature": 0.7,
                "max_tokens": 2000,
                "tool_1": {
                    "component_id": "data-processor",
                    "component_type": "DataProcessor",
                    "component_name": "Data Processing Tool"
                },
                "tool_2": {
                    "component_id": "api-caller",
                    "component_type": "APICaller", 
                    "component_name": "API Calling Tool"
                }
            }
        }
        context.current_node_id = "test-node"
        
        # Measure agent config generation time
        start_time = time.time()
        
        for _ in range(25):  # Generate config 25 times
            agent_config = component.get_agent_config(context)
        
        end_time = time.time()
        generation_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should generate config in less than 50ms for 25 iterations
        assert generation_time < 50, f"Agent config generation took {generation_time:.2f}ms for 25 iterations, should be < 50ms"
        
        # Verify config is generated correctly
        agent_config = component.get_agent_config(context)
        assert "id" in agent_config
        assert "name" in agent_config
        assert "tools" in agent_config
        assert len(agent_config["tools"]) == 2

    def test_memory_efficiency(self):
        """Test that memory usage is efficient after cleanup"""
        import sys
        
        # Measure memory usage of a single component
        component = AgenticAI()
        component_size = sys.getsizeof(component)
        
        # Should use reasonable memory (less than 5KB per component)
        assert component_size < 5120, f"Component uses {component_size} bytes, should be < 5KB"
        
        # Test memory usage with multiple components
        components = []
        for i in range(20):
            components.append(AgenticAI())
        
        # Calculate total memory usage
        total_size = sum(sys.getsizeof(comp) for comp in components)
        average_size = total_size / len(components)
        
        # Average should still be reasonable
        assert average_size < 5120, f"Average component memory usage is {average_size:.2f} bytes, should be < 5KB"

    def test_input_definition_efficiency(self):
        """Test that input definitions are efficiently structured"""
        component = AgenticAI()
        
        # Measure time to access input definitions
        start_time = time.time()
        
        for _ in range(1000):
            inputs = component.inputs
            input_names = [inp.name for inp in inputs]
            input_count = len(inputs)
        
        end_time = time.time()
        access_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should access input definitions quickly
        assert access_time < 10, f"Input definition access took {access_time:.2f}ms for 1000 iterations, should be < 10ms"
        
        # Verify input structure is reasonable
        inputs = component.inputs
        assert len(inputs) < 20, f"Component has {len(inputs)} inputs, should be < 20 for usability"
        
        # Check that all inputs have required attributes
        for inp in inputs:
            assert hasattr(inp, 'name'), "Input should have name attribute"
            assert hasattr(inp, 'display_name'), "Input should have display_name attribute"
            assert hasattr(inp, 'info'), "Input should have info attribute"

    def test_workflow_components_handle_efficiency(self):
        """Test that workflow_components dynamic handle is efficient"""
        component = AgenticAI()
        
        # Find the workflow_components input
        workflow_components_input = None
        for inp in component.inputs:
            if inp.name == "workflow_components":
                workflow_components_input = inp
                break
        
        assert workflow_components_input is not None, "Should have workflow_components input"
        
        # Test handle configuration access performance
        start_time = time.time()
        
        for _ in range(500):
            base_name = workflow_components_input.base_name
            max_handles = workflow_components_input.max_handles
            min_handles = workflow_components_input.min_handles
            allow_direct_input = workflow_components_input.allow_direct_input
        
        end_time = time.time()
        access_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should access handle configuration quickly
        assert access_time < 5, f"Handle configuration access took {access_time:.2f}ms for 500 iterations, should be < 5ms"
        
        # Verify configuration is optimal
        assert workflow_components_input.max_handles == 10, "Should support up to 10 tool handles"
        assert workflow_components_input.allow_direct_input == False, "Should not allow direct input for tools"

    def test_error_handling_efficiency(self):
        """Test that error handling is efficient"""
        component = AgenticAI()
        
        # Create context with missing data
        context = WorkflowContext(
            workflow_id="error-test",
            execution_id="error-test-execution"
        )
        
        # Test error handling performance
        start_time = time.time()
        
        for _ in range(100):
            # Try to get non-existent input values
            result1 = component.get_input_value("non_existent_input", context, "default")
            result2 = component.get_input_value("another_missing_input", context)
            
            # Try to extract tools from empty context
            tools = component._extract_connected_workflow_components(context)
        
        end_time = time.time()
        error_handling_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Error handling should be fast
        assert error_handling_time < 30, f"Error handling took {error_handling_time:.2f}ms for 100 iterations, should be < 30ms"
        
        # Verify error handling works correctly
        result = component.get_input_value("missing_input", context, "default_value")
        assert result == "default_value", "Should return default value for missing input"
        
        tools = component._extract_connected_workflow_components(context)
        assert tools == [], "Should return empty list for missing tools"
