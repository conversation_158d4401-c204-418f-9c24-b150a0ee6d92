"""
Test suite for template variable preprocessing functionality in workflow schema converter.

This test suite verifies that the template variable preprocessing correctly converts
{variable_name} syntax to ${variable_name} format while preserving existing ${variable_name}
patterns and handling various data types and edge cases.
"""

import pytest
from unittest.mock import patch, MagicMock
from app.services.workflow_builder.workflow_schema_converter import (
    preprocess_template_variables,
    _process_string_template_variables,
    detect_template_variables,
    validate_template_variables,
    validate_workflow_template_variables,
)


class TestTemplateVariablePreprocessing:
    """Test cases for template variable preprocessing functionality."""

    def test_simple_string_conversion(self):
        """Test basic {variable_name} to ${variable_name} conversion."""
        # Test single variable
        result = preprocess_template_variables("Hello {user_name}!", "test_field")
        assert result == "Hello ${user_name}!"
        
        # Test multiple variables
        result = preprocess_template_variables("Hello {user_name}, your email is {email}", "test_field")
        assert result == "Hello ${user_name}, your email is ${email}"
        
        # Test variable at start and end
        result = preprocess_template_variables("{greeting} {user_name} {suffix}", "test_field")
        assert result == "${greeting} ${user_name} ${suffix}"

    def test_preserve_existing_dollar_syntax(self):
        """Test that existing ${variable_name} syntax is preserved."""
        # Test existing dollar syntax is unchanged
        result = preprocess_template_variables("Hello ${user_name}!", "test_field")
        assert result == "Hello ${user_name}!"
        
        # Test mixed syntax conversion
        result = preprocess_template_variables("Hello ${existing_var} and {new_var}", "test_field")
        assert result == "Hello ${existing_var} and ${new_var}"

    def test_nested_dictionary_processing(self):
        """Test template variable preprocessing in nested dictionaries."""
        input_dict = {
            "message": "Hello {user_name}!",
            "config": {
                "greeting": "Welcome {user_name}",
                "settings": {
                    "email": "{user_email}",
                    "phone": "${existing_phone}"
                }
            },
            "static_value": "no variables here"
        }
        
        expected = {
            "message": "Hello ${user_name}!",
            "config": {
                "greeting": "Welcome ${user_name}",
                "settings": {
                    "email": "${user_email}",
                    "phone": "${existing_phone}"
                }
            },
            "static_value": "no variables here"
        }
        
        result = preprocess_template_variables(input_dict, "test_dict")
        assert result == expected

    def test_list_processing(self):
        """Test template variable preprocessing in lists."""
        input_list = [
            "Hello {user_name}",
            {"message": "Welcome {user_name}"},
            ["Nested {variable}", "Another {var}"],
            "Static string",
            123,
            None
        ]
        
        expected = [
            "Hello ${user_name}",
            {"message": "Welcome ${user_name}"},
            ["Nested ${variable}", "Another ${var}"],
            "Static string",
            123,
            None
        ]
        
        result = preprocess_template_variables(input_list, "test_list")
        assert result == expected

    def test_edge_cases(self):
        """Test edge cases and malformed patterns."""
        # Empty braces
        result = preprocess_template_variables("Hello {}", "test_field")
        assert result == "Hello {}"  # Should remain unchanged (empty variable name)

        # Whitespace in braces
        result = preprocess_template_variables("Hello { user_name }", "test_field")
        assert result == "Hello ${ user_name }"  # Should convert with whitespace preserved

        # Nested braces (malformed)
        result = preprocess_template_variables("Hello {user_{name}}", "test_field")
        assert result == "Hello ${user_{name}}"  # Should convert outer braces

        # Escaped braces (should not convert)
        result = preprocess_template_variables("Hello \\{user_name\\}", "test_field")
        assert result == "Hello \\{user_name\\}"  # Should remain unchanged

    def test_non_string_types(self):
        """Test that non-string types are handled correctly."""
        # None
        result = preprocess_template_variables(None, "test_field")
        assert result is None
        
        # Numbers
        result = preprocess_template_variables(123, "test_field")
        assert result == 123
        
        # Booleans
        result = preprocess_template_variables(True, "test_field")
        assert result is True
        
        # Empty string
        result = preprocess_template_variables("", "test_field")
        assert result == ""

    def test_complex_mixed_content(self):
        """Test complex scenarios with mixed content types."""
        complex_input = {
            "api_config": {
                "url": "https://api.example.com/{endpoint}",
                "headers": {
                    "Authorization": "Bearer {api_token}",
                    "Content-Type": "application/json"
                },
                "params": [
                    {"name": "user_id", "value": "{user_id}"},
                    {"name": "format", "value": "json"}
                ]
            },
            "message_template": "Hello {user_name}, your balance is ${current_balance}",
            "retry_count": 3,
            "enabled": True
        }
        
        expected = {
            "api_config": {
                "url": "https://api.example.com/${endpoint}",
                "headers": {
                    "Authorization": "Bearer ${api_token}",
                    "Content-Type": "application/json"
                },
                "params": [
                    {"name": "user_id", "value": "${user_id}"},
                    {"name": "format", "value": "json"}
                ]
            },
            "message_template": "Hello ${user_name}, your balance is ${current_balance}",
            "retry_count": 3,
            "enabled": True
        }
        
        result = preprocess_template_variables(complex_input, "complex_test")
        assert result == expected

    @patch('app.services.workflow_builder.workflow_schema_converter.logger')
    def test_logging_behavior(self, mock_logger):
        """Test that appropriate logging occurs during preprocessing."""
        # Test successful conversion logging
        preprocess_template_variables("Hello {user_name}!", "test_field")
        mock_logger.debug.assert_called()
        mock_logger.info.assert_called()
        
        # Reset mock
        mock_logger.reset_mock()
        
        # Test no conversion (no logging)
        preprocess_template_variables("Hello world!", "test_field")
        mock_logger.debug.assert_not_called()
        mock_logger.info.assert_not_called()

    def test_error_handling(self):
        """Test error handling in preprocessing."""
        # Create a mock dictionary that raises an exception when iterating
        class ErrorDict(dict):
            def items(self):
                raise ValueError("Test error during iteration")

        error_dict = ErrorDict({"test": "value"})

        # Should not raise exception, should return original value
        with patch('app.services.workflow_builder.workflow_schema_converter.logger') as mock_logger:
            result = preprocess_template_variables(error_dict, "error_test")
            assert result is error_dict
            mock_logger.error.assert_called()

    def test_string_processing_function(self):
        """Test the internal string processing function directly."""
        # Test basic conversion
        result = _process_string_template_variables("Hello {user_name}!", "test")
        assert result == "Hello ${user_name}!"
        
        # Test no variables
        result = _process_string_template_variables("Hello world!", "test")
        assert result == "Hello world!"
        
        # Test non-string input
        result = _process_string_template_variables(123, "test")
        assert result == 123

    def test_performance_with_large_data(self):
        """Test preprocessing performance with larger data structures."""
        # Create a large nested structure
        large_data = {
            f"field_{i}": {
                "message": f"Hello {{user_{i}}}!",
                "config": {
                    "value": f"{{config_value_{i}}}",
                    "nested": [f"{{item_{j}}}" for j in range(10)]
                }
            }
            for i in range(100)
        }
        
        # Should complete without timeout or memory issues
        result = preprocess_template_variables(large_data, "performance_test")
        
        # Verify some conversions occurred
        assert "${user_0}" in result["field_0"]["message"]
        assert "${config_value_50}" in result["field_50"]["config"]["value"]
        assert "${item_5}" in result["field_99"]["config"]["nested"][5]


class TestTemplateVariableDetection:
    """Test cases for template variable detection functionality."""

    def test_detect_simple_variables(self):
        """Test detection of simple template variables."""
        data = "Hello {user_name}, your email is ${user_email}"
        variables = detect_template_variables(data, "test_string")

        assert len(variables) == 2

        # Check first variable
        var1 = next(v for v in variables if v["variable_name"] == "user_name")
        assert var1["syntax"] == "{user_name}"
        assert var1["location"] == "test_string"

        # Check second variable
        var2 = next(v for v in variables if v["variable_name"] == "user_email")
        assert var2["syntax"] == "${user_email}"
        assert var2["location"] == "test_string"

    def test_detect_nested_structure_variables(self):
        """Test detection in nested data structures."""
        data = {
            "config": {
                "message": "Hello {user_name}",
                "settings": {
                    "email": "${user_email}",
                    "phone": "{user_phone}"
                }
            },
            "items": [
                "Item {item_id}",
                {"name": "Product {product_name}"}
            ]
        }

        variables = detect_template_variables(data, "root")
        assert len(variables) == 5

        # Check locations are correct
        locations = [v["location"] for v in variables]
        assert "root.config.message" in locations
        assert "root.config.settings.email" in locations
        assert "root.config.settings.phone" in locations
        assert "root.items[0]" in locations
        assert "root.items[1].name" in locations

    def test_detect_no_variables(self):
        """Test detection when no variables are present."""
        data = {
            "message": "Hello world",
            "config": {"value": "static"},
            "items": ["item1", "item2"]
        }

        variables = detect_template_variables(data)
        assert len(variables) == 0

    def test_detect_malformed_variables(self):
        """Test detection handles malformed patterns correctly."""
        data = {
            "empty_braces": "Hello {}",
            "escaped": "Hello \\{user_name\\}",
            "nested_braces": "Hello {user_{name}}",
            "whitespace": "Hello { user_name }"
        }

        variables = detect_template_variables(data)

        # Should only detect nested_braces and whitespace
        # Note: nested braces will be detected as "user_{name" (stops at first })
        assert len(variables) == 2
        variable_names = [v["variable_name"] for v in variables]
        assert "user_{name" in variable_names  # Regex stops at first }
        assert " user_name " in variable_names  # Whitespace is preserved in detection


class TestTemplateVariableValidation:
    """Test cases for template variable validation functionality."""

    def test_validate_valid_variables(self):
        """Test validation of correctly formatted variables."""
        variables = [
            {
                "variable_name": "user_name",
                "syntax": "{user_name}",
                "location": "test.field1",
                "value": "Hello {user_name}"
            },
            {
                "variable_name": "user_email",
                "syntax": "${user_email}",
                "location": "test.field2",
                "value": "Email: ${user_email}"
            }
        ]

        result = validate_template_variables(variables)

        assert result["valid"] is True
        assert len(result["errors"]) == 0
        assert result["variable_summary"]["total_count"] == 2
        assert len(result["variable_summary"]["unique_variables"]) == 2

    def test_validate_invalid_variable_names(self):
        """Test validation catches invalid variable names."""
        variables = [
            {
                "variable_name": "123invalid",  # Starts with number
                "syntax": "{123invalid}",
                "location": "test.field1",
                "value": "Hello {123invalid}"
            },
            {
                "variable_name": "invalid-name",  # Contains hyphen
                "syntax": "{invalid-name}",
                "location": "test.field2",
                "value": "Hello {invalid-name}"
            },
            {
                "variable_name": "",  # Empty name
                "syntax": "{}",
                "location": "test.field3",
                "value": "Hello {}"
            }
        ]

        result = validate_template_variables(variables)

        assert result["valid"] is False
        assert len(result["errors"]) >= 3  # May have additional validation errors

        # Check that specific errors are present
        error_text = " ".join(result["errors"])
        assert "Invalid variable name '123invalid'" in error_text
        assert "Invalid variable name 'invalid-name'" in error_text
        assert "Empty variable name" in error_text or "Invalid variable name ''" in error_text

    def test_validate_reserved_keywords(self):
        """Test validation warns about reserved keywords."""
        variables = [
            {
                "variable_name": "if",
                "syntax": "{if}",
                "location": "test.field1",
                "value": "Hello {if}"
            },
            {
                "variable_name": "class",
                "syntax": "${class}",
                "location": "test.field2",
                "value": "Hello ${class}"
            }
        ]

        result = validate_template_variables(variables)

        assert result["valid"] is True  # Warnings don't make it invalid
        assert len(result["warnings"]) >= 2  # May have mixed syntax warning too

        # Check that reserved keyword warnings are present
        warning_text = " ".join(result["warnings"])
        assert "reserved keyword" in warning_text.lower()
        assert result["warnings"][0].count("reserved keyword") >= 1

    def test_validate_mixed_syntax_warning(self):
        """Test validation warns about mixed syntax usage."""
        variables = [
            {
                "variable_name": "user_name",
                "syntax": "{user_name}",
                "location": "test.field1",
                "value": "Hello {user_name}"
            },
            {
                "variable_name": "user_email",
                "syntax": "${user_email}",
                "location": "test.field2",
                "value": "Email: ${user_email}"
            }
        ]

        result = validate_template_variables(variables)

        assert result["valid"] is True
        assert len(result["warnings"]) == 1
        assert "Mixed template variable syntax" in result["warnings"][0]

    def test_validate_long_variable_names(self):
        """Test validation warns about very long variable names."""
        long_name = "a" * 60  # 60 characters
        variables = [
            {
                "variable_name": long_name,
                "syntax": f"{{{long_name}}}",
                "location": "test.field1",
                "value": f"Hello {{{long_name}}}"
            }
        ]

        result = validate_template_variables(variables)

        assert result["valid"] is True
        assert len(result["warnings"]) == 1
        assert "very long" in result["warnings"][0]

    def test_validate_workflow_integration(self):
        """Test complete workflow validation integration."""
        workflow_data = {
            "nodes": [
                {
                    "id": "node1",
                    "data": {
                        "config": {
                            "message": "Hello {user_name}",
                            "email": "${user_email}"
                        }
                    }
                }
            ],
            "settings": {
                "title": "Workflow for {project_name}",
                "description": "Invalid variable: {123invalid}"
            }
        }

        result = validate_workflow_template_variables(workflow_data)

        assert "detected_variables" in result
        assert len(result["detected_variables"]) == 4
        assert result["valid"] is False  # Due to invalid variable name
        assert len(result["errors"]) == 1
        assert len(result["warnings"]) == 1  # Mixed syntax warning
