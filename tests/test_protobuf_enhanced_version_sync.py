import pytest
import uuid
from datetime import datetime, timezone
from unittest.mock import Mock, patch, MagicMock
from app.helpers.workflow_to_protobuf import (
    _workflow_to_protobuf,
    _marketplace_listing_to_protobuf,
    _listing_to_marketplace_workflow
)
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing


class TestEnhancedVersionSyncProtobuf:
    """Test enhanced version synchronization fields in protobuf conversions."""

    def test_workflow_to_protobuf_with_source_version_id(self):
        """Test that _workflow_to_protobuf includes source_version_id field."""
        # Create mock workflow with source_version_id
        workflow = Mock(spec=Workflow)
        workflow.id = uuid.uuid4()
        workflow.name = "Enhanced Workflow"
        workflow.description = "Test workflow with version sync"
        workflow.workflow_url = "test_url"
        workflow.builder_url = "builder_url"
        workflow.owner_id = "owner123"
        workflow.user_ids = []
        workflow.owner_type = "user"
        workflow.start_nodes = []
        workflow.available_nodes = []
        workflow.workflow_template_id = uuid.uuid4()
        workflow.template_owner_id = "template_owner"
        workflow.is_imported = True
        workflow.source_version_id = str(uuid.uuid4())  # Enhanced version sync field
        workflow.is_updated = False
        workflow.is_customizable = True
        workflow.visibility = "private"
        workflow.category = "automation"
        workflow.tags = []
        workflow.status = "active"
        workflow.created_at = datetime.now(timezone.utc)
        workflow.updated_at = datetime.now(timezone.utc)
        workflow.current_version_id = None

        # Convert to protobuf
        protobuf_workflow = _workflow_to_protobuf(workflow)

        # Verify enhanced version sync field is included
        assert protobuf_workflow.source_version_id == workflow.source_version_id
        assert protobuf_workflow.workflow_template_id == str(workflow.workflow_template_id)
        assert protobuf_workflow.is_imported == workflow.is_imported

    def test_workflow_to_protobuf_without_source_version_id(self):
        """Test that _workflow_to_protobuf handles None source_version_id gracefully."""
        # Create mock workflow without source_version_id
        workflow = Mock(spec=Workflow)
        workflow.id = uuid.uuid4()
        workflow.name = "Regular Workflow"
        workflow.description = "Test workflow without version sync"
        workflow.workflow_url = "test_url"
        workflow.builder_url = "builder_url"
        workflow.owner_id = "owner123"
        workflow.user_ids = []
        workflow.owner_type = "user"
        workflow.start_nodes = []
        workflow.available_nodes = []
        workflow.workflow_template_id = None
        workflow.template_owner_id = None
        workflow.is_imported = False
        workflow.source_version_id = None  # No source version
        workflow.is_updated = False
        workflow.is_customizable = True
        workflow.visibility = "private"
        workflow.category = "automation"
        workflow.tags = []
        workflow.status = "active"
        workflow.created_at = datetime.now(timezone.utc)
        workflow.updated_at = datetime.now(timezone.utc)
        workflow.current_version_id = None

        # Convert to protobuf
        protobuf_workflow = _workflow_to_protobuf(workflow)

        # Verify source_version_id is None when not set
        assert protobuf_workflow.source_version_id is None
        assert protobuf_workflow.workflow_template_id is None
        assert protobuf_workflow.is_imported == False

    @patch('app.helpers.workflow_to_protobuf.get_db')
    def test_marketplace_listing_to_protobuf_enhanced_fields(self, mock_get_db):
        """Test that _marketplace_listing_to_protobuf includes enhanced version sync fields."""
        # Create mock listing
        listing = Mock(spec=WorkflowMarketplaceListing)
        listing.id = uuid.uuid4()
        listing.title = "Marketplace Workflow"
        listing.description = "Test marketplace listing"
        listing.workflow_version_id = str(uuid.uuid4())
        listing.workflow_id = str(uuid.uuid4())
        listing.use_count = 10
        listing.listed_by_user_id = "user123"
        listing.execution_count = 50
        listing.category = "automation"
        listing.tags = ["tag1", "tag2"]
        listing.status = "active"
        listing.created_at = datetime.now(timezone.utc)
        listing.updated_at = datetime.now(timezone.utc)

        # Create mock workflow version (user's version)
        user_version = Mock(spec=WorkflowVersion)
        user_version.id = listing.workflow_version_id
        user_version.version_number = "1.0.0"
        user_version.workflow_url = "workflow_url"
        user_version.builder_url = "builder_url"
        user_version.start_nodes = [{"id": "start1"}]
        user_version.available_nodes = [{"id": "node1"}]

        # Create mock source workflow
        source_workflow = Mock(spec=Workflow)
        source_workflow.id = listing.workflow_id
        source_workflow.current_version_id = str(uuid.uuid4())

        # Create mock current source version (different from user's version)
        current_source_version = Mock(spec=WorkflowVersion)
        current_source_version.id = source_workflow.current_version_id
        current_source_version.version_number = "2.0.0"

        # Mock database queries
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock the query chain for workflow version
        mock_version_query = MagicMock()
        mock_version_filter = MagicMock()
        mock_version_filter.first.return_value = user_version
        mock_version_query.filter.return_value = mock_version_filter
        
        # Mock the query chain for source workflow
        mock_workflow_query = MagicMock()
        mock_workflow_filter = MagicMock()
        mock_workflow_filter.first.return_value = source_workflow
        mock_workflow_query.filter.return_value = mock_workflow_filter
        
        # Mock the query chain for current source version
        mock_current_version_query = MagicMock()
        mock_current_version_filter = MagicMock()
        mock_current_version_filter.first.return_value = current_source_version
        mock_current_version_query.filter.return_value = mock_current_version_filter
        
        # Set up query method to return different mocks based on the model
        def mock_query_side_effect(model):
            if model == WorkflowVersion:
                # First call for user's version, second call for current source version
                if not hasattr(mock_query_side_effect, 'call_count'):
                    mock_query_side_effect.call_count = 0
                mock_query_side_effect.call_count += 1
                
                if mock_query_side_effect.call_count == 1:
                    return mock_version_query
                else:
                    return mock_current_version_query
            else:  # Workflow model
                return mock_workflow_query
        
        mock_db.query.side_effect = mock_query_side_effect

        # Convert to protobuf
        protobuf_template = _marketplace_listing_to_protobuf(listing)

        # Verify enhanced version sync fields
        assert protobuf_template.source_workflow_id == str(listing.workflow_id)
        assert protobuf_template.has_updates == True  # Different versions should indicate updates
        assert protobuf_template.current_version_id == str(current_source_version.id)
        assert protobuf_template.user_source_version_id == str(user_version.id)
        
        # Verify other fields
        assert protobuf_template.id == str(listing.id)
        assert protobuf_template.name == listing.title
        assert protobuf_template.version == user_version.version_number

    @patch('app.helpers.workflow_to_protobuf.get_db')
    def test_marketplace_listing_to_protobuf_no_updates(self, mock_get_db):
        """Test _marketplace_listing_to_protobuf when no updates are available."""
        # Create mock listing
        listing = Mock(spec=WorkflowMarketplaceListing)
        listing.id = uuid.uuid4()
        listing.title = "Up-to-date Workflow"
        listing.description = "Test marketplace listing"
        listing.workflow_version_id = str(uuid.uuid4())
        listing.workflow_id = str(uuid.uuid4())
        listing.use_count = 5
        listing.listed_by_user_id = "user123"
        listing.execution_count = 25
        listing.category = "automation"
        listing.tags = []
        listing.status = "active"
        listing.created_at = datetime.now(timezone.utc)
        listing.updated_at = datetime.now(timezone.utc)

        # Create mock workflow version (same as current source version)
        version_id = str(uuid.uuid4())
        user_version = Mock(spec=WorkflowVersion)
        user_version.id = version_id
        user_version.version_number = "1.5.0"
        user_version.workflow_url = "workflow_url"
        user_version.builder_url = "builder_url"
        user_version.start_nodes = []
        user_version.available_nodes = []

        # Create mock source workflow
        source_workflow = Mock(spec=Workflow)
        source_workflow.id = listing.workflow_id
        source_workflow.current_version_id = version_id  # Same as user's version

        # Create mock current source version (same as user's version)
        current_source_version = Mock(spec=WorkflowVersion)
        current_source_version.id = version_id
        current_source_version.version_number = "1.5.0"

        # Mock database queries (similar setup as above)
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        mock_version_query = MagicMock()
        mock_version_filter = MagicMock()
        mock_version_filter.first.return_value = user_version
        mock_version_query.filter.return_value = mock_version_filter
        
        mock_workflow_query = MagicMock()
        mock_workflow_filter = MagicMock()
        mock_workflow_filter.first.return_value = source_workflow
        mock_workflow_query.filter.return_value = mock_workflow_filter
        
        mock_current_version_query = MagicMock()
        mock_current_version_filter = MagicMock()
        mock_current_version_filter.first.return_value = current_source_version
        mock_current_version_query.filter.return_value = mock_current_version_filter
        
        def mock_query_side_effect(model):
            if model == WorkflowVersion:
                if not hasattr(mock_query_side_effect, 'call_count'):
                    mock_query_side_effect.call_count = 0
                mock_query_side_effect.call_count += 1
                
                if mock_query_side_effect.call_count == 1:
                    return mock_version_query
                else:
                    return mock_current_version_query
            else:
                return mock_workflow_query
        
        mock_db.query.side_effect = mock_query_side_effect

        # Convert to protobuf
        protobuf_template = _marketplace_listing_to_protobuf(listing)

        # Verify no updates available (same version IDs)
        assert protobuf_template.has_updates == False
        assert protobuf_template.current_version_id == str(current_source_version.id)
        assert protobuf_template.user_source_version_id == str(user_version.id)
        assert protobuf_template.current_version_id == protobuf_template.user_source_version_id

    def test_listing_to_marketplace_workflow_unchanged(self):
        """Test that _listing_to_marketplace_workflow function is not affected by changes."""
        # Create mock listing
        listing = Mock(spec=WorkflowMarketplaceListing)
        listing.id = uuid.uuid4()
        listing.title = "Marketplace Workflow"
        listing.description = "Test description"
        listing.image_url = "image.jpg"
        listing.workflow_version_id = None  # No version
        listing.listed_by_user_id = "user123"
        listing.average_rating = 4.5
        listing.use_count = 100
        listing.execution_count = 500
        listing.category = "automation"
        listing.tags = ["popular"]
        listing.status = "active"
        listing.created_at = datetime.now(timezone.utc)
        listing.updated_at = datetime.now(timezone.utc)

        # Convert to protobuf
        protobuf_marketplace = _listing_to_marketplace_workflow(listing)

        # Verify basic fields (this function doesn't have enhanced version sync fields)
        assert protobuf_marketplace.id == str(listing.id)
        assert protobuf_marketplace.name == listing.title
        assert protobuf_marketplace.description == listing.description
        assert protobuf_marketplace.average_rating == listing.average_rating
        assert protobuf_marketplace.use_count == listing.use_count
        assert protobuf_marketplace.execution_count == listing.execution_count
        assert protobuf_marketplace.visibility == "PUBLIC"
        assert protobuf_marketplace.version == "1.0.0"  # Default when no version


if __name__ == "__main__":
    pytest.main([__file__, "-v"])