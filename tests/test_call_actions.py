import unittest
from unittest.mock import MagicMock, patch
import grpc
from datetime import datetime, timezone
from app.services.agent_functions import AgentFunctionsService
from app.models.agent import AgentConfig, AgentConfigVersion
from app.utils.constants.constants import AgentStatusEnum, AgentVisibilityEnum, ActionTypeEnum, ExecutionTypeEnum
from app.grpc import agent_pb2


class MockRequest:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)


class TestCallActions(unittest.TestCase):
    def setUp(self):
        self.agent_functions = AgentFunctionsService()
        
        # Mock the database session
        self.db_mock = MagicMock()
        
        # Mock SessionLocal to return our mock
        self.session_local_patcher = patch('app.services.agent_functions.SessionLocal')
        self.mock_session_local = self.session_local_patcher.start()
        self.mock_session_local.return_value = self.db_mock
        
        # Mock the close method
        self.db_mock.close = MagicMock()
        self.db_mock.commit = MagicMock()
        self.db_mock.rollback = MagicMock()
        
        # Mock gRPC context
        self.context_mock = MagicMock()
        
        # Test data
        self.agent_id = "test-agent-id"
        self.version_id = "test-version-id"
        
        # Mock agent with all required fields
        self.agent_mock = MagicMock(spec=AgentConfig)
        self.agent_mock.id = self.agent_id
        self.agent_mock.name = "Test Agent"
        self.agent_mock.description = "Test Description"
        self.agent_mock.current_version_id = self.version_id
        self.agent_mock.call_actions = None
        
        # Mock version
        self.version_mock = MagicMock(spec=AgentConfigVersion)
        self.version_mock.id = self.version_id
        self.version_mock.call_actions = None
        
        # Mock query methods
        self.db_mock.query.return_value.filter.return_value.first.return_value = self.agent_mock

    def tearDown(self):
        self.session_local_patcher.stop()

    def test_update_call_actions_success(self):
        """Test successful update of call actions"""
        # Prepare test data
        call_actions = [
            agent_pb2.CallAction(
                action_type=agent_pb2.ActionType.POST_CALL,
                execution_type=agent_pb2.ExecutionType.WORKFLOW,
                id="workflow-123"
            ),
            agent_pb2.CallAction(
                action_type=agent_pb2.ActionType.PRE_CALL,
                execution_type=agent_pb2.ExecutionType.MCP,
                id="mcp-456"
            )
        ]
        
        request = MockRequest(
            agent_id=self.agent_id,
            call_actions=call_actions
        )
        
        # Mock the version query
        version_query_mock = MagicMock()
        version_query_mock.filter.return_value.first.return_value = self.version_mock
        self.db_mock.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=self.agent_mock)))),
            version_query_mock
        ]
        
        # Execute the method
        response = self.agent_functions.updateCallActions(request, self.context_mock)
        
        # Verify the response
        self.assertTrue(response.success)
        self.assertEqual(response.message, "Call actions updated successfully")
        
        # Verify that call_actions were set correctly
        expected_call_actions = [
            {
                "action_type": "POST_CALL",
                "execution_type": "WORKFLOW",
                "id": "workflow-123"
            },
            {
                "action_type": "PRE_CALL",
                "execution_type": "MCP",
                "id": "mcp-456"
            }
        ]
        self.assertEqual(self.agent_mock.call_actions, expected_call_actions)
        self.assertEqual(self.version_mock.call_actions, expected_call_actions)
        
        # Verify database operations
        self.db_mock.commit.assert_called_once()
        self.db_mock.close.assert_called_once()

    def test_update_call_actions_agent_not_found(self):
        """Test update call actions when agent is not found"""
        request = MockRequest(
            agent_id="non-existent-agent",
            call_actions=[]
        )
        
        # Mock agent not found
        self.db_mock.query.return_value.filter.return_value.first.return_value = None
        
        # Execute the method
        response = self.agent_functions.updateCallActions(request, self.context_mock)
        
        # Verify the response
        self.assertFalse(response.success)
        self.assertEqual(response.message, "Agent not found")
        
        # Verify gRPC context was set correctly
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)
        self.context_mock.set_details.assert_called_with("Agent not found")

    def test_get_call_actions_success(self):
        """Test successful retrieval of call actions"""
        # Set up test data
        self.agent_mock.call_actions = [
            {
                "action_type": "POST_CALL",
                "execution_type": "WORKFLOW",
                "id": "workflow-123"
            },
            {
                "action_type": "PRE_CALL",
                "execution_type": "MCP",
                "id": "mcp-456"
            }
        ]
        
        request = MockRequest(agent_id=self.agent_id)
        
        # Execute the method
        response = self.agent_functions.getCallActions(request, self.context_mock)
        
        # Verify the response
        self.assertTrue(response.success)
        self.assertEqual(response.message, "Call actions retrieved successfully")
        self.assertEqual(len(response.call_actions), 2)
        
        # Verify the call actions content
        call_action_1 = response.call_actions[0]
        self.assertEqual(call_action_1.action_type, agent_pb2.ActionType.POST_CALL)
        self.assertEqual(call_action_1.execution_type, agent_pb2.ExecutionType.WORKFLOW)
        self.assertEqual(call_action_1.id, "workflow-123")
        
        call_action_2 = response.call_actions[1]
        self.assertEqual(call_action_2.action_type, agent_pb2.ActionType.PRE_CALL)
        self.assertEqual(call_action_2.execution_type, agent_pb2.ExecutionType.MCP)
        self.assertEqual(call_action_2.id, "mcp-456")

    def test_get_call_actions_empty(self):
        """Test retrieval of call actions when none exist"""
        # Agent has no call actions
        self.agent_mock.call_actions = None
        
        request = MockRequest(agent_id=self.agent_id)
        
        # Execute the method
        response = self.agent_functions.getCallActions(request, self.context_mock)
        
        # Verify the response
        self.assertTrue(response.success)
        self.assertEqual(response.message, "Call actions retrieved successfully")
        self.assertEqual(len(response.call_actions), 0)

    def test_get_call_actions_agent_not_found(self):
        """Test get call actions when agent is not found"""
        request = MockRequest(agent_id="non-existent-agent")
        
        # Mock agent not found
        self.db_mock.query.return_value.filter.return_value.first.return_value = None
        
        # Execute the method
        response = self.agent_functions.getCallActions(request, self.context_mock)
        
        # Verify the response
        self.assertFalse(response.success)
        self.assertEqual(response.message, "Agent not found")
        self.assertEqual(len(response.call_actions), 0)
        
        # Verify gRPC context was set correctly
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)
        self.context_mock.set_details.assert_called_with("Agent not found")

    def test_update_call_actions_database_error(self):
        """Test update call actions with database error"""
        request = MockRequest(
            agent_id=self.agent_id,
            call_actions=[
                agent_pb2.CallAction(
                    action_type=agent_pb2.ActionType.POST_CALL,
                    execution_type=agent_pb2.ExecutionType.WORKFLOW,
                    id="workflow-123"
                )
            ]
        )

        # Mock database error
        self.db_mock.commit.side_effect = Exception("Database error")

        # Execute the method
        response = self.agent_functions.updateCallActions(request, self.context_mock)

        # Verify the response
        self.assertFalse(response.success)
        self.assertEqual(response.message, "Internal server error")

        # Verify rollback was called
        self.db_mock.rollback.assert_called_once()

        # Verify gRPC context was set correctly
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.INTERNAL)

    def test_get_call_actions_database_error(self):
        """Test get call actions with database error"""
        request = MockRequest(agent_id=self.agent_id)

        # Mock database error
        self.db_mock.query.side_effect = Exception("Database error")

        # Execute the method
        response = self.agent_functions.getCallActions(request, self.context_mock)

        # Verify the response
        self.assertFalse(response.success)
        self.assertEqual(response.message, "Internal server error")
        self.assertEqual(len(response.call_actions), 0)

        # Verify gRPC context was set correctly
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.INTERNAL)

    def test_update_call_actions_without_current_version(self):
        """Test update call actions when agent has no current version"""
        # Agent without current version
        self.agent_mock.current_version_id = None

        request = MockRequest(
            agent_id=self.agent_id,
            call_actions=[
                agent_pb2.CallAction(
                    action_type=agent_pb2.ActionType.POST_CALL,
                    execution_type=agent_pb2.ExecutionType.WORKFLOW,
                    id="workflow-123"
                )
            ]
        )

        # Execute the method
        response = self.agent_functions.updateCallActions(request, self.context_mock)

        # Verify the response
        self.assertTrue(response.success)
        self.assertEqual(response.message, "Call actions updated successfully")

        # Verify that only agent call_actions were set (not version)
        expected_call_actions = [
            {
                "action_type": "POST_CALL",
                "execution_type": "WORKFLOW",
                "id": "workflow-123"
            }
        ]
        self.assertEqual(self.agent_mock.call_actions, expected_call_actions)


if __name__ == '__main__':
    unittest.main()
