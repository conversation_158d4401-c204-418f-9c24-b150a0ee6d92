#!/usr/bin/env python3
"""
Test script for UpdateAgentCombined functionality.

This test verifies that the UpdateAgentCombined method can handle all the fields
from the UpdateAgentCombinedRequest proto message.
"""

import sys
import os
import json
from datetime import datetime
from unittest.mock import Mock, patch

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from app.services.agent_functions import AgentFunctionsService
from app.models.agent import AgentConfig, AgentCapabilities, AgentVariables
from app.utils.constants.constants import (
    AgentOwnerTypeEnum,
    AgentCategoryEnum,
    AgentVisibilityEnum,
    AgentStatusEnum,
    AgentToneEnum,
    CategoryEnum,
)
from app.grpc import agent_pb2
from app.db.session import SessionLocal
from google.protobuf.field_mask_pb2 import FieldMask


def test_update_agent_combined_core_details():
    """Test updating core details through UpdateAgentCombined"""
    
    print("=== Test: UpdateAgentCombined - Core Details ===")
    
    agent_functions = AgentFunctionsService()
    db = SessionLocal()
    
    try:
        # Create a test agent
        test_agent = AgentConfig(
            id="test-combined-agent-123",
            name="Original Agent",
            description="Original description",
            avatar="https://example.com/original.png",
            owner_id="test-user-123",
            owner_type=AgentOwnerTypeEnum.USER,
            is_updated=False,
            visibility=AgentVisibilityEnum.PRIVATE,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Original system message",
            department="Engineering",
            tone=AgentToneEnum.PROFESSIONAL,
            agent_topic_type="general",
            category=CategoryEnum.GENERAL,
        )
        
        db.add(test_agent)
        db.commit()
        
        print(f"✅ Created test agent with original values")
        
        # Create update request with core details
        update_request = agent_pb2.UpdateAgentCombinedRequest()
        update_request.agent_id = test_agent.id
        update_request.owner.id = "test-user-123"
        
        # Set new values
        update_request.name = "Updated Agent Name"
        update_request.description = "Updated description"
        update_request.avatar = "https://example.com/updated.png"
        update_request.system_message = "Updated system message"
        update_request.department = "Marketing"
        update_request.tone = agent_pb2.Tone.FRIENDLY
        update_request.agent_topic_type = "specific"
        update_request.category = agent_pb2.Category.MARKETING
        
        # Set field mask
        field_mask = FieldMask()
        field_mask.paths.extend([
            "name", "description", "avatar", "system_message", 
            "department", "tone", "agent_topic_type", "category"
        ])
        update_request.update_mask.CopyFrom(field_mask)
        
        context = Mock()
        
        # Execute update
        response = agent_functions.UpdateAgentCombined(update_request, context)
        
        print(f"Update response: {response.success} - {response.message}")
        
        assert response.success, f"Update failed: {response.message}"
        
        # Verify changes were applied
        updated_agent = db.query(AgentConfig).filter(AgentConfig.id == test_agent.id).first()
        
        assert updated_agent.name == "Updated Agent Name"
        assert updated_agent.description == "Updated description"
        assert updated_agent.avatar == "https://example.com/updated.png"
        assert updated_agent.system_message == "Updated system message"
        assert updated_agent.department == "Marketing"
        assert updated_agent.tone == AgentToneEnum.FRIENDLY
        assert updated_agent.agent_topic_type == "specific"
        assert updated_agent.category == CategoryEnum.MARKETING
        assert updated_agent.is_updated == True
        
        print("✅ All core details updated successfully")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        # Cleanup
        db.query(AgentConfig).filter(AgentConfig.id == "test-combined-agent-123").delete()
        db.commit()
        db.close()


def test_update_agent_combined_settings():
    """Test updating settings through UpdateAgentCombined"""
    
    print("=== Test: UpdateAgentCombined - Settings ===")
    
    agent_functions = AgentFunctionsService()
    db = SessionLocal()
    
    try:
        # Create a test agent
        test_agent = AgentConfig(
            id="test-combined-settings-123",
            name="Settings Test Agent",
            description="Test description",
            avatar="https://example.com/avatar.png",
            owner_id="test-user-123",
            owner_type=AgentOwnerTypeEnum.USER,
            is_updated=False,
            visibility=AgentVisibilityEnum.PRIVATE,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Test system message",
            user_ids=["user1", "user2"],
            tags=["tag1", "tag2"],
            is_changes_marketplace=False,
            is_bench_employee=False,
            is_a2a=False,
            is_customizable=False,
            example_prompts=["prompt1", "prompt2"],
        )
        
        db.add(test_agent)
        db.commit()
        
        print(f"✅ Created test agent with original settings")
        
        # Create update request with settings
        update_request = agent_pb2.UpdateAgentCombinedRequest()
        update_request.agent_id = test_agent.id
        update_request.owner.id = "test-user-123"
        
        # Set new values
        update_request.user_ids.extend(["user3", "user4"])
        update_request.tags.extend(["tag3", "tag4"])
        update_request.status = agent_pb2.Status.DRAFT
        update_request.is_changes_marketplace = True
        update_request.is_bench_employee = True
        update_request.is_a2a = True
        update_request.is_customizable = True
        update_request.example_prompts.extend(["prompt3", "prompt4"])
        
        # Set field mask
        field_mask = FieldMask()
        field_mask.paths.extend([
            "user_ids", "tags", "status", "is_changes_marketplace",
            "is_bench_employee", "is_a2a", "is_customizable", "example_prompts"
        ])
        update_request.update_mask.CopyFrom(field_mask)
        
        context = Mock()
        
        # Execute update
        response = agent_functions.UpdateAgentCombined(update_request, context)
        
        print(f"Update response: {response.success} - {response.message}")
        
        assert response.success, f"Update failed: {response.message}"
        
        # Verify changes were applied
        updated_agent = db.query(AgentConfig).filter(AgentConfig.id == test_agent.id).first()
        
        assert updated_agent.user_ids == ["user3", "user4"]
        assert updated_agent.tags == ["tag3", "tag4"]
        assert updated_agent.status == "draft"
        assert updated_agent.is_changes_marketplace == True
        assert updated_agent.is_bench_employee == True
        assert updated_agent.is_a2a == True
        assert updated_agent.is_customizable == True
        assert updated_agent.example_prompts == ["prompt3", "prompt4"]
        assert updated_agent.is_updated == True
        
        print("✅ All settings updated successfully")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        # Cleanup
        db.query(AgentConfig).filter(AgentConfig.id == "test-combined-settings-123").delete()
        db.commit()
        db.close()


def run_all_tests():
    """Run all UpdateAgentCombined tests"""
    
    print("🧪 Running UpdateAgentCombined functionality tests...")
    
    tests = [
        test_update_agent_combined_core_details,
        test_update_agent_combined_settings,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
            print(f"✅ {test.__name__} PASSED\n")
        except Exception as e:
            failed += 1
            print(f"❌ {test.__name__} FAILED with exception: {str(e)}\n")
    
    print(f"🏁 Test Results: {passed} passed, {failed} failed")
    
    if failed > 0:
        print("❌ Some tests failed!")
        return False
    else:
        print("✅ All tests passed!")
        return True


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
