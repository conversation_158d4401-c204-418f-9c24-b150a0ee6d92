import pytest
import json
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock
from app.services.marketplace_functions import WorkflowMarketplaceFunctions
from app.models.workflow import WorkflowMarketplaceListing, WorkflowVersion
from app.grpc_ import workflow_pb2
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum, WorkflowCategoryEnum
from app.helpers.workflow_to_protobuf import _marketplace_listing_to_protobuf


class TestGetTemplateDatabaseFix:
    """Test the database session management fix in getTemplate endpoint"""

    def setup_method(self):
        """Setup test fixtures"""
        self.marketplace_functions = WorkflowMarketplaceFunctions()
        
        # Mock workflow version with available_nodes
        self.mock_workflow_version = Mock(spec=WorkflowVersion)
        self.mock_workflow_version.id = "version-123"
        self.mock_workflow_version.version_number = "2.1.0"
        self.mock_workflow_version.workflow_url = "https://example.com/workflow"
        self.mock_workflow_version.builder_url = "https://example.com/builder"
        self.mock_workflow_version.start_nodes = [{"type": "start", "id": "start-1"}]
        self.mock_workflow_version.available_nodes = [
            {"type": "ai", "name": "OpenAI GPT", "id": "openai-1"},
            {"type": "data", "name": "Data Processor", "id": "data-1"},
            {"type": "api", "name": "API Call", "id": "api-1"}
        ]
        
        # Mock marketplace listing
        self.mock_marketplace_listing = Mock(spec=WorkflowMarketplaceListing)
        self.mock_marketplace_listing.id = "listing-123"
        self.mock_marketplace_listing.title = "Test Workflow"
        self.mock_marketplace_listing.description = "A test workflow"
        self.mock_marketplace_listing.workflow_version_id = "version-123"
        self.mock_marketplace_listing.workflow_id = "workflow-123"
        self.mock_marketplace_listing.listed_by_user_id = "user-123"
        self.mock_marketplace_listing.use_count = 5
        self.mock_marketplace_listing.execution_count = 10
        self.mock_marketplace_listing.category = WorkflowCategoryEnum.LLM_ORCHESTRATION
        self.mock_marketplace_listing.tags = ["ai", "automation"]
        self.mock_marketplace_listing.status = WorkflowStatusEnum.ACTIVE
        self.mock_marketplace_listing.created_at = datetime(2024, 1, 1)
        self.mock_marketplace_listing.updated_at = datetime(2024, 1, 2)

    def test_marketplace_listing_to_protobuf_direct(self):
        """Test the _marketplace_listing_to_protobuf function directly with database session fix"""
        
        # Mock the database session and query
        with patch('app.helpers.workflow_to_protobuf.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            
            # Mock the query chain
            mock_query = Mock()
            mock_filter = Mock()
            mock_db.query.return_value = mock_query
            mock_query.filter.return_value = mock_filter
            mock_filter.first.return_value = self.mock_workflow_version
            
            # Call the function directly
            template_proto = _marketplace_listing_to_protobuf(self.mock_marketplace_listing)
            
            # Verify database session was created and closed
            mock_get_db.assert_called_once()
            mock_db.close.assert_called_once()
            
            # Verify the protobuf contains the correct data
            assert template_proto.version == "2.1.0"
            assert len(template_proto.available_nodes) == 3
            
            # Parse the JSON strings back to verify content
            available_nodes = [json.loads(node) for node in template_proto.available_nodes]
            assert available_nodes[0]["type"] == "ai"
            assert available_nodes[0]["name"] == "OpenAI GPT"
            assert available_nodes[1]["type"] == "data"
            assert available_nodes[2]["type"] == "api"

    def test_marketplace_listing_to_protobuf_with_db_error(self):
        """Test that database errors are handled gracefully"""
        
        # Mock the database session to raise an exception
        with patch('app.helpers.workflow_to_protobuf.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            
            # Make the query raise an exception
            mock_db.query.side_effect = Exception("Database connection failed")
            
            # Call the function directly
            template_proto = _marketplace_listing_to_protobuf(self.mock_marketplace_listing)
            
            # Verify database session was created and closed even with error
            mock_get_db.assert_called_once()
            mock_db.close.assert_called_once()
            
            # Verify the protobuf falls back to defaults
            assert template_proto.version == "1.0.0"  # Default version
            assert len(template_proto.available_nodes) == 0  # Empty list

    def test_marketplace_listing_to_protobuf_no_version_id(self):
        """Test behavior when workflow_version_id is None"""
        
        # Create a listing without version ID
        listing_no_version = Mock(spec=WorkflowMarketplaceListing)
        listing_no_version.id = "listing-456"
        listing_no_version.title = "Test Workflow No Version"
        listing_no_version.description = "A test workflow without version"
        listing_no_version.workflow_version_id = None  # No version ID
        listing_no_version.workflow_id = "workflow-456"
        listing_no_version.listed_by_user_id = "user-456"
        listing_no_version.use_count = 0
        listing_no_version.execution_count = 0
        listing_no_version.category = WorkflowCategoryEnum.AUTOMATION
        listing_no_version.tags = []
        listing_no_version.status = WorkflowStatusEnum.ACTIVE
        listing_no_version.created_at = datetime(2024, 1, 1)
        listing_no_version.updated_at = datetime(2024, 1, 2)
        
        # Mock get_db should not be called since workflow_version_id is None
        with patch('app.helpers.workflow_to_protobuf.get_db') as mock_get_db:
            # Call the function directly
            template_proto = _marketplace_listing_to_protobuf(listing_no_version)
            
            # Verify database session was NOT created since no version ID
            mock_get_db.assert_not_called()
            
            # Verify the protobuf falls back to defaults
            assert template_proto.version == "1.0.0"  # Default version
            assert len(template_proto.available_nodes) == 0  # Empty list
            assert template_proto.workflow_url == ""
            assert template_proto.builder_url == ""

    @patch('app.services.marketplace_functions.SessionLocal')
    def test_getTemplate_end_to_end_with_fix(self, mock_session_local):
        """Test the complete getTemplate flow with the database fix"""
        
        # Setup mock database session for marketplace functions
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        
        # Mock the marketplace listing query
        mock_db.query.return_value.filter.return_value.first.return_value = self.mock_marketplace_listing
        
        # Mock the workflow version query in the helper function
        with patch('app.helpers.workflow_to_protobuf.get_db') as mock_get_db:
            mock_helper_db = Mock()
            mock_get_db.return_value = mock_helper_db
            mock_helper_db.query.return_value.filter.return_value.first.return_value = self.mock_workflow_version
            
            # Create request
            request = workflow_pb2.GetTemplateRequest(id="listing-123")
            context = Mock()
            
            # Call the method
            response = self.marketplace_functions.getTemplate(request, context)
            
            # Verify success
            assert response.success is True
            assert response.template is not None
            
            # Verify version and available_nodes are correctly populated
            assert response.template.version == "2.1.0"
            assert len(response.template.available_nodes) == 3
            
            # Verify database sessions were properly managed
            mock_get_db.assert_called_once()
            mock_helper_db.close.assert_called_once()
            
            # Parse and verify available_nodes content
            available_nodes = [json.loads(node) for node in response.template.available_nodes]
            assert available_nodes[0]["type"] == "ai"
            assert available_nodes[1]["type"] == "data"
            assert available_nodes[2]["type"] == "api"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])