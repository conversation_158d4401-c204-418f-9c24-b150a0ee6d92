import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from fastapi.testclient import TestClient

from app.api.routers.agent_routes import agent_router
from app.schemas.agent import (
    UpdateCallActionsRequest,
    UpdateCallActionsResponse,
    GetCallActionsResponse,
    CallAction,
    ActionTypeEnum,
    ExecutionTypeEnum
)


class TestAgentCallActionsRoutes:
    """Test cases for agent call actions routes."""

    @pytest.fixture
    def mock_current_user(self):
        """Mock current user from JWT token."""
        return {
            "user_id": "123e4567-e89b-12d3-a456-426614174000",
            "email": "<EMAIL>",
            "role": "admin",
            "name": "<PERSON>",
            "organisation_id": "org123"
        }

    @pytest.fixture
    def sample_call_actions(self):
        """Sample call actions for testing."""
        return [
            CallAction(
                action_type=ActionTypeEnum.POST_CALL,
                execution_type=ExecutionTypeEnum.WORKFLOW,
                id="workflow-123"
            ),
            CallAction(
                action_type=ActionTypeEnum.PRE_CALL,
                execution_type=ExecutionTypeEnum.MCP,
                id="mcp-456"
            )
        ]

    @pytest.fixture
    def mock_update_response(self):
        """Mock successful update response."""
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Call actions updated successfully"
        return mock_response

    @pytest.fixture
    def mock_get_response(self, sample_call_actions):
        """Mock successful get response."""
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Call actions retrieved successfully"
        
        # Create mock protobuf call actions
        mock_call_actions = []
        for action in sample_call_actions:
            mock_action = Mock()
            mock_action.action_type.name = action.action_type.value
            mock_action.execution_type.name = action.execution_type.value
            mock_action.id = action.id
            mock_call_actions.append(mock_action)
        
        mock_response.call_actions = mock_call_actions
        return mock_response

    @patch('app.api.routers.agent_routes.agent_service')
    @patch('app.api.routers.agent_routes.role_required')
    async def test_update_agent_call_actions_success(
        self, mock_role_required, mock_agent_service, mock_current_user, 
        sample_call_actions, mock_update_response
    ):
        """Test successful update of agent call actions."""
        # Setup mocks
        mock_role_required.return_value = lambda: mock_current_user
        mock_agent_service.update_call_actions = AsyncMock(return_value=mock_update_response)
        
        # Create request payload
        payload = UpdateCallActionsRequest(call_actions=sample_call_actions)
        
        # Import and call the route function directly
        from app.api.routers.agent_routes import update_agent_call_actions
        
        result = await update_agent_call_actions(
            agent_id="test-agent-id",
            payload=payload,
            current_user=mock_current_user
        )
        
        # Verify the result
        assert isinstance(result, UpdateCallActionsResponse)
        assert result.success is True
        assert result.message == "Call actions updated successfully"
        
        # Verify the gRPC service was called correctly
        mock_agent_service.update_call_actions.assert_called_once()
        call_args = mock_agent_service.update_call_actions.call_args
        assert call_args[1]["agent_id"] == "test-agent-id"
        assert len(call_args[1]["call_actions"]) == 2
        assert call_args[1]["call_actions"][0]["action_type"] == "POST_CALL"
        assert call_args[1]["call_actions"][0]["execution_type"] == "WORKFLOW"
        assert call_args[1]["call_actions"][0]["id"] == "workflow-123"

    @patch('app.api.routers.agent_routes.agent_service')
    @patch('app.api.routers.agent_routes.role_required')
    async def test_get_agent_call_actions_success(
        self, mock_role_required, mock_agent_service, mock_current_user, mock_get_response
    ):
        """Test successful retrieval of agent call actions."""
        # Setup mocks
        mock_role_required.return_value = lambda: mock_current_user
        mock_agent_service.get_call_actions = AsyncMock(return_value=mock_get_response)
        
        # Import and call the route function directly
        from app.api.routers.agent_routes import get_agent_call_actions
        
        result = await get_agent_call_actions(
            agent_id="test-agent-id",
            current_user=mock_current_user
        )
        
        # Verify the result
        assert isinstance(result, GetCallActionsResponse)
        assert result.success is True
        assert result.message == "Call actions retrieved successfully"
        assert len(result.call_actions) == 2
        assert result.call_actions[0].action_type == ActionTypeEnum.POST_CALL
        assert result.call_actions[0].execution_type == ExecutionTypeEnum.WORKFLOW
        assert result.call_actions[0].id == "workflow-123"
        
        # Verify the gRPC service was called correctly
        mock_agent_service.get_call_actions.assert_called_once_with(agent_id="test-agent-id")

    @patch('app.api.routers.agent_routes.agent_service')
    @patch('app.api.routers.agent_routes.role_required')
    async def test_update_agent_call_actions_empty_list(
        self, mock_role_required, mock_agent_service, mock_current_user, mock_update_response
    ):
        """Test updating agent call actions with empty list."""
        # Setup mocks
        mock_role_required.return_value = lambda: mock_current_user
        mock_agent_service.update_call_actions = AsyncMock(return_value=mock_update_response)
        
        # Create request payload with empty list
        payload = UpdateCallActionsRequest(call_actions=[])
        
        # Import and call the route function directly
        from app.api.routers.agent_routes import update_agent_call_actions
        
        result = await update_agent_call_actions(
            agent_id="test-agent-id",
            payload=payload,
            current_user=mock_current_user
        )
        
        # Verify the result
        assert isinstance(result, UpdateCallActionsResponse)
        assert result.success is True
        
        # Verify the gRPC service was called with empty list
        mock_agent_service.update_call_actions.assert_called_once()
        call_args = mock_agent_service.update_call_actions.call_args
        assert call_args[1]["agent_id"] == "test-agent-id"
        assert len(call_args[1]["call_actions"]) == 0

    @patch('app.api.routers.agent_routes.agent_service')
    @patch('app.api.routers.agent_routes.role_required')
    async def test_get_agent_call_actions_empty_response(
        self, mock_role_required, mock_agent_service, mock_current_user
    ):
        """Test getting agent call actions when none exist."""
        # Setup mocks
        mock_role_required.return_value = lambda: mock_current_user
        
        # Mock empty response
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Call actions retrieved successfully"
        mock_response.call_actions = []
        
        mock_agent_service.get_call_actions = AsyncMock(return_value=mock_response)
        
        # Import and call the route function directly
        from app.api.routers.agent_routes import get_agent_call_actions
        
        result = await get_agent_call_actions(
            agent_id="test-agent-id",
            current_user=mock_current_user
        )
        
        # Verify the result
        assert isinstance(result, GetCallActionsResponse)
        assert result.success is True
        assert len(result.call_actions) == 0

    @patch('app.api.routers.agent_routes.agent_service')
    @patch('app.api.routers.agent_routes.role_required')
    async def test_update_agent_call_actions_grpc_error(
        self, mock_role_required, mock_agent_service, mock_current_user, sample_call_actions
    ):
        """Test update agent call actions with gRPC error."""
        # Setup mocks
        mock_role_required.return_value = lambda: mock_current_user
        mock_agent_service.update_call_actions = AsyncMock(
            side_effect=HTTPException(status_code=404, detail="Agent not found")
        )
        
        # Create request payload
        payload = UpdateCallActionsRequest(call_actions=sample_call_actions)
        
        # Import and call the route function directly
        from app.api.routers.agent_routes import update_agent_call_actions
        
        # Verify that HTTPException is raised
        with pytest.raises(HTTPException) as exc_info:
            await update_agent_call_actions(
                agent_id="non-existent-agent",
                payload=payload,
                current_user=mock_current_user
            )
        
        assert exc_info.value.status_code == 404
        assert exc_info.value.detail == "Agent not found"

    @patch('app.api.routers.agent_routes.agent_service')
    @patch('app.api.routers.agent_routes.role_required')
    async def test_get_agent_call_actions_grpc_error(
        self, mock_role_required, mock_agent_service, mock_current_user
    ):
        """Test get agent call actions with gRPC error."""
        # Setup mocks
        mock_role_required.return_value = lambda: mock_current_user
        mock_agent_service.get_call_actions = AsyncMock(
            side_effect=HTTPException(status_code=404, detail="Agent not found")
        )
        
        # Import and call the route function directly
        from app.api.routers.agent_routes import get_agent_call_actions
        
        # Verify that HTTPException is raised
        with pytest.raises(HTTPException) as exc_info:
            await get_agent_call_actions(
                agent_id="non-existent-agent",
                current_user=mock_current_user
            )
        
        assert exc_info.value.status_code == 404
        assert exc_info.value.detail == "Agent not found"
