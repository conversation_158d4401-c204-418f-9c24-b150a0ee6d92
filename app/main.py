import os
import grpc
from concurrent import futures

from app.services.agent_service import AgentService
from app.services.provider_service import ProviderService, ModelService
from app.grpc import agent_pb2_grpc, provider_pb2_grpc


def serve():
    # Configure gRPC server options to prevent "too_many_pings" errors
    options = [
        # Keepalive settings for server
        ("grpc.keepalive_time_ms", 30000),  # Send keepalive ping every 30s
        ("grpc.keepalive_timeout_ms", 5000),  # Wait 5s for ping ack
        ("grpc.keepalive_permit_without_calls", True),  # Allow keepalive pings
        ("grpc.http2.max_pings_without_data", 0),  # Allow unlimited pings
        ("grpc.http2.min_time_between_pings_ms", 10000),  # Min 10s between
        ("grpc.http2.min_ping_interval_without_data_ms", 300000),  # 5min no data
        # Connection settings
        ("grpc.max_connection_idle_ms", 300000),  # Close after 5min inactivity
        ("grpc.max_connection_age_ms", 600000),  # Close after 10min
        ("grpc.max_connection_age_grace_ms", 30000),  # Grace period 30s
    ]

    # Create gRPC server with proper configuration
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10), options=options)

    # Add agent service to server
    agent_service = AgentService()
    agent_pb2_grpc.add_AgentServiceServicer_to_server(agent_service, server)

    # Add provider services to server
    provider_service = ProviderService()
    model_service = ModelService()
    provider_pb2_grpc.add_ProviderServiceServicer_to_server(provider_service, server)
    provider_pb2_grpc.add_ModelServiceServicer_to_server(model_service, server)

    # Get port from environment or use default
    port = os.getenv("PORT", "50057")  # Default port 50057 to avoid collision
    server.add_insecure_port(f"[::]:{port}")

    # Start server
    server.start()
    print(f"Agent service started on port {port} with keepalive configuration")

    # Keep thread alive
    server.wait_for_termination()


if __name__ == "__main__":
    serve()
