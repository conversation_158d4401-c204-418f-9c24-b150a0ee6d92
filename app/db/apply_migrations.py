#!/usr/bin/env python
"""
Migration application script.
Usage: python -m app.db.apply_migrations
"""

import subprocess
import sys


def apply_migrations():
    try:
        print("Applying all existing migrations...")
        # Run alembic upgrade to latest revision
        result = subprocess.run(
            ["alembic", "upgrade", "head"], check=True, capture_output=True, text=True
        )

        print("Migrations applied successfully!")
        print(result.stdout)

    except subprocess.CalledProcessError as e:
        print(f"Error applying migrations: {e}")
        print(f"Error output: {e.stderr}")
        sys.exit(1)


if __name__ == "__main__":
    apply_migrations()
