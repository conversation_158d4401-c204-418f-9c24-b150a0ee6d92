#!/usr/bin/env python
"""
Apply a specific migration.
Usage: python -m app.db.apply_specific_migration 003
"""

import subprocess
import sys


def apply_specific_migration(revision):
    try:
        # Run alembic upgrade to the specified revision
        print(f"Applying migration to revision {revision}...")
        result = subprocess.run(
            ["alembic", "upgrade", revision], check=True, capture_output=True, text=True
        )

        print("Migration applied successfully!")
        print(result.stdout)

    except subprocess.CalledProcessError as e:
        print(f"Error applying migration: {e}")
        print(f"Error output: {e.stderr}")
        sys.exit(1)


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python -m app.db.apply_specific_migration <revision>")
        sys.exit(1)

    revision = sys.argv[1]
    apply_specific_migration(revision)
