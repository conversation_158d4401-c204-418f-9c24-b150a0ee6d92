from datetime import datetime
import uuid
from sqlalchemy import (
    Column,
    String,
    DateTime,
    Boolean,
    Float,
    Integer,
    Text,
    ForeignKey,
)
from sqlalchemy.orm import declarative_base, relationship
from app.utils.constants.table_names import (
    PROVIDERS_TABLE,
    MODELS_TABLE,
)

Base = declarative_base()


class Provider(Base):
    __tablename__ = PROVIDERS_TABLE

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    provider = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    base_url = Column(String(500), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    platform = Column(String(100), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationship to models
    models = relationship("Model", back_populates="provider", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Provider id={self.id} provider='{self.provider}' is_active={self.is_active}>"


class Model(Base):
    __tablename__ = MODELS_TABLE

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    provider_id = Column(String, ForeignKey(f"{PROVIDERS_TABLE}.id"), nullable=False, index=True)
    model = Column(String(255), nullable=False)
    model_id = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    input_price_per_token = Column(Float, nullable=True)
    output_price_per_token = Column(Float, nullable=True)
    max_tokens = Column(Integer, nullable=True)
    context_window = Column(Integer, nullable=True)
    temperature = Column(Float, nullable=True, default=0.7)
    provider_type = Column(String(100), nullable=False, default="chat")
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationship to provider
    provider = relationship("Provider", back_populates="models")

    def __repr__(self):
        return f"<Model id={self.id} model='{self.model}' provider_id='{self.provider_id}' is_active={self.is_active}>"