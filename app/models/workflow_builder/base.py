"""
Base schemas for the workflow builder backend.

This module defines base Pydantic models that are used as the foundation
for other models in the application.
"""

from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field, field_validator, model_validator
import re


class BaseSchema(BaseModel):
    """
    Base schema for all models.
    
    This class provides common functionality for all models in the application.
    """
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BaseSchema":
        """
        Create a model instance from a dictionary.
        
        Args:
            data: The dictionary to create the model from.
            
        Returns:
            An instance of the model.
        """
        return cls(**data)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the model to a dictionary.
        
        Returns:
            A dictionary representation of the model.
        """
        return self.model_dump()


class IdentifiableSchema(BaseSchema):
    """
    Base schema for models with an ID.
    
    This class provides common functionality for models that have an ID field.
    """
    
    id: str = Field(..., description="The unique identifier for this object")
    
    @field_validator("id")
    @classmethod
    def validate_id(cls, v: str) -> str:
        """
        Validate the ID.
        
        Args:
            v: The ID to validate.
            
        Returns:
            The validated ID.
            
        Raises:
            ValueError: If the ID is invalid.
        """
        if not v:
            raise ValueError("ID cannot be empty")
        
        # Check that the ID is a valid identifier
        if not re.match(r"^[a-zA-Z0-9_-]+$", v):
            raise ValueError("ID can only contain letters, numbers, underscores, and hyphens")
        
        return v


class NamedSchema(BaseSchema):
    """
    Base schema for models with a name.
    
    This class provides common functionality for models that have a name field.
    """
    
    name: str = Field(..., description="The name of this object")
    display_name: str = Field(..., description="The display name of this object")
    
    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """
        Validate the name.
        
        Args:
            v: The name to validate.
            
        Returns:
            The validated name.
            
        Raises:
            ValueError: If the name is invalid.
        """
        if not v:
            raise ValueError("Name cannot be empty")
        
        return v
    
    @field_validator("display_name")
    @classmethod
    def validate_display_name(cls, v: str) -> str:
        """
        Validate the display name.
        
        Args:
            v: The display name to validate.
            
        Returns:
            The validated display name.
            
        Raises:
            ValueError: If the display name is invalid.
        """
        if not v:
            raise ValueError("Display name cannot be empty")
        
        return v


class DescribedSchema(NamedSchema):
    """
    Base schema for models with a description.
    
    This class provides common functionality for models that have a description field.
    """
    
    description: str = Field(..., description="The description of this object")
    
    @field_validator("description")
    @classmethod
    def validate_description(cls, v: str) -> str:
        """
        Validate the description.
        
        Args:
            v: The description to validate.
            
        Returns:
            The validated description.
            
        Raises:
            ValueError: If the description is invalid.
        """
        if not v:
            raise ValueError("Description cannot be empty")
        
        return v


class PositionedSchema(BaseSchema):
    """
    Base schema for models with a position.
    
    This class provides common functionality for models that have a position field.
    """
    
    position: Dict[str, float] = Field(..., description="The position of this object")
    
    @field_validator("position")
    @classmethod
    def validate_position(cls, v: Dict[str, float]) -> Dict[str, float]:
        """
        Validate the position.
        
        Args:
            v: The position to validate.
            
        Returns:
            The validated position.
            
        Raises:
            ValueError: If the position is invalid.
        """
        if "x" not in v or "y" not in v:
            raise ValueError("Position must have x and y coordinates")
        
        return v
