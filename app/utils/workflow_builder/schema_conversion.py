"""
Utilities for converting between JSON schemas and input definitions.
"""

import logging
from typing import Dict, List, Any, Type
from typing import Literal  # Use standard typing for Python 3.8+
from pydantic import BaseModel, create_model, Field

from app.models.workflow_builder.components import (
    InputBase,
    StringInput,
    IntInput,
    FloatInput,
    BoolInput,
    DropdownInput,
    ListInput,
    DictInput,
    HandleInput,
)

logger = logging.getLogger(__name__)


def create_input_schema_from_json_schema(
    schema: Dict[str, Any], name: str = "DynamicSchema"
) -> Type[BaseModel]:
    """
    Converts a JSON schema into a Pydantic model dynamically.

    Fields not listed as required are wrapped in Optional[...] and default to None if not provided.

    Args:
        schema: The JSON schema as a dictionary.
        name: The name to give the generated model.

    Returns:
        A Pydantic model class.
    """
    if schema.get("type") != "object":
        msg = "JSON schema must be of type 'object' at the root level."
        raise ValueError(msg)

    fields = {}
    properties = schema.get("properties", {})
    required_fields = set(schema.get("required", []))

    for field_name, field_def in properties.items():
        # Determine the base type from the JSON schema type string.
        field_type_str = field_def.get("type", "str")  # Defaults to string if not specified.
        base_type = {
            "string": str,
            "str": str,
            "integer": int,
            "int": int,
            "number": float,
            "boolean": bool,
            "array": list,
            "object": dict,
        }.get(field_type_str, Any)

        field_metadata = {"description": field_def.get("description", "")}

        # For non-required fields, set a default value.
        if field_name not in required_fields:
            field_metadata["default"] = field_def.get("default", None)

        # Handle enums for dropdown inputs
        if "enum" in field_def:
            enum_values = field_def["enum"]
            # Create a Literal type for the enum
            base_type = Literal[tuple(enum_values)]
            # Use first enum value as default if not specified
            if (
                field_name not in required_fields
                and "default" not in field_metadata
                and enum_values
            ):
                field_metadata["default"] = enum_values[0]

        fields[field_name] = (base_type, Field(**field_metadata))

    return create_model(name, **fields)


def _get_field_type(schema: Dict[str, Any]) -> Type:
    """
    Determine the Python type from a JSON schema property.

    Args:
        schema: The JSON schema property

    Returns:
        The corresponding Python type
    """
    schema_type = schema.get("type", "string")

    if schema_type == "string":
        return str
    elif schema_type == "integer":
        return int
    elif schema_type == "number":
        return float
    elif schema_type == "boolean":
        return bool
    elif schema_type == "array":
        items = schema.get("items", {})
        item_type = _get_field_type(items)
        return List[item_type]
    elif schema_type == "object":
        return Dict[str, Any]
    else:
        return Any


def pydantic_to_node_inputs(
    schema: Type[BaseModel],
    base_name: str = "",
    make_handles: bool = True,  # Default to True to match API request component pattern
) -> List[InputBase]:
    """
    Convert a Pydantic model to a list of node input definitions.

    Args:
        schema: The Pydantic model to convert
        base_name: Optional prefix for input names
        make_handles: If True, all inputs will have is_handle=True to enable connections

    Returns:
        A list of InputBase objects
    """
    logger.info(
        f"Converting Pydantic model {schema.__name__} to node inputs with base_name: {base_name}"
    )
    logger.info(f"Model fields: {list(schema.__fields__.keys())}")
    inputs = []

    for field_name, field_info in schema.__fields__.items():
        display_name = field_info.field_info.description or field_name.replace("_", " ").title()
        name = f"{base_name}{field_name}" if base_name else field_name

        # Get field type and create appropriate input
        field_type = field_info.annotation
        field_default = field_info.default if field_info.default is not ... else None

        # Common parameters for all input types
        common_params = {
            "name": name,
            "display_name": display_name,
            "info": field_info.field_info.description or "",
            "is_handle": make_handles,  # Set is_handle based on the parameter
        }

        # Check if it's an enum (Literal type)
        if hasattr(field_type, "__origin__") and field_type.__origin__ is Literal:
            options = list(field_type.__args__)
            inputs.append(
                DropdownInput(
                    **common_params,
                    options=options,
                    value=(
                        field_default
                        if field_default is not None
                        else (options[0] if options else None)
                    ),
                    input_types=["string", "Any"] if make_handles else None,
                )
            )
        elif field_type is str:
            inputs.append(
                StringInput(
                    **common_params,
                    value=field_default if field_default is not None else "",
                    input_types=["string", "Any"] if make_handles else None,
                )
            )
        elif field_type is int:
            inputs.append(
                IntInput(
                    **common_params,
                    value=field_default if field_default is not None else 0,
                    input_types=["int", "number", "Any"] if make_handles else None,
                )
            )
        elif field_type is float:
            inputs.append(
                FloatInput(
                    **common_params,
                    value=field_default if field_default is not None else 0.0,
                    input_types=["float", "number", "Any"] if make_handles else None,
                )
            )
        elif field_type is bool:
            inputs.append(
                BoolInput(
                    **common_params,
                    value=field_default if field_default is not None else False,
                    input_types=["bool", "Any"] if make_handles else None,
                )
            )
        elif hasattr(field_type, "__origin__") and field_type.__origin__ is list:
            inputs.append(
                ListInput(
                    **common_params,
                    value=field_default if field_default is not None else [],
                    input_types=["list", "Any"] if make_handles else None,
                )
            )
        elif hasattr(field_type, "__origin__") and field_type.__origin__ is dict:
            inputs.append(
                DictInput(
                    **common_params,
                    value=field_default if field_default is not None else {},
                    input_types=["dict", "Any"] if make_handles else None,
                )
            )
        else:
            # Default to string input for complex types
            inputs.append(
                StringInput(
                    **common_params,
                    value=str(field_default) if field_default is not None else "",
                    info=f"{field_info.field_info.description or ''} (Complex type)",
                    input_types=["string", "Any"] if make_handles else None,
                )
            )

    return inputs
