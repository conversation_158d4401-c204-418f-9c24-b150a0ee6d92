"""
Component discovery utilities.

This module provides functions for discovering and validating components.
"""

import importlib
import inspect
import pkgutil
import traceback
from pathlib import Path
from typing import Dict, Type, Any, List, Tuple

# Import BaseNode and Input/Output definitions
# Ensure components.core.base_node exists and BaseNode is defined
try:
    from app.components.core.base_node import BaseNode
except ImportError:
    print(
        "ERROR: Cannot import BaseNode from components.core.base_node. Discovery will likely fail."
    )
    BaseNode = None  # Set to None to prevent errors later, but discovery won't work well

# Ensure these definition files exist
try:
    # These imports are needed for component validation
    from app.models.workflow_builder.components import (
        InputBase,
    )  # Need InputBase to create handle defs
    from app.models.workflow_builder.components import Output
except ImportError:
    print("ERROR: Cannot import InputBase/Output from schemas.components. Check paths.")
    InputBase = None
    Output = None

# Import MCP definition loader and type mapper
try:
    from app.components.tools.mcp_definitions import (
        load_mcp_definitions,
        map_mcp_type_to_node_type,
        get_json_schema_from_mcp_schema,
    )
except ImportError:
    print("ERROR: Cannot import MCP definition helpers from components.tools.mcp_definitions.")

    # Define dummy functions if MCP part is optional or handled elsewhere
    def load_mcp_definitions():
        return []

    def map_mcp_type_to_node_type(t):
        return "any"

    def get_json_schema_from_mcp_schema(schema):
        return {"type": "object", "properties": {}}


def validate_component_interface(component_class: Type) -> Tuple[bool, List[str]]:
    """
    Validates that a component class follows the standardized interface pattern.

    Args:
        component_class: The component class to validate.

    Returns:
        A tuple of (is_valid, issues) where is_valid is a boolean indicating if the component
        follows the standardized interface, and issues is a list of strings describing any issues.
    """
    issues = []

    # Check if the class has an execute method
    if not hasattr(component_class, "execute"):
        issues.append(f"Component {component_class.__name__} does not have an execute method.")
        return False, issues

    # Get the execute method
    execute_method = getattr(component_class, "execute")

    # Check if it's a method (not a property or something else)
    if not inspect.isfunction(execute_method) and not inspect.iscoroutinefunction(execute_method):
        issues.append(f"Component {component_class.__name__}'s execute attribute is not a method.")
        return False, issues

    # Check if it's an async method
    if not inspect.iscoroutinefunction(execute_method):
        issues.append(f"Component {component_class.__name__}'s execute method is not async.")

    # Check the method signature
    try:
        sig = inspect.signature(execute_method)
        params = list(sig.parameters.keys())

        # Check if it has the right parameters
        if len(params) != 2 or params[0] != "self" or params[1] != "context":
            issues.append(
                f"Component {component_class.__name__}'s execute method has incorrect parameters: {params}"
            )

    except Exception as e:
        issues.append(f"Error checking execute method signature: {e}")

    # Check if the class has a build method with a deprecation warning
    if hasattr(component_class, "build"):
        build_method = getattr(component_class, "build")
        if inspect.isfunction(build_method) or inspect.iscoroutinefunction(build_method):
            # Check if the build method has a docstring
            if build_method.__doc__:
                # Check if the docstring contains a deprecation warning
                if (
                    "deprecated" not in build_method.__doc__.lower()
                    and "warning" not in build_method.__doc__.lower()
                ):
                    issues.append(
                        f"Component {component_class.__name__}'s build method does not have a deprecation warning."
                    )
            else:
                issues.append(
                    f"Component {component_class.__name__}'s build method does not have a docstring."
                )

    return len(issues) == 0, issues


COMPONENT_PACKAGE_PATH = "app.components"
try:
    component_package = importlib.import_module(COMPONENT_PACKAGE_PATH)
    BASE_COMPONENT_DIR = Path(component_package.__path__[0])
except ImportError:
    print(f"ERROR: Could not import component package '{COMPONENT_PACKAGE_PATH}'")
    component_package = None
    BASE_COMPONENT_DIR = None


def generate_mcp_tool_definitions() -> Dict[str, Dict[str, Any]]:
    """Generates component definitions dynamically from MCP JSON config."""
    # --- (Keep the existing MCP generation logic exactly as it is) ---
    # --- (It correctly sets a different kind of 'path' for MCP tools) ---
    mcp_components: Dict[str, Dict[str, Any]] = {}
    try:
        mcp_defs = load_mcp_definitions()
    except Exception as e:
        print(f"ERROR loading MCP definitions: {e}")
        traceback.print_exc()
        mcp_defs = []

    category = "MCP Marketplace"

    for server_def in mcp_defs:
        server_id = server_def.get("id")
        server_display = server_def.get("display_name", server_id)
        server_desc = server_def.get("description", f"Tools from {server_display}")
        server_path = server_def.get("server_script_path", "Unknown Path")

        if not server_id:
            print("Warning: Skipping MCP definition without an 'id'.")
            continue

        tools = server_def.get("server_tools", [])
        if not tools:
            print(f"Warning: MCP definition '{server_id}' has no 'server_tools'.")
            continue

        for tool_def in tools:
            tool_name = tool_def.get("tool_name")
            if not tool_name:
                print(f"Warning: Skipping tool in '{server_id}' without 'tool_name'.")
                continue

            component_name = f"mcp_{server_id}_{tool_name}"
            component_display_name = f"{server_display}: {tool_name.replace('_', ' ').title()}"

            component_inputs: List[Dict[str, Any]] = []
            input_schema = tool_def.get("input_schema", {}).get("predefined_fields", [])
            if InputBase:  # Check if InputBase was imported
                for field in input_schema:
                    field_name = field.get("field_name")
                    if not field_name:
                        continue
                    data_type_info = field.get("data_type", {})
                    mcp_type = data_type_info.get("type", "string")
                    node_type = map_mcp_type_to_node_type(mcp_type)
                    is_required = field.get("required", False)
                    field_description = data_type_info.get(
                        "description", f"Input data for {field_name}"
                    )
                    display_name = field_name.replace("_", " ").title()

                    # Create a single input field that can be both directly edited and connected via handle
                    # Following the pattern used in the API request component
                    input_def = None
                    if mcp_type == "string":
                        input_def = {
                            "name": field_name,
                            "display_name": display_name,
                            "info": field_description,
                            "input_type": "string",
                            "input_types": [
                                "string",
                                "Any",
                            ],  # Accept both string and Any for connections
                            "required": is_required,
                            "is_handle": True,  # Mark as handle to enable connections
                            "is_list": False,
                            "real_time_refresh": False,
                            "advanced": False,
                            "value": "",
                        }
                    elif mcp_type == "number" or mcp_type == "integer":
                        input_def = {
                            "name": field_name,
                            "display_name": display_name,
                            "info": field_description,
                            "input_type": "number" if mcp_type == "number" else "int",
                            "input_types": [
                                "number",
                                "int",
                                "Any",
                            ],  # Accept numeric and Any for connections
                            "required": is_required,
                            "is_handle": True,  # Mark as handle to enable connections
                            "is_list": False,
                            "real_time_refresh": False,
                            "advanced": False,
                            "value": 0,
                        }
                    elif mcp_type == "boolean":
                        input_def = {
                            "name": field_name,
                            "display_name": display_name,
                            "info": field_description,
                            "input_type": "bool",
                            "input_types": [
                                "bool",
                                "Any",
                            ],  # Accept bool and Any for connections
                            "required": is_required,
                            "is_handle": True,  # Mark as handle to enable connections
                            "is_list": False,
                            "real_time_refresh": False,
                            "advanced": False,
                            "value": False,
                        }
                    elif mcp_type == "array":
                        input_def = {
                            "name": field_name,
                            "display_name": display_name,
                            "info": field_description,
                            "input_type": "list",
                            "input_types": [
                                "list",
                                "Any",
                            ],  # Accept list and Any for connections
                            "required": is_required,
                            "is_handle": True,  # Mark as handle to enable connections
                            "is_list": True,
                            "real_time_refresh": False,
                            "advanced": False,
                            "value": [],
                        }
                    elif mcp_type == "object":
                        input_def = {
                            "name": field_name,
                            "display_name": display_name,
                            "info": field_description,
                            "input_type": "dict",
                            "input_types": [
                                "dict",
                                "Any",
                            ],  # Accept dict and Any for connections
                            "required": is_required,
                            "is_handle": True,  # Mark as handle to enable connections
                            "is_list": False,
                            "real_time_refresh": False,
                            "advanced": False,
                            "value": {},
                        }
                    else:
                        # Default to string for unknown types
                        input_def = {
                            "name": field_name,
                            "display_name": display_name,
                            "info": field_description,
                            "input_type": "string",
                            "input_types": [
                                "string",
                                "Any",
                            ],  # Accept string and Any for connections
                            "required": is_required,
                            "is_handle": True,  # Mark as handle to enable connections
                            "is_list": False,
                            "real_time_refresh": False,
                            "advanced": False,
                            "value": "",
                        }

                    # Add the input to the component inputs
                    component_inputs.append(input_def)

            component_outputs: List[Dict[str, Any]] = []
            output_schema = tool_def.get("output_schema", {}).get("predefined_fields", [])
            if Output:  # Check if Output was imported
                for field in output_schema:
                    field_name = field.get("field_name")
                    if not field_name:
                        continue
                    data_type_info = field.get("data_type", {})
                    mcp_type = data_type_info.get("type", "string")
                    node_type = map_mcp_type_to_node_type(mcp_type)
                    handle_output = Output(
                        name=field_name,
                        display_name=field_name.replace("_", " ").title(),
                        output_type=node_type,
                        method=None,
                    ).dict()
                    component_outputs.append(handle_output)

            # Create a default config object with initial values for all inputs
            default_config = {}
            for input_def in component_inputs:
                if not input_def.get("is_handle", False):
                    default_config[input_def["name"]] = input_def.get("value", "")

            definition = {
                "name": component_name,
                "display_name": component_display_name,
                "description": tool_def.get("description", server_desc),
                "category": category,
                "icon": "ServerCog",
                "beta": True,
                "inputs": component_inputs,
                "outputs": component_outputs,
                "is_valid": True,
                "config": default_config,  # Add default config with initial values
                "type": "MCPMarketplaceComponent",  # Set a specific type for the frontend to recognize
                "mcp_info": {
                    "server_id": server_id,
                    "server_path": server_path,
                    "tool_name": tool_name,
                    "tool_id": tool_def.get("tool_id"),
                    "endpoint": tool_def.get("endpoint"),
                    "input_schema": get_json_schema_from_mcp_schema(
                        tool_def.get("input_schema", {})
                    ),
                    "output_schema": get_json_schema_from_mcp_schema(
                        tool_def.get("output_schema", {})
                    ),
                },
                # Path for MCP tools is just informational, not for Python import
                "path": f"mcp_marketplace.{component_name}",
            }
            mcp_components[component_name] = definition

    print(f"Finished generating {len(mcp_components)} MCP marketplace component definitions.")
    return mcp_components


def create_minimal_component_set() -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    Creates a minimal set of components when no components are found.
    This ensures that the frontend can still function even if component discovery fails.

    Returns:
        A dictionary of component definitions by category.
    """
    print("Creating minimal component set...")

    # Create a minimal StartNode component
    start_node = {
        "name": "StartNode",
        "display_name": "Start",
        "description": "The starting point for all workflows.",
        "category": "Input/Output",
        "icon": "Play",
        "beta": False,
        "inputs": [],
        "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}],
        "is_valid": True,
        "path": "components.io.start_node",
    }

    # Return the minimal component set
    return {"Input/Output": {"StartNode": start_node}}


def discover_components() -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    Discovers BaseNode subclasses AND dynamically generates MCP tool definitions.
    Ensures the 'path' key for Python nodes is correctly set for dynamic import.
    """
    if not BaseNode:  # Basic check
        print("ERROR: BaseNode class is not available. Cannot discover Python components.")
        return {}  # Return early if BaseNode is missing

    all_components: Dict[str, Dict[str, Any]] = {}  # Combined components by category

    # --- 1. Discover Python Class Components ---
    print("Starting Python component discovery...")
    if component_package and BASE_COMPONENT_DIR:
        for _, module_name, _ in pkgutil.walk_packages(  # is_pkg not needed here
            path=component_package.__path__,
            prefix=component_package.__name__ + ".",
            onerror=lambda x: print(f"Error walking package: {x}"),  # Log errors
        ):
            try:
                module = importlib.import_module(module_name)
                # Iterate over attributes that are classes defined in this module
                for name, obj in inspect.getmembers(module, inspect.isclass):
                    # Special handling for AI components
                    is_ai_component = "components.ai" in module_name and name in [
                        "AgenticAI",
                        "OpenAIModule",
                        "BasicLLMChain",
                        "InformationExtractor",
                        "QuestionAnswerModule",
                        "SentimentAnalyzer",
                        "Summarizer",
                        "Classifier",
                    ]

                    if (
                        obj.__module__ == module_name  # Only classes defined in *this* module
                        and issubclass(obj, BaseNode)  # Must be a subclass of BaseNode
                        and obj is not BaseNode  # Cannot be BaseNode itself
                        and (not getattr(obj, "is_abstract", False) or is_ai_component)
                    ):  # Skip explicitly abstract classes, but include AI components

                        component_name = getattr(
                            obj, "name", name
                        )  # Use defined name or class name
                        category = getattr(obj, "category", "Uncategorized")

                        if category not in all_components:
                            all_components[category] = {}
                        if component_name in all_components[category]:
                            print(
                                f"  Warning: Duplicate component name '{component_name}' found. Overwriting definition."
                            )

                        try:
                            # Get the definition dictionary from the class method
                            definition = obj.get_definition()

                            # --- >>> CRITICAL FIX: Ensure correct path <<< ---
                            # Override or set the 'path' key to the exact import path
                            # regardless of what get_definition might have returned.
                            correct_path = f"{obj.__module__}.{obj.__name__}"
                            if definition.get("path") != correct_path:
                                # print(f"    Correcting path for '{component_name}': Was '{definition.get('path')}', Now '{correct_path}'")
                                pass  # Quietly correct, or print if debugging
                            definition["path"] = correct_path
                            # --- >>> END FIX <<< ---

                            # Validate the component interface
                            is_valid, issues = validate_component_interface(obj)
                            if not is_valid:
                                # Only log critical interface issues
                                if any(
                                    "does not have an execute method" in issue for issue in issues
                                ):
                                    print(
                                        f"  WARNING: Component {component_name} has critical interface issues."
                                    )

                            # Ensure basic keys exist (even if get_definition failed partially)
                            definition.setdefault("name", component_name)
                            definition.setdefault("category", category)
                            definition.setdefault(
                                "is_valid", is_valid
                            )  # Set based on interface validation
                            definition.setdefault(
                                "interface_issues", issues
                            )  # Store any interface issues

                            all_components[category][component_name] = definition

                        except Exception as e:
                            print(f"  ERROR: Failed to load component {component_name}: {e}")
                            # Store an error definition
                            all_components[category][component_name] = {
                                "name": component_name,
                                "display_name": f"{component_name} (Load Error)",
                                "description": f"Failed to load definition: {e}",
                                "category": category,
                                "icon": "AlertTriangle",
                                "beta": False,
                                "inputs": [],
                                "outputs": [],
                                "is_valid": False,  # Mark as invalid
                                "path": f"{module_name}.{name}",  # Store original path attempt
                            }

            except ImportError as e:
                print(f"ERROR: Could not import module {module_name}: {e}")
            except Exception as e:
                print(f"ERROR: Unexpected error processing module {module_name}: {e}")
                traceback.print_exc()  # Print traceback for unexpected module errors
    else:
        print(
            "Warning: Component package not found or not importable. Skipping Python component discovery."
        )
    print("Finished Python component discovery.")

    # --- 2. Generate MCP Tool Definitions ---
    mcp_tool_defs_by_name = generate_mcp_tool_definitions()
    mcp_category_name = "MCP Marketplace"  # Consistent category name

    if mcp_tool_defs_by_name:
        if mcp_category_name not in all_components:
            all_components[mcp_category_name] = {}
        # Merge MCP definitions into the main dictionary
        for name, definition in mcp_tool_defs_by_name.items():
            if name in all_components[mcp_category_name]:
                print(f"  Warning: Duplicate MCP component name '{name}'. Overwriting.")
            all_components[mcp_category_name][name] = definition

    total_components = sum(len(v) for v in all_components.values())
    print(
        f"Discovery complete. Found {total_components} total components in {len(all_components)} categories."
    )

    # Print component counts by category
    print("Component counts by category:")
    for category, components in sorted(all_components.items()):
        print(f"  - {category}: {len(components)} components")

    return all_components
