"""
Utility functions to transform workflow payloads.

This module provides functions for transforming workflow payloads between
different formats and for validating workflow structures.
"""

import copy
from typing import Dict, Any, List, Set


def fix_mcp_marketplace_node(node: Dict[str, Any]) -> Dict[str, Any]:
    """
    Fix an MCP Marketplace node to match the expected template.

    Args:
        node: The node to fix

    Returns:
        The fixed node
    """
    # Create a deep copy to avoid modifying the original
    fixed_node = copy.deepcopy(node)

    node_id = fixed_node.get("id", "")
    node_data = fixed_node.get("data", {})
    node_type = node_data.get("type", "")
    original_type = node_data.get("originalType", "")

    # Check if this is an MCP Marketplace component
    is_mcp_marketplace = (
        node_type == "mcp"
        or node_type == "MCPMarketplaceComponent"
        or "mcp_" in original_type
    )

    # Check if this is an API request node
    is_api_request = (
        original_type == "ApiRequestNode"
        or node_type == "ApiRequestNode"
        or (node_type == "component" and original_type == "ApiRequestNode")
    )

    # Check if this is an AgenticAI component
    is_agentic_ai = (
        original_type == "AgenticAI"
        or node_type == "AgenticAI"
        or (node_type == "component" and original_type == "AgenticAI")
    )

    # Only process MCP Marketplace components, API request nodes, and AgenticAI components
    if not (is_mcp_marketplace or is_api_request or is_agentic_ai):
        return fixed_node

    # Get the node definition and config
    definition = node_data.get("definition", {})
    config = node_data.get("config", {})

    # Extract input values with priority order
    input_values = {}

    # Print detailed information about the node for debugging
    print(f"\n[TRANSFORM] Processing node: {node_id}")
    print(f"[TRANSFORM] Node type: {node_type}")
    print(f"[TRANSFORM] Original type: {original_type}")
    print(f"[TRANSFORM] Is MCP Marketplace: {is_mcp_marketplace}")
    print(f"[TRANSFORM] Is API Request: {is_api_request}")
    print(f"[TRANSFORM] Is AgenticAI: {is_agentic_ai}")
    print(f"[TRANSFORM] Original config: {config}")

    # PRIORITY 1: Direct values in the config (highest priority)
    # These are values that were explicitly set in the frontend
    if isinstance(config, dict) and config:
        print(f"[TRANSFORM] Checking direct config values")
        for key, value in config.items():
            # Skip special keys that are not user inputs and skip the inputs array
            if (
                key
                not in [
                    "mode",
                    "selected_tool_name",
                    "stdio_command",
                    "tool_args",
                    "node_id",
                    "_internal_state",
                    "inputs",
                ]
                and value is not None
                and value != ""
            ):
                input_values[key] = value
                print(f"[TRANSFORM] Found config value: {key}={value}")

    # PRIORITY 2: Component state config
    # These are values that might be stored in the component state
    if "component_state" in node_data and isinstance(
        node_data["component_state"], dict
    ):
        component_state = node_data.get("component_state", {})
        print(f"[TRANSFORM] Checking component_state")

        # Check if there's a config in the component state
        if "config" in component_state and isinstance(component_state["config"], dict):
            component_config = component_state.get("config", {})
            for key, value in component_config.items():
                if value is not None and value != "" and key not in input_values:
                    input_values[key] = value
                    print(
                        f"[TRANSFORM] Found component_state.config value: {key}={value}"
                    )

        # Also check for direct values in the component state
        for key, value in component_state.items():
            if (
                key != "config"
                and value is not None
                and value != ""
                and key not in input_values
            ):
                input_values[key] = value
                print(f"[TRANSFORM] Found component_state value: {key}={value}")

    # PRIORITY 3: Tool args in the config
    # These are values that might be set for MCP Tools
    if "tool_args" in config and isinstance(config["tool_args"], dict):
        tool_args = config.get("tool_args", {})
        print(f"[TRANSFORM] Checking tool_args")
        for name, value in tool_args.items():
            if value is not None and value != "" and name not in input_values:
                input_values[name] = value
                print(f"[TRANSFORM] Found tool_args value: {name}={value}")

    # PRIORITY 4: Internal state
    # This is where the values might be stored when sent from the frontend
    if "internal_state" in node_data and isinstance(node_data["internal_state"], dict):
        internal_state = node_data.get("internal_state", {})
        print(f"[TRANSFORM] Checking internal_state")
        for key, value in internal_state.items():
            if value is not None and value != "" and key not in input_values:
                input_values[key] = value
                print(f"[TRANSFORM] Found internal_state value: {key}={value}")

    # PRIORITY 5: State
    # This is another place where the values might be stored
    if "state" in node_data and isinstance(node_data["state"], dict):
        state = node_data.get("state", {})
        print(f"[TRANSFORM] Checking state")
        for key, value in state.items():
            if value is not None and value != "" and key not in input_values:
                input_values[key] = value
                print(f"[TRANSFORM] Found state value: {key}={value}")

    # PRIORITY 6: MCP info input values
    # This is another place where the values might be stored
    if "mcp_info" in definition and isinstance(definition["mcp_info"], dict):
        mcp_info = definition.get("mcp_info", {})
        print(f"[TRANSFORM] Checking mcp_info")
        if "input_values" in mcp_info and isinstance(mcp_info["input_values"], dict):
            input_values_from_mcp_info = mcp_info.get("input_values", {})
            for key, value in input_values_from_mcp_info.items():
                if value is not None and value != "" and key not in input_values:
                    input_values[key] = value
                    print(
                        f"[TRANSFORM] Found mcp_info.input_values value: {key}={value}"
                    )

    # PRIORITY 7: Values from inputs in the definition (lowest priority)
    # These are the default values defined in the component
    if "inputs" in definition and isinstance(definition["inputs"], list):
        inputs = definition.get("inputs", [])
        print(f"[TRANSFORM] Checking definition inputs")
        for input_def in inputs:
            if not isinstance(input_def, dict):
                continue

            name = input_def.get("name")
            value = input_def.get("value")

            # Skip empty values and handle inputs
            if (
                name is None
                or value is None
                or value == ""
                or (isinstance(value, (dict, list)) and not value)
                or name.endswith("_handle")
            ):
                continue

            # Only add if not already set from higher priority sources
            if name not in input_values:
                input_values[name] = value
                print(f"[TRANSFORM] Found definition input value: {name}={value}")

    # PRIORITY 8: Direct values in node_data
    # This is another place where the values might be stored
    print(f"[TRANSFORM] Checking node_data direct values")
    for key, value in node_data.items():
        if (
            key
            not in [
                "label",
                "type",
                "originalType",
                "definition",
                "config",
                "internal_state",
                "state",
                "component_state",
            ]
            and value is not None
            and value != ""
            and key not in input_values
        ):
            input_values[key] = value
            print(f"[TRANSFORM] Found node_data value: {key}={value}")

    # Special handling for API request nodes to ensure method is preserved
    if (
        original_type == "ApiRequestNode"
        or node_type == "ApiRequestNode"
        or (node_type == "component" and original_type == "ApiRequestNode")
    ):
        print(
            f"[TRANSFORM] Special handling for API request node {node_id} in fix_mcp_marketplace_node"
        )

        # Make sure to preserve the method value from the original config
        if "config" in node_data and isinstance(node_data["config"], dict):
            method_value = node_data["config"].get("method")
            if method_value:
                print(
                    f"[TRANSFORM] Preserving method {method_value} for API request node {node_id} in fix_mcp_marketplace_node"
                )
                input_values["method"] = method_value
            else:
                print(
                    f"[TRANSFORM] No method value found in config for API request node {node_id}"
                )

        # Also check for method in the input values
        if "method" not in input_values:
            # Default to GET if no method value is found
            input_values["method"] = "GET"
            print(
                f"[TRANSFORM] Setting default method GET for API request node {node_id} in fix_mcp_marketplace_node"
            )

    # Add node metadata to the config for AgenticAI components
    if original_type == "AgenticAI" or node_type == "AgenticAI":
        # Add the node ID and name to the config
        input_values["id"] = node_id
        input_values["name"] = node_data.get("label", "Untitled Agent")
        print(f"[TRANSFORM] Added AgenticAI metadata: id={node_id}, name={input_values['name']}")

    # Create a new config with the input values directly
    node_data["config"] = input_values

    # Print final transformed config
    print(f"[TRANSFORM] Final transformed config for node {node_id}: {input_values}")

    return fixed_node


def is_tool_handle(handle_name: str) -> bool:
    """
    Check if a handle name represents a tool connection.
    
    Args:
        handle_name: The handle name to check (e.g., 'tool_1', 'tool_2', 'input_data')
        
    Returns:
        True if the handle is a tool handle
    """
    import re
    return bool(re.match(r'^tool_\d+$', handle_name))


def get_connected_nodes_with_tool_connections(
    nodes: List[Dict[str, Any]], edges: List[Dict[str, Any]], start_node_id: str
) -> Set[str]:
    """
    Get all nodes connected to the start node using BFS, including tool connections.
    
    Args:
        nodes: The list of nodes in the workflow
        edges: The list of edges in the workflow
        start_node_id: The ID of the start node
        
    Returns:
        A set of node IDs connected to the start node (including via tool connections)
    """
    connected_nodes = {start_node_id}
    queue = [start_node_id]
    
    # Create adjacency list for regular connections
    adjacency_list = {}
    for node in nodes:
        node_id = node.get("id", "")
        adjacency_list[node_id] = []
    
    # Populate adjacency list with regular connections
    for edge in edges:
        source = edge.get("source", "")
        target = edge.get("target", "")
        if source and target:
            adjacency_list[source].append(target)
    
    # Track nodes we've processed to avoid infinite loops
    processed_nodes = set()
    
    # BFS to find all connected nodes
    while queue:
        current_node_id = queue.pop(0)
        if current_node_id in processed_nodes:
            continue
        processed_nodes.add(current_node_id)
        
        # Add regular connections
        neighbors = adjacency_list.get(current_node_id, [])
        for neighbor_id in neighbors:
            if neighbor_id not in connected_nodes:
                connected_nodes.add(neighbor_id)
                queue.append(neighbor_id)
        
        # Add tool connections - find components connected to this node via tool handles
        for edge in edges:
            target = edge.get("target", "")
            target_handle = edge.get("targetHandle", "")
            source = edge.get("source", "")
            
            if target == current_node_id and target_handle and is_tool_handle(target_handle):
                if source not in connected_nodes:
                    connected_nodes.add(source)
                    queue.append(source)
    
    return connected_nodes


def get_connected_nodes(
    nodes: List[Dict[str, Any]], edges: List[Dict[str, Any]]
) -> Set[str]:
    """
    Get the set of node IDs that are connected to the start node, including tool connections.

    Args:
        nodes: The list of nodes in the workflow
        edges: The list of edges in the workflow

    Returns:
        A set of node IDs that are connected to the start node
    """
    # Find the start node
    start_node = None
    for node in nodes:
        node_data = node.get("data", {})
        original_type = node_data.get("originalType", "")
        if original_type == "StartNode":
            start_node = node
            break
    
    if not start_node:
        print("[DEBUG] No StartNode found in workflow")
        # Return all nodes that have any connections as fallback
        connected_nodes = set()
        for edge in edges:
            connected_nodes.add(edge.get("source", ""))
            connected_nodes.add(edge.get("target", ""))
        return connected_nodes
    
    start_node_id = start_node.get("id", "")
    print(f"[DEBUG] Found StartNode with ID: {start_node_id}")
    
    # Use the enhanced tool-aware connection logic
    connected_nodes = get_connected_nodes_with_tool_connections(nodes, edges, start_node_id)
    print(f"[DEBUG] Connected nodes (including tool connections): {connected_nodes}")
    
    return connected_nodes


def collect_missing_required_fields(
    nodes: List[Dict[str, Any]], edges: List[Dict[str, Any]], connected_nodes: Set[str]
) -> List[Dict[str, Any]]:
    """
    Collect information about missing required fields in the workflow.

    Args:
        nodes: The list of nodes in the workflow
        edges: The list of edges in the workflow
        connected_nodes: The set of node IDs that are connected to other nodes

    Returns:
        A list of dictionaries containing information about missing required fields
    """
    missing_fields = []

    for node in nodes:
        node_id = node.get("id", "")
        node_data = node.get("data", {})
        node_type = node_data.get("type", "")
        original_type = node_data.get("originalType", "")
        definition = node_data.get("definition", {})
        config = node_data.get("config", {})

        # Skip nodes that are not connected to any other node
        if node_id not in connected_nodes:
            continue

        # Check if the node has inputs
        inputs = definition.get("inputs", [])
        for input_def in inputs:
            if not isinstance(input_def, dict):
                continue

            name = input_def.get("name", "")
            display_name = input_def.get("display_name", name)
            is_required = input_def.get("required", False)
            is_handle = input_def.get("is_handle", False)

            # Skip non-required inputs
            if not is_required:
                continue

            # Check if the input is connected
            is_connected = False
            for edge in edges:
                if edge.get("target") == node_id and edge.get("targetHandle") == name:
                    is_connected = True
                    break

            # If the input is not connected, check if it has a value in the config
            if not is_connected:
                value = config.get(name)
                if value is None or value == "":
                    missing_fields.append(
                        {
                            "node_id": node_id,
                            "node_label": node_data.get("label", ""),
                            "input_name": name,
                            "input_display_name": display_name,
                            "is_handle": is_handle,
                        }
                    )

    return missing_fields
