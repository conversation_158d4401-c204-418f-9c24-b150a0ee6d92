"""
Universal Schema Validator - DRY implementation for schema validation across services
"""

import time
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Result of schema validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    validation_time: float
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []


class UniversalSchemaValidator:
    """
    Universal schema validator implementing DRY principles.
    
    This class provides consistent schema validation across all services,
    eliminating code duplication and ensuring uniform validation logic.
    """
    
    def __init__(self):
        """Initialize the universal schema validator."""
        self.supported_schema_types = ["autogen", "mcp", "component"]
        self.validation_stats = {
            "total_validations": 0,
            "successful_validations": 0,
            "failed_validations": 0,
            "average_validation_time": 0.0
        }
        
        logger.info("UniversalSchemaValidator initialized")
    
    def validate_autogen_schema(self, schema: Dict[str, Any]) -> ValidationResult:
        """
        Validate AutoGen tool schema format.
        
        Args:
            schema: AutoGen schema dictionary
            
        Returns:
            ValidationResult with validation status and details
        """
        start_time = time.time()
        errors = []
        warnings = []
        
        try:
            # Check required fields
            required_fields = ["name", "description", "parameters"]
            for field in required_fields:
                if field not in schema:
                    errors.append(f"Missing required field: {field}")
            
            # Validate name format
            if "name" in schema:
                name = schema["name"]
                if not isinstance(name, str) or not name.strip():
                    errors.append("Field 'name' must be a non-empty string")
                elif not name.replace("_", "").replace("-", "").isalnum():
                    warnings.append("Field 'name' should contain only alphanumeric characters, underscores, and hyphens")
            
            # Validate description
            if "description" in schema:
                description = schema["description"]
                if not isinstance(description, str) or not description.strip():
                    errors.append("Field 'description' must be a non-empty string")
                elif len(description) < 10:
                    warnings.append("Field 'description' should be at least 10 characters long")
            
            # Validate parameters structure
            if "parameters" in schema:
                parameters = schema["parameters"]
                if not isinstance(parameters, dict):
                    errors.append("Field 'parameters' must be a dictionary")
                else:
                    param_errors = self._validate_parameters_structure(parameters)
                    errors.extend(param_errors)
            
            # Validate strict field if present
            if "strict" in schema:
                if not isinstance(schema["strict"], bool):
                    errors.append("Field 'strict' must be a boolean")
            
        except Exception as e:
            errors.append(f"Validation error: {str(e)}")
        
        validation_time = time.time() - start_time
        self._update_validation_stats(len(errors) == 0, validation_time)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            validation_time=validation_time
        )
    
    def validate_component_schema(self, component: Dict[str, Any]) -> ValidationResult:
        """
        Validate workflow component schema format.
        
        Args:
            component: Component schema dictionary
            
        Returns:
            ValidationResult with validation status and details
        """
        start_time = time.time()
        errors = []
        warnings = []
        
        try:
            # Check required component fields
            required_fields = ["component_id", "component_type", "component_schema"]
            for field in required_fields:
                if field not in component:
                    errors.append(f"Missing required component field: {field}")
            
            # Validate component_id
            if "component_id" in component:
                component_id = component["component_id"]
                if not isinstance(component_id, str) or not component_id.strip():
                    errors.append("Field 'component_id' must be a non-empty string")
            
            # Validate component_type
            if "component_type" in component:
                component_type = component["component_type"]
                if not isinstance(component_type, str) or not component_type.strip():
                    errors.append("Field 'component_type' must be a non-empty string")
            
            # Validate component_schema
            if "component_schema" in component:
                component_schema = component["component_schema"]
                if not isinstance(component_schema, dict):
                    errors.append("Field 'component_schema' must be a dictionary")
                else:
                    # Validate the nested AutoGen schema
                    autogen_result = self.validate_autogen_schema(component_schema)
                    errors.extend(autogen_result.errors)
                    warnings.extend(autogen_result.warnings)
            
            # Validate optional component_name
            if "component_name" in component:
                component_name = component["component_name"]
                if not isinstance(component_name, str):
                    warnings.append("Field 'component_name' should be a string")
        
        except Exception as e:
            errors.append(f"Component validation error: {str(e)}")
        
        validation_time = time.time() - start_time
        self._update_validation_stats(len(errors) == 0, validation_time)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            validation_time=validation_time
        )
    
    def validate_mcp_schema(self, mcp_component: Dict[str, Any]) -> ValidationResult:
        """
        Validate MCP marketplace component schema format.
        
        Args:
            mcp_component: MCP component schema dictionary
            
        Returns:
            ValidationResult with validation status and details
        """
        start_time = time.time()
        errors = []
        warnings = []
        
        try:
            # First validate as regular component
            component_result = self.validate_component_schema(mcp_component)
            errors.extend(component_result.errors)
            warnings.extend(component_result.warnings)
            
            # Additional MCP-specific validation
            if "component_type" in mcp_component:
                if mcp_component["component_type"] != "MCPMarketplace":
                    errors.append("MCP component must have component_type 'MCPMarketplace'")
            
            # Validate MCP metadata
            if "mcp_metadata" not in mcp_component:
                errors.append("MCP component must have 'mcp_metadata' field")
            else:
                mcp_metadata = mcp_component["mcp_metadata"]
                if not isinstance(mcp_metadata, dict):
                    errors.append("Field 'mcp_metadata' must be a dictionary")
                else:
                    # Validate required MCP metadata fields
                    if "server_url" not in mcp_metadata:
                        errors.append("MCP metadata must have 'server_url' field")
                    elif not isinstance(mcp_metadata["server_url"], str):
                        errors.append("MCP 'server_url' must be a string")
                    
                    if "tool_name" in mcp_metadata:
                        if not isinstance(mcp_metadata["tool_name"], str):
                            warnings.append("MCP 'tool_name' should be a string")
        
        except Exception as e:
            errors.append(f"MCP validation error: {str(e)}")
        
        validation_time = time.time() - start_time
        self._update_validation_stats(len(errors) == 0, validation_time)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            validation_time=validation_time
        )
    
    def validate_batch_autogen_schemas(self, schemas: List[Dict[str, Any]]) -> List[ValidationResult]:
        """
        Validate multiple AutoGen schemas efficiently.
        
        Args:
            schemas: List of AutoGen schema dictionaries
            
        Returns:
            List of ValidationResult objects
        """
        results = []
        
        for schema in schemas:
            result = self.validate_autogen_schema(schema)
            results.append(result)
        
        return results
    
    def _validate_parameters_structure(self, parameters: Dict[str, Any]) -> List[str]:
        """
        Validate parameters structure for AutoGen schema.
        
        Args:
            parameters: Parameters dictionary
            
        Returns:
            List of validation errors
        """
        errors = []
        
        # Check type field
        if "type" not in parameters:
            errors.append("Parameters must have 'type' field")
        elif parameters["type"] != "object":
            errors.append("Parameters type must be 'object'")
        
        # Check properties field
        if "properties" not in parameters:
            errors.append("Parameters must have 'properties' field")
        elif not isinstance(parameters["properties"], dict):
            errors.append("Parameters 'properties' must be a dictionary")
        
        # Validate required field if present
        if "required" in parameters:
            required = parameters["required"]
            if not isinstance(required, list):
                errors.append("Parameters 'required' must be a list")
            elif "properties" in parameters:
                properties = parameters["properties"]
                for req_field in required:
                    if req_field not in properties:
                        errors.append(f"Required field '{req_field}' not found in properties")
        
        # Validate additionalProperties field if present
        if "additionalProperties" in parameters:
            if not isinstance(parameters["additionalProperties"], bool):
                errors.append("Parameters 'additionalProperties' must be a boolean")
        
        return errors
    
    def _update_validation_stats(self, success: bool, validation_time: float) -> None:
        """
        Update validation statistics.
        
        Args:
            success: Whether validation was successful
            validation_time: Time taken for validation
        """
        self.validation_stats["total_validations"] += 1
        
        if success:
            self.validation_stats["successful_validations"] += 1
        else:
            self.validation_stats["failed_validations"] += 1
        
        # Update average validation time
        total = self.validation_stats["total_validations"]
        current_avg = self.validation_stats["average_validation_time"]
        self.validation_stats["average_validation_time"] = (
            (current_avg * (total - 1) + validation_time) / total
        )
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """
        Get validation statistics.
        
        Returns:
            Dictionary containing validation statistics
        """
        return self.validation_stats.copy()
    
    def reset_statistics(self) -> None:
        """Reset validation statistics."""
        self.validation_stats = {
            "total_validations": 0,
            "successful_validations": 0,
            "failed_validations": 0,
            "average_validation_time": 0.0
        }
        logger.info("Validation statistics reset")


# Global validator instance
_universal_validator = None


def get_universal_validator() -> UniversalSchemaValidator:
    """
    Get or create the global universal schema validator instance.
    
    Returns:
        The universal schema validator instance
    """
    global _universal_validator
    if _universal_validator is None:
        logger.info("Creating new global UniversalSchemaValidator instance")
        _universal_validator = UniversalSchemaValidator()
    return _universal_validator
