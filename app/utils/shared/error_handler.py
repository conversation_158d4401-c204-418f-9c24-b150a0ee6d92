"""
Universal Error Handler - DRY implementation for error handling across services
"""

import time
import logging
import traceback
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ErrorCategory(Enum):
    """Error categories for classification"""
    VALIDATION = "validation"
    EXTRACTION = "extraction"
    EXECUTION = "execution"
    NETWORK = "network"
    CONFIGURATION = "configuration"
    UNKNOWN = "unknown"


@dataclass
class ErrorResult:
    """Result of error handling"""
    error_type: str
    error_message: str
    error_details: Dict[str, Any]
    recovery_suggestions: List[str]
    timestamp: float
    context: Dict[str, Any]
    
    def __post_init__(self):
        if self.error_details is None:
            self.error_details = {}
        if self.recovery_suggestions is None:
            self.recovery_suggestions = []
        if self.context is None:
            self.context = {}


class UniversalErrorHandler:
    """
    Universal error handler implementing DRY principles.
    
    This class provides consistent error handling across all services,
    eliminating code duplication and ensuring uniform error processing.
    """
    
    def __init__(self):
        """Initialize the universal error handler."""
        self.error_categories = ["validation", "extraction", "execution", "network"]
        self.error_stats = {
            "total_errors": 0,
            "validation_errors": 0,
            "extraction_errors": 0,
            "execution_errors": 0,
            "network_errors": 0,
            "configuration_errors": 0,
            "unknown_errors": 0
        }
        
        logger.info("UniversalErrorHandler initialized")
    
    def handle_validation_error(
        self, 
        error: Exception, 
        context: Dict[str, Any] = None
    ) -> ErrorResult:
        """
        Handle validation errors with consistent processing.
        
        Args:
            error: The validation error exception
            context: Additional context information
            
        Returns:
            ErrorResult with processed error information
        """
        return self._handle_error(
            error=error,
            error_category=ErrorCategory.VALIDATION,
            context=context or {}
        )
    
    def handle_extraction_error(
        self, 
        error: Exception, 
        context: Dict[str, Any] = None
    ) -> ErrorResult:
        """
        Handle extraction errors with consistent processing.
        
        Args:
            error: The extraction error exception
            context: Additional context information
            
        Returns:
            ErrorResult with processed error information
        """
        return self._handle_error(
            error=error,
            error_category=ErrorCategory.EXTRACTION,
            context=context or {}
        )
    
    def handle_execution_error(
        self, 
        error: Exception, 
        context: Dict[str, Any] = None
    ) -> ErrorResult:
        """
        Handle execution errors with consistent processing.
        
        Args:
            error: The execution error exception
            context: Additional context information
            
        Returns:
            ErrorResult with processed error information
        """
        return self._handle_error(
            error=error,
            error_category=ErrorCategory.EXECUTION,
            context=context or {}
        )
    
    def handle_network_error(
        self, 
        error: Exception, 
        context: Dict[str, Any] = None
    ) -> ErrorResult:
        """
        Handle network errors with consistent processing.
        
        Args:
            error: The network error exception
            context: Additional context information
            
        Returns:
            ErrorResult with processed error information
        """
        return self._handle_error(
            error=error,
            error_category=ErrorCategory.NETWORK,
            context=context or {}
        )
    
    def handle_configuration_error(
        self, 
        error: Exception, 
        context: Dict[str, Any] = None
    ) -> ErrorResult:
        """
        Handle configuration errors with consistent processing.
        
        Args:
            error: The configuration error exception
            context: Additional context information
            
        Returns:
            ErrorResult with processed error information
        """
        return self._handle_error(
            error=error,
            error_category=ErrorCategory.CONFIGURATION,
            context=context or {}
        )
    
    def _handle_error(
        self, 
        error: Exception, 
        error_category: ErrorCategory, 
        context: Dict[str, Any]
    ) -> ErrorResult:
        """
        Internal method to handle errors consistently.
        
        Args:
            error: The error exception
            error_category: Category of the error
            context: Additional context information
            
        Returns:
            ErrorResult with processed error information
        """
        timestamp = time.time()
        
        # Extract error information
        error_message = str(error)
        error_type = error_category.value
        
        # Get error details
        error_details = {
            "exception_type": type(error).__name__,
            "traceback": traceback.format_exc(),
            "error_args": error.args if hasattr(error, 'args') else []
        }
        
        # Generate recovery suggestions
        recovery_suggestions = self.get_recovery_suggestions(error_type, error_message)
        
        # Update error statistics
        self._update_error_stats(error_category)
        
        # Log the error
        logger.error(f"{error_type.upper()} ERROR: {error_message}", extra={
            "error_category": error_type,
            "context": context,
            "error_details": error_details
        })
        
        return ErrorResult(
            error_type=error_type,
            error_message=error_message,
            error_details=error_details,
            recovery_suggestions=recovery_suggestions,
            timestamp=timestamp,
            context=context
        )
    
    def get_recovery_suggestions(self, error_type: str, error_message: str) -> List[str]:
        """
        Generate recovery suggestions based on error type and message.
        
        Args:
            error_type: Type of error
            error_message: Error message
            
        Returns:
            List of recovery suggestions
        """
        suggestions = []
        
        if error_type == "validation":
            suggestions.extend(self._get_validation_recovery_suggestions(error_message))
        elif error_type == "extraction":
            suggestions.extend(self._get_extraction_recovery_suggestions(error_message))
        elif error_type == "execution":
            suggestions.extend(self._get_execution_recovery_suggestions(error_message))
        elif error_type == "network":
            suggestions.extend(self._get_network_recovery_suggestions(error_message))
        elif error_type == "configuration":
            suggestions.extend(self._get_configuration_recovery_suggestions(error_message))
        
        # Add general suggestions
        suggestions.extend([
            "Check the error logs for more detailed information",
            "Verify that all required dependencies are installed",
            "Ensure the system has sufficient resources (memory, disk space)"
        ])
        
        return suggestions
    
    def _get_validation_recovery_suggestions(self, error_message: str) -> List[str]:
        """Get validation-specific recovery suggestions."""
        suggestions = []
        
        if "missing" in error_message.lower():
            suggestions.append("Add the missing field to the schema or configuration")
            suggestions.append("Check the documentation for required fields")
        
        if "invalid" in error_message.lower():
            suggestions.append("Verify the format and type of the provided value")
            suggestions.append("Check examples of valid configurations")
        
        if "schema" in error_message.lower():
            suggestions.append("Validate the schema against the expected format")
            suggestions.append("Use a schema validation tool to identify issues")
        
        return suggestions
    
    def _get_extraction_recovery_suggestions(self, error_message: str) -> List[str]:
        """Get extraction-specific recovery suggestions."""
        suggestions = []
        
        if "component" in error_message.lower():
            suggestions.append("Check that the component exists and is properly configured")
            suggestions.append("Verify component registration and availability")
        
        if "not found" in error_message.lower():
            suggestions.append("Ensure the requested resource exists")
            suggestions.append("Check the resource identifier for typos")
        
        if "access" in error_message.lower():
            suggestions.append("Verify permissions and access rights")
            suggestions.append("Check authentication and authorization settings")
        
        return suggestions
    
    def _get_execution_recovery_suggestions(self, error_message: str) -> List[str]:
        """Get execution-specific recovery suggestions."""
        suggestions = []
        
        if "timeout" in error_message.lower():
            suggestions.append("Increase the timeout value for the operation")
            suggestions.append("Check for performance issues or bottlenecks")
        
        if "memory" in error_message.lower():
            suggestions.append("Increase available memory or optimize memory usage")
            suggestions.append("Check for memory leaks in the application")
        
        if "connection" in error_message.lower():
            suggestions.append("Check network connectivity and service availability")
            suggestions.append("Verify connection parameters and credentials")
        
        return suggestions
    
    def _get_network_recovery_suggestions(self, error_message: str) -> List[str]:
        """Get network-specific recovery suggestions."""
        suggestions = []
        
        suggestions.extend([
            "Check network connectivity and DNS resolution",
            "Verify firewall and security group settings",
            "Ensure the target service is running and accessible",
            "Check for proxy or load balancer configuration issues"
        ])
        
        return suggestions
    
    def _get_configuration_recovery_suggestions(self, error_message: str) -> List[str]:
        """Get configuration-specific recovery suggestions."""
        suggestions = []
        
        suggestions.extend([
            "Review configuration files for syntax errors",
            "Verify environment variables are set correctly",
            "Check configuration file permissions and accessibility",
            "Ensure configuration values match expected formats"
        ])
        
        return suggestions
    
    def _update_error_stats(self, error_category: ErrorCategory) -> None:
        """
        Update error statistics.
        
        Args:
            error_category: Category of the error
        """
        self.error_stats["total_errors"] += 1
        
        category_key = f"{error_category.value}_errors"
        if category_key in self.error_stats:
            self.error_stats[category_key] += 1
        else:
            self.error_stats["unknown_errors"] += 1
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """
        Get error statistics.
        
        Returns:
            Dictionary containing error statistics
        """
        return self.error_stats.copy()
    
    def reset_statistics(self) -> None:
        """Reset error statistics."""
        self.error_stats = {
            "total_errors": 0,
            "validation_errors": 0,
            "extraction_errors": 0,
            "execution_errors": 0,
            "network_errors": 0,
            "configuration_errors": 0,
            "unknown_errors": 0
        }
        logger.info("Error statistics reset")


# Global error handler instance
_universal_error_handler = None


def get_universal_error_handler() -> UniversalErrorHandler:
    """
    Get or create the global universal error handler instance.
    
    Returns:
        The universal error handler instance
    """
    global _universal_error_handler
    if _universal_error_handler is None:
        logger.info("Creating new global UniversalErrorHandler instance")
        _universal_error_handler = UniversalErrorHandler()
    return _universal_error_handler
