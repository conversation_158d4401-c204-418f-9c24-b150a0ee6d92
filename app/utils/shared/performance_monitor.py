"""
Universal Performance Monitor - DRY implementation for performance monitoring
"""

import time
import psutil
import logging
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from contextlib import contextmanager

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics data"""
    execution_count: int
    total_time: float
    average_time: float
    min_time: float
    max_time: float
    last_execution_time: float
    
    def __post_init__(self):
        if self.execution_count == 0:
            self.average_time = 0.0
        else:
            self.average_time = self.total_time / self.execution_count


@dataclass
class BenchmarkResult:
    """Benchmark result data"""
    iterations: int
    total_time: float
    average_time: float
    min_time: float
    max_time: float
    operations_per_second: float
    
    def __post_init__(self):
        if self.total_time > 0:
            self.operations_per_second = self.iterations / self.total_time
        else:
            self.operations_per_second = 0.0


@dataclass
class RegressionResult:
    """Performance regression analysis result"""
    has_regression: bool
    current_performance: float
    baseline_performance: float
    performance_change: float
    threshold_exceeded: bool
    
    def __post_init__(self):
        if self.baseline_performance > 0:
            self.performance_change = (
                (self.current_performance - self.baseline_performance) / self.baseline_performance
            )
        else:
            self.performance_change = 0.0


class PerformanceMonitor:
    """
    Universal performance monitor implementing DRY principles.
    
    This class provides consistent performance monitoring across all services,
    eliminating code duplication and ensuring uniform performance tracking.
    """
    
    def __init__(self):
        """Initialize the performance monitor."""
        self.metrics: Dict[str, PerformanceMetrics] = {}
        self.active_timers: Dict[str, float] = {}
        self.baselines: Dict[str, float] = {}
        
        logger.info("PerformanceMonitor initialized")
    
    @contextmanager
    def time_execution(self, operation_name: str):
        """
        Context manager for timing operation execution.
        
        Args:
            operation_name: Name of the operation being timed
        """
        start_time = time.time()
        try:
            yield
        finally:
            end_time = time.time()
            execution_time = end_time - start_time
            self._record_execution_time(operation_name, execution_time)
    
    def start_timer(self, operation_name: str) -> None:
        """
        Start a timer for an operation.
        
        Args:
            operation_name: Name of the operation
        """
        self.active_timers[operation_name] = time.time()
    
    def stop_timer(self, operation_name: str) -> float:
        """
        Stop a timer and record the execution time.
        
        Args:
            operation_name: Name of the operation
            
        Returns:
            Execution time in seconds
        """
        if operation_name not in self.active_timers:
            logger.warning(f"Timer for operation '{operation_name}' was not started")
            return 0.0
        
        start_time = self.active_timers.pop(operation_name)
        execution_time = time.time() - start_time
        self._record_execution_time(operation_name, execution_time)
        
        return execution_time
    
    def _record_execution_time(self, operation_name: str, execution_time: float) -> None:
        """
        Record execution time for an operation.
        
        Args:
            operation_name: Name of the operation
            execution_time: Execution time in seconds
        """
        if operation_name not in self.metrics:
            self.metrics[operation_name] = PerformanceMetrics(
                execution_count=0,
                total_time=0.0,
                average_time=0.0,
                min_time=float('inf'),
                max_time=0.0,
                last_execution_time=0.0
            )
        
        metrics = self.metrics[operation_name]
        metrics.execution_count += 1
        metrics.total_time += execution_time
        metrics.average_time = metrics.total_time / metrics.execution_count
        metrics.min_time = min(metrics.min_time, execution_time)
        metrics.max_time = max(metrics.max_time, execution_time)
        metrics.last_execution_time = execution_time
    
    def get_metrics(self, operation_name: str) -> Optional[PerformanceMetrics]:
        """
        Get performance metrics for an operation.
        
        Args:
            operation_name: Name of the operation
            
        Returns:
            PerformanceMetrics object or None if not found
        """
        return self.metrics.get(operation_name)
    
    def get_all_metrics(self) -> Dict[str, PerformanceMetrics]:
        """
        Get all performance metrics.
        
        Returns:
            Dictionary of all performance metrics
        """
        return self.metrics.copy()
    
    def get_current_memory_usage(self) -> float:
        """
        Get current memory usage in MB.
        
        Returns:
            Current memory usage in megabytes
        """
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            return memory_info.rss / 1024 / 1024  # Convert to MB
        except Exception as e:
            logger.error(f"Error getting memory usage: {str(e)}")
            return 0.0
    
    def get_cpu_usage(self) -> float:
        """
        Get current CPU usage percentage.
        
        Returns:
            Current CPU usage percentage
        """
        try:
            return psutil.cpu_percent(interval=0.1)
        except Exception as e:
            logger.error(f"Error getting CPU usage: {str(e)}")
            return 0.0
    
    def benchmark_function(
        self, 
        func: Callable, 
        iterations: int = 100,
        *args, 
        **kwargs
    ) -> BenchmarkResult:
        """
        Benchmark a function by running it multiple times.
        
        Args:
            func: Function to benchmark
            iterations: Number of iterations to run
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            BenchmarkResult with timing statistics
        """
        execution_times = []
        
        for _ in range(iterations):
            start_time = time.time()
            try:
                func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error during benchmark iteration: {str(e)}")
                continue
            end_time = time.time()
            execution_times.append(end_time - start_time)
        
        if not execution_times:
            return BenchmarkResult(
                iterations=0,
                total_time=0.0,
                average_time=0.0,
                min_time=0.0,
                max_time=0.0,
                operations_per_second=0.0
            )
        
        total_time = sum(execution_times)
        average_time = total_time / len(execution_times)
        min_time = min(execution_times)
        max_time = max(execution_times)
        
        return BenchmarkResult(
            iterations=len(execution_times),
            total_time=total_time,
            average_time=average_time,
            min_time=min_time,
            max_time=max_time,
            operations_per_second=len(execution_times) / total_time if total_time > 0 else 0.0
        )
    
    def set_performance_baseline(self, operation_name: str, baseline_time: float) -> None:
        """
        Set a performance baseline for an operation.
        
        Args:
            operation_name: Name of the operation
            baseline_time: Baseline execution time in seconds
        """
        self.baselines[operation_name] = baseline_time
        logger.info(f"Set performance baseline for '{operation_name}': {baseline_time:.4f}s")
    
    def check_performance_regression(
        self, 
        operation_name: str, 
        threshold: float = 0.2
    ) -> RegressionResult:
        """
        Check for performance regression against baseline.
        
        Args:
            operation_name: Name of the operation
            threshold: Regression threshold (e.g., 0.2 for 20% degradation)
            
        Returns:
            RegressionResult with analysis
        """
        if operation_name not in self.baselines:
            logger.warning(f"No baseline set for operation '{operation_name}'")
            return RegressionResult(
                has_regression=False,
                current_performance=0.0,
                baseline_performance=0.0,
                performance_change=0.0,
                threshold_exceeded=False
            )
        
        if operation_name not in self.metrics:
            logger.warning(f"No metrics available for operation '{operation_name}'")
            return RegressionResult(
                has_regression=False,
                current_performance=0.0,
                baseline_performance=self.baselines[operation_name],
                performance_change=0.0,
                threshold_exceeded=False
            )
        
        baseline = self.baselines[operation_name]
        current = self.metrics[operation_name].average_time
        
        performance_change = (current - baseline) / baseline if baseline > 0 else 0.0
        threshold_exceeded = performance_change > threshold
        
        return RegressionResult(
            has_regression=threshold_exceeded,
            current_performance=current,
            baseline_performance=baseline,
            performance_change=performance_change,
            threshold_exceeded=threshold_exceeded
        )
    
    def reset_metrics(self, operation_name: Optional[str] = None) -> None:
        """
        Reset performance metrics.
        
        Args:
            operation_name: Specific operation to reset, or None to reset all
        """
        if operation_name:
            if operation_name in self.metrics:
                del self.metrics[operation_name]
                logger.info(f"Reset metrics for operation '{operation_name}'")
        else:
            self.metrics.clear()
            logger.info("Reset all performance metrics")
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        Get system information for performance context.
        
        Returns:
            Dictionary with system information
        """
        try:
            return {
                "cpu_count": psutil.cpu_count(),
                "memory_total_mb": psutil.virtual_memory().total / 1024 / 1024,
                "memory_available_mb": psutil.virtual_memory().available / 1024 / 1024,
                "cpu_usage_percent": psutil.cpu_percent(interval=0.1),
                "memory_usage_percent": psutil.virtual_memory().percent
            }
        except Exception as e:
            logger.error(f"Error getting system info: {str(e)}")
            return {}


# Global performance monitor instance
_performance_monitor = None


def get_performance_monitor() -> PerformanceMonitor:
    """
    Get or create the global performance monitor instance.
    
    Returns:
        The performance monitor instance
    """
    global _performance_monitor
    if _performance_monitor is None:
        logger.info("Creating new global PerformanceMonitor instance")
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor
