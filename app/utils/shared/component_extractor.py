"""
Universal Component Extractor - DRY implementation for component data extraction
"""

import time
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ComponentInfo:
    """Extracted component information"""
    component_id: str
    component_type: str
    component_name: str
    inputs: List[Dict[str, Any]]
    outputs: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    extraction_time: float
    
    def __post_init__(self):
        if self.inputs is None:
            self.inputs = []
        if self.outputs is None:
            self.outputs = []
        if self.metadata is None:
            self.metadata = {}


@dataclass
class MCPComponentInfo(ComponentInfo):
    """Extended component info for MCP components"""
    mcp_metadata: Dict[str, Any]
    server_url: str
    tool_name: Optional[str] = None
    
    def __post_init__(self):
        super().__post_init__()
        if self.mcp_metadata is None:
            self.mcp_metadata = {}
        
        # Extract server_url from mcp_metadata if not provided
        if not self.server_url and "server_url" in self.mcp_metadata:
            self.server_url = self.mcp_metadata["server_url"]
        
        # Extract tool_name from mcp_metadata if not provided
        if not self.tool_name and "tool_name" in self.mcp_metadata:
            self.tool_name = self.mcp_metadata["tool_name"]


class UniversalComponentExtractor:
    """
    Universal component extractor implementing DRY principles.
    
    This class provides consistent component data extraction across all services,
    eliminating code duplication and ensuring uniform extraction logic.
    """
    
    def __init__(self):
        """Initialize the universal component extractor."""
        self.supported_component_types = ["workflow_component", "mcp_marketplace"]
        self.extraction_stats = {
            "total_extractions": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "average_extraction_time": 0.0
        }
        
        logger.info("UniversalComponentExtractor initialized")
    
    def extract_component_info(self, component_data: Dict[str, Any]) -> ComponentInfo:
        """
        Extract component information from component data.
        
        Args:
            component_data: Component data dictionary
            
        Returns:
            ComponentInfo object with extracted data
        """
        start_time = time.time()
        
        try:
            # Extract basic component information
            component_id = component_data.get("component_id", "")
            component_type = component_data.get("component_type", "")
            component_name = component_data.get("component_name", component_type)
            
            # Extract inputs
            inputs = self._extract_inputs(component_data)
            
            # Extract outputs
            outputs = self._extract_outputs(component_data)
            
            # Extract metadata
            metadata = self._extract_metadata(component_data)
            
            extraction_time = time.time() - start_time
            self._update_extraction_stats(True, extraction_time)
            
            return ComponentInfo(
                component_id=component_id,
                component_type=component_type,
                component_name=component_name,
                inputs=inputs,
                outputs=outputs,
                metadata=metadata,
                extraction_time=extraction_time
            )
            
        except Exception as e:
            extraction_time = time.time() - start_time
            self._update_extraction_stats(False, extraction_time)
            logger.error(f"Error extracting component info: {str(e)}")
            
            # Return minimal component info on error
            return ComponentInfo(
                component_id=component_data.get("component_id", "unknown"),
                component_type=component_data.get("component_type", "unknown"),
                component_name=component_data.get("component_name", "Unknown Component"),
                inputs=[],
                outputs=[],
                metadata={"extraction_error": str(e)},
                extraction_time=extraction_time
            )
    
    def extract_mcp_info(self, mcp_data: Dict[str, Any]) -> MCPComponentInfo:
        """
        Extract MCP component information from component data.
        
        Args:
            mcp_data: MCP component data dictionary
            
        Returns:
            MCPComponentInfo object with extracted data
        """
        start_time = time.time()
        
        try:
            # Extract basic component info first
            base_info = self.extract_component_info(mcp_data)
            
            # Extract MCP-specific information
            mcp_metadata = mcp_data.get("mcp_metadata", {})
            server_url = mcp_metadata.get("server_url", "")
            tool_name = mcp_metadata.get("tool_name")
            
            extraction_time = time.time() - start_time
            
            return MCPComponentInfo(
                component_id=base_info.component_id,
                component_type=base_info.component_type,
                component_name=base_info.component_name,
                inputs=base_info.inputs,
                outputs=base_info.outputs,
                metadata=base_info.metadata,
                extraction_time=extraction_time,
                mcp_metadata=mcp_metadata,
                server_url=server_url,
                tool_name=tool_name
            )
            
        except Exception as e:
            extraction_time = time.time() - start_time
            self._update_extraction_stats(False, extraction_time)
            logger.error(f"Error extracting MCP component info: {str(e)}")
            
            # Return minimal MCP component info on error
            return MCPComponentInfo(
                component_id=mcp_data.get("component_id", "unknown"),
                component_type="MCPMarketplace",
                component_name=mcp_data.get("component_name", "Unknown MCP Component"),
                inputs=[],
                outputs=[],
                metadata={"extraction_error": str(e)},
                extraction_time=extraction_time,
                mcp_metadata={},
                server_url=""
            )
    
    def extract_batch_components(self, components: List[Dict[str, Any]]) -> List[ComponentInfo]:
        """
        Extract information from multiple components efficiently.
        
        Args:
            components: List of component data dictionaries
            
        Returns:
            List of ComponentInfo objects
        """
        results = []
        
        for component in components:
            try:
                # Determine if it's an MCP component
                if component.get("component_type") == "MCPMarketplace":
                    info = self.extract_mcp_info(component)
                else:
                    info = self.extract_component_info(component)
                
                results.append(info)
                
            except Exception as e:
                logger.error(f"Error in batch extraction: {str(e)}")
                # Continue with other components
                continue
        
        return results
    
    def _extract_inputs(self, component_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract input definitions from component data.
        
        Args:
            component_data: Component data dictionary
            
        Returns:
            List of input definitions
        """
        inputs = []
        
        # Check for inputs in various formats
        if "inputs" in component_data:
            raw_inputs = component_data["inputs"]
            if isinstance(raw_inputs, list):
                inputs = raw_inputs
            elif isinstance(raw_inputs, dict):
                # Convert dict format to list format
                for name, config in raw_inputs.items():
                    input_def = {"name": name}
                    if isinstance(config, dict):
                        input_def.update(config)
                    else:
                        input_def["type"] = str(type(config).__name__)
                        input_def["value"] = config
                    inputs.append(input_def)
        
        # Check for component_inputs (alternative format)
        elif "component_inputs" in component_data:
            inputs = component_data["component_inputs"]
        
        # Ensure all inputs have required fields
        for input_def in inputs:
            if "name" not in input_def:
                input_def["name"] = "unnamed_input"
            if "type" not in input_def:
                input_def["type"] = "string"
        
        return inputs
    
    def _extract_outputs(self, component_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract output definitions from component data.
        
        Args:
            component_data: Component data dictionary
            
        Returns:
            List of output definitions
        """
        outputs = []
        
        # Check for outputs in various formats
        if "outputs" in component_data:
            raw_outputs = component_data["outputs"]
            if isinstance(raw_outputs, list):
                outputs = raw_outputs
            elif isinstance(raw_outputs, dict):
                # Convert dict format to list format
                for name, config in raw_outputs.items():
                    output_def = {"name": name}
                    if isinstance(config, dict):
                        output_def.update(config)
                    else:
                        output_def["type"] = str(type(config).__name__)
                    outputs.append(output_def)
        
        # Check for component_outputs (alternative format)
        elif "component_outputs" in component_data:
            outputs = component_data["component_outputs"]
        
        # Ensure all outputs have required fields
        for output_def in outputs:
            if "name" not in output_def:
                output_def["name"] = "unnamed_output"
            if "type" not in output_def:
                output_def["type"] = "string"
        
        return outputs
    
    def _extract_metadata(self, component_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract metadata from component data.
        
        Args:
            component_data: Component data dictionary
            
        Returns:
            Metadata dictionary
        """
        metadata = {}
        
        # Extract common metadata fields
        metadata_fields = [
            "description", "version", "author", "category",
            "tags", "documentation", "examples", "requirements"
        ]
        
        for field in metadata_fields:
            if field in component_data:
                metadata[field] = component_data[field]
        
        # Extract any additional metadata
        if "metadata" in component_data:
            additional_metadata = component_data["metadata"]
            if isinstance(additional_metadata, dict):
                metadata.update(additional_metadata)
        
        return metadata
    
    def _update_extraction_stats(self, success: bool, extraction_time: float) -> None:
        """
        Update extraction statistics.
        
        Args:
            success: Whether extraction was successful
            extraction_time: Time taken for extraction
        """
        self.extraction_stats["total_extractions"] += 1
        
        if success:
            self.extraction_stats["successful_extractions"] += 1
        else:
            self.extraction_stats["failed_extractions"] += 1
        
        # Update average extraction time
        total = self.extraction_stats["total_extractions"]
        current_avg = self.extraction_stats["average_extraction_time"]
        self.extraction_stats["average_extraction_time"] = (
            (current_avg * (total - 1) + extraction_time) / total
        )
    
    def get_extraction_statistics(self) -> Dict[str, Any]:
        """
        Get extraction statistics.
        
        Returns:
            Dictionary containing extraction statistics
        """
        return self.extraction_stats.copy()
    
    def reset_statistics(self) -> None:
        """Reset extraction statistics."""
        self.extraction_stats = {
            "total_extractions": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "average_extraction_time": 0.0
        }
        logger.info("Extraction statistics reset")


# Global extractor instance
_universal_extractor = None


def get_universal_extractor() -> UniversalComponentExtractor:
    """
    Get or create the global universal component extractor instance.
    
    Returns:
        The universal component extractor instance
    """
    global _universal_extractor
    if _universal_extractor is None:
        logger.info("Creating new global UniversalComponentExtractor instance")
        _universal_extractor = UniversalComponentExtractor()
    return _universal_extractor
