{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Workflow Capture Schema", "type": "object", "properties": {"connections": {"type": "array", "description": "Array of connections (edges) between transitions in the workflow.", "items": {"type": "object", "properties": {"from_transition_id": {"type": "string", "description": "The unique ID of the originating transition."}, "to_transition_id": {"type": "string", "description": "The unique ID of the target transition."}, "conditional_routing": {"type": "string", "description": "Condition for the routing of this connection. Use 'null' if none."}}, "required": ["from_transition_id", "to_transition_id", "conditional_routing"]}}, "transitions": {"type": "array", "description": "Array of transitions in the workflow.", "items": {"type": "object", "properties": {"transition_id": {"type": "string", "description": "Unique identifier for the transition."}, "node": {"type": "object", "description": "The node associated with this transition.", "properties": {"node_id": {"type": "string", "description": "Unique identifier for the node."}, "tools_to_use": {"type": "array", "description": "List of tools that will be used by this node.", "items": {"type": "object", "properties": {"tool_id": {"type": "number", "description": "Unique identifier for the tool."}, "tool_name": {"type": "string", "description": "Name of the tool."}, "tool_params": {"type": "object", "description": "Parameters to invoke the tool with", "properties": {"items": {"type": "array", "description": "List of tool parameters", "items": {"type": "object", "properties": {"field_name": {"type": "string", "description": "The name of the input parameter."}, "user_dependent": {"type": "boolean", "default": false, "description": "Indicates whether this field is required to be filled by the user."}}, "required": ["field_name", "user_dependent"]}}}, "required": ["items"]}}, "required": ["tool_id", "tool_name", "tool_params"]}}}, "required": ["node_id", "tools_to_use"]}}, "required": ["transition_id", "node"]}}}, "required": ["connections", "transitions"]}