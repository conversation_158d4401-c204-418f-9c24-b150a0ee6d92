{"nodes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "task": {"type": "string"}, "node_details": {"type": "object", "properties": {"type": {"type": "string"}, "server_script_path": {"type": "string"}, "server_tools": {"type": "array", "items": {"type": "object", "properties": {"tool_id": {"type": "number"}, "tool_name": {"type": "string"}, "endpoint": {"type": "string"}, "input_schema": {"type": "object", "description": "Unified schema for server inputs", "properties": {"predefined_fields": {"type": "array", "description": "Explicitly defined input fields required by the server.", "items": {"type": "object", "properties": {"field_name": {"type": "string", "description": "The name of the input parameter."}, "data_type": {"type": "object", "properties": {"type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"], "description": "The base type of the field."}, "description": {"type": "string", "description": "Optional description of the field."}, "format": {"type": "string", "description": "Optional format for string types (e.g., 'date', 'email')."}, "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"], "description": "The type of items in the array."}, "properties": {"type": "object", "description": "Schema definition for object items.", "additionalProperties": true}, "required": {"type": "array", "items": {"type": "string"}, "description": "Required fields inside the object items."}}, "required": ["type"]}}, "required": ["type"], "description": "The data type of the input parameter, stored as a rich metadata object."}, "required": {"type": "boolean", "default": false, "description": "Indicates whether this field is mandatory."}}, "required": ["field_name", "data_type", "required"]}}}, "required": ["predefined_fields"]}, "output_schema": {"type": "object", "description": "Unified schema for server outputs", "properties": {"predefined_fields": {"type": "array", "description": "Explicitly defined output fields generated by the server.", "items": {"type": "object", "properties": {"field_name": {"type": "string", "description": "The name of the output field."}, "data_type": {"type": "object", "properties": {"type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"], "description": "The base type of the field."}, "description": {"type": "string", "description": "Optional description of the field."}, "format": {"type": "string", "description": "Optional format for string types (e.g., 'date', 'email')."}, "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"], "description": "The type of items in the array."}, "properties": {"type": "object", "description": "Schema definition for object items.", "additionalProperties": true}, "required": {"type": "array", "items": {"type": "string"}, "description": "Required fields inside the object items."}}, "required": ["type"]}}, "required": ["type"], "description": "The data type of the input parameter, stored as a rich metadata object."}}, "required": ["field_name", "data_type"]}}}, "required": ["predefined_fields"]}}, "required": ["tool_name", "endpoint", "input_schema", "output_schema"]}}}, "required": ["type", "server_tools", "server_script_path"]}}, "required": ["id", "task", "node_details"]}}}