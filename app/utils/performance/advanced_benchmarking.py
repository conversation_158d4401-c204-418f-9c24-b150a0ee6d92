"""
Advanced Benchmarking - Comprehensive performance benchmarking utilities
"""

import time
import asyncio
import statistics
import logging
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass, field
import psutil
import json
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class BenchmarkMetrics:
    """Comprehensive benchmark metrics"""
    operation: str
    iterations: int
    total_time: float
    average_time: float
    median_time: float
    min_time: float
    max_time: float
    std_deviation: float
    percentile_95: float
    percentile_99: float
    throughput: float  # operations per second
    memory_peak: float
    memory_average: float
    cpu_average: float
    success_rate: float
    error_count: int
    timestamp: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        if self.total_time > 0:
            self.throughput = self.iterations / self.total_time
        else:
            self.throughput = 0.0


@dataclass
class PerformanceTarget:
    """Performance target definition"""
    operation: str
    max_average_time: float
    max_memory_usage: float
    min_throughput: float
    max_error_rate: float
    
    def is_met(self, metrics: BenchmarkMetrics) -> bool:
        """Check if performance target is met."""
        return (
            metrics.average_time <= self.max_average_time and
            metrics.memory_average <= self.max_memory_usage and
            metrics.throughput >= self.min_throughput and
            (1 - metrics.success_rate) <= self.max_error_rate
        )


@dataclass
class LoadTestResult:
    """Load testing result"""
    operation: str
    concurrent_users: int
    duration: float
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    requests_per_second: float
    error_rate: float
    memory_usage: float
    cpu_usage: float


class AdvancedBenchmarking:
    """
    Advanced benchmarking utilities for comprehensive performance testing.
    
    Provides detailed performance metrics, load testing, stress testing,
    and performance regression detection capabilities.
    """
    
    def __init__(self):
        """Initialize the advanced benchmarking system."""
        self.benchmark_history = {}
        self.performance_targets = {}
        self.baseline_metrics = {}
        
        # Default performance targets
        self._set_default_targets()
        
        logger.info("AdvancedBenchmarking initialized")
    
    def _set_default_targets(self):
        """Set default performance targets."""
        self.performance_targets = {
            "ui_interaction": PerformanceTarget(
                operation="ui_interaction",
                max_average_time=0.1,  # 100ms
                max_memory_usage=10.0,  # 10MB
                min_throughput=10.0,    # 10 ops/sec
                max_error_rate=0.01     # 1%
            ),
            "schema_generation": PerformanceTarget(
                operation="schema_generation",
                max_average_time=0.05,  # 50ms
                max_memory_usage=5.0,   # 5MB
                min_throughput=20.0,    # 20 ops/sec
                max_error_rate=0.005    # 0.5%
            ),
            "tool_extraction": PerformanceTarget(
                operation="tool_extraction",
                max_average_time=0.1,   # 100ms for 10 tools
                max_memory_usage=15.0,  # 15MB
                min_throughput=10.0,    # 10 ops/sec
                max_error_rate=0.01     # 1%
            ),
            "workflow_processing": PerformanceTarget(
                operation="workflow_processing",
                max_average_time=0.2,   # 200ms
                max_memory_usage=25.0,  # 25MB
                min_throughput=5.0,     # 5 ops/sec
                max_error_rate=0.02     # 2%
            )
        }
    
    async def comprehensive_benchmark(
        self,
        operation_name: str,
        operation_func: Callable,
        iterations: int = 100,
        warmup_iterations: int = 10,
        *args,
        **kwargs
    ) -> BenchmarkMetrics:
        """
        Perform comprehensive benchmarking of an operation.
        
        Args:
            operation_name: Name of the operation
            operation_func: Function to benchmark
            iterations: Number of iterations to run
            warmup_iterations: Number of warmup iterations
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            BenchmarkMetrics with comprehensive performance data
        """
        logger.info(f"Starting comprehensive benchmark for {operation_name}")
        
        # Warmup phase
        await self._warmup_phase(operation_func, warmup_iterations, *args, **kwargs)
        
        # Benchmark phase
        execution_times = []
        memory_readings = []
        cpu_readings = []
        error_count = 0
        
        process = psutil.Process()
        
        start_time = time.time()
        
        for i in range(iterations):
            # Memory and CPU before operation
            memory_before = process.memory_info().rss / 1024 / 1024  # MB
            cpu_before = process.cpu_percent()
            
            # Execute operation
            operation_start = time.time()
            
            try:
                if asyncio.iscoroutinefunction(operation_func):
                    await operation_func(*args, **kwargs)
                else:
                    operation_func(*args, **kwargs)
            except Exception as e:
                error_count += 1
                logger.warning(f"Error in iteration {i}: {str(e)}")
            
            operation_end = time.time()
            
            # Memory and CPU after operation
            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            cpu_after = process.cpu_percent()
            
            # Record metrics
            execution_times.append(operation_end - operation_start)
            memory_readings.append(memory_after - memory_before)
            cpu_readings.append(cpu_after - cpu_before)
        
        end_time = time.time()
        
        # Calculate comprehensive metrics
        total_time = end_time - start_time
        successful_iterations = iterations - error_count
        
        if execution_times:
            metrics = BenchmarkMetrics(
                operation=operation_name,
                iterations=iterations,
                total_time=total_time,
                average_time=statistics.mean(execution_times),
                median_time=statistics.median(execution_times),
                min_time=min(execution_times),
                max_time=max(execution_times),
                std_deviation=statistics.stdev(execution_times) if len(execution_times) > 1 else 0.0,
                percentile_95=self._percentile(execution_times, 95),
                percentile_99=self._percentile(execution_times, 99),
                throughput=0.0,  # Will be calculated in __post_init__
                memory_peak=max(memory_readings) if memory_readings else 0.0,
                memory_average=statistics.mean(memory_readings) if memory_readings else 0.0,
                cpu_average=statistics.mean(cpu_readings) if cpu_readings else 0.0,
                success_rate=successful_iterations / iterations,
                error_count=error_count
            )
        else:
            # All operations failed
            metrics = BenchmarkMetrics(
                operation=operation_name,
                iterations=iterations,
                total_time=total_time,
                average_time=0.0,
                median_time=0.0,
                min_time=0.0,
                max_time=0.0,
                std_deviation=0.0,
                percentile_95=0.0,
                percentile_99=0.0,
                throughput=0.0,
                memory_peak=0.0,
                memory_average=0.0,
                cpu_average=0.0,
                success_rate=0.0,
                error_count=error_count
            )
        
        # Store in history
        if operation_name not in self.benchmark_history:
            self.benchmark_history[operation_name] = []
        self.benchmark_history[operation_name].append(metrics)
        
        logger.info(f"Benchmark completed for {operation_name}: {metrics.average_time:.3f}s avg")
        return metrics
    
    async def _warmup_phase(
        self,
        operation_func: Callable,
        warmup_iterations: int,
        *args,
        **kwargs
    ):
        """Perform warmup phase to stabilize performance."""
        logger.debug(f"Warming up with {warmup_iterations} iterations")
        
        for _ in range(warmup_iterations):
            try:
                if asyncio.iscoroutinefunction(operation_func):
                    await operation_func(*args, **kwargs)
                else:
                    operation_func(*args, **kwargs)
            except Exception:
                pass  # Ignore warmup errors
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile value."""
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))
    
    async def load_test(
        self,
        operation_name: str,
        operation_func: Callable,
        concurrent_users: int = 10,
        duration: float = 60.0,
        *args,
        **kwargs
    ) -> LoadTestResult:
        """
        Perform load testing with concurrent users.
        
        Args:
            operation_name: Name of the operation
            operation_func: Function to test
            concurrent_users: Number of concurrent users
            duration: Test duration in seconds
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            LoadTestResult with load testing metrics
        """
        logger.info(f"Starting load test for {operation_name} with {concurrent_users} users")
        
        start_time = time.time()
        end_time = start_time + duration
        
        # Shared counters
        total_requests = 0
        successful_requests = 0
        failed_requests = 0
        response_times = []
        
        # Memory and CPU monitoring
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        async def user_simulation():
            """Simulate a single user's operations."""
            nonlocal total_requests, successful_requests, failed_requests, response_times
            
            while time.time() < end_time:
                request_start = time.time()
                
                try:
                    if asyncio.iscoroutinefunction(operation_func):
                        await operation_func(*args, **kwargs)
                    else:
                        operation_func(*args, **kwargs)
                    
                    successful_requests += 1
                except Exception:
                    failed_requests += 1
                
                request_end = time.time()
                response_times.append(request_end - request_start)
                total_requests += 1
                
                # Small delay to prevent overwhelming
                await asyncio.sleep(0.01)
        
        # Run concurrent user simulations
        tasks = [user_simulation() for _ in range(concurrent_users)]
        await asyncio.gather(*tasks)
        
        # Calculate final metrics
        actual_duration = time.time() - start_time
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_usage = final_memory - initial_memory
        
        average_response_time = statistics.mean(response_times) if response_times else 0.0
        requests_per_second = total_requests / actual_duration if actual_duration > 0 else 0.0
        error_rate = failed_requests / total_requests if total_requests > 0 else 0.0
        
        result = LoadTestResult(
            operation=operation_name,
            concurrent_users=concurrent_users,
            duration=actual_duration,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            average_response_time=average_response_time,
            requests_per_second=requests_per_second,
            error_rate=error_rate,
            memory_usage=memory_usage,
            cpu_usage=process.cpu_percent()
        )
        
        logger.info(f"Load test completed: {requests_per_second:.1f} req/sec, {error_rate:.2%} error rate")
        return result
    
    def validate_performance_targets(self, metrics: BenchmarkMetrics) -> Dict[str, Any]:
        """
        Validate metrics against performance targets.
        
        Args:
            metrics: Benchmark metrics to validate
            
        Returns:
            Dictionary with validation results
        """
        target = self.performance_targets.get(metrics.operation)
        
        if not target:
            return {
                "has_target": False,
                "message": f"No performance target defined for {metrics.operation}"
            }
        
        is_met = target.is_met(metrics)
        
        validation_details = {
            "average_time": {
                "target": target.max_average_time,
                "actual": metrics.average_time,
                "passed": metrics.average_time <= target.max_average_time
            },
            "memory_usage": {
                "target": target.max_memory_usage,
                "actual": metrics.memory_average,
                "passed": metrics.memory_average <= target.max_memory_usage
            },
            "throughput": {
                "target": target.min_throughput,
                "actual": metrics.throughput,
                "passed": metrics.throughput >= target.min_throughput
            },
            "error_rate": {
                "target": target.max_error_rate,
                "actual": 1 - metrics.success_rate,
                "passed": (1 - metrics.success_rate) <= target.max_error_rate
            }
        }
        
        return {
            "has_target": True,
            "target_met": is_met,
            "details": validation_details,
            "summary": f"Performance target {'MET' if is_met else 'NOT MET'} for {metrics.operation}"
        }
    
    def detect_performance_regression(
        self,
        operation_name: str,
        current_metrics: BenchmarkMetrics,
        regression_threshold: float = 0.1  # 10% degradation
    ) -> Dict[str, Any]:
        """
        Detect performance regression compared to baseline.
        
        Args:
            operation_name: Name of the operation
            current_metrics: Current benchmark metrics
            regression_threshold: Threshold for regression detection (0.1 = 10%)
            
        Returns:
            Dictionary with regression analysis
        """
        baseline = self.baseline_metrics.get(operation_name)
        
        if not baseline:
            # Set current as baseline if none exists
            self.baseline_metrics[operation_name] = current_metrics
            return {
                "has_baseline": False,
                "message": f"No baseline found for {operation_name}, setting current as baseline"
            }
        
        # Calculate performance changes
        time_change = (current_metrics.average_time - baseline.average_time) / baseline.average_time
        memory_change = (current_metrics.memory_average - baseline.memory_average) / baseline.memory_average if baseline.memory_average > 0 else 0
        throughput_change = (current_metrics.throughput - baseline.throughput) / baseline.throughput if baseline.throughput > 0 else 0
        
        # Detect regressions
        time_regression = time_change > regression_threshold
        memory_regression = memory_change > regression_threshold
        throughput_regression = throughput_change < -regression_threshold
        
        has_regression = time_regression or memory_regression or throughput_regression
        
        return {
            "has_baseline": True,
            "has_regression": has_regression,
            "time_change": time_change,
            "memory_change": memory_change,
            "throughput_change": throughput_change,
            "regressions": {
                "execution_time": time_regression,
                "memory_usage": memory_regression,
                "throughput": throughput_regression
            },
            "baseline_date": baseline.timestamp,
            "current_date": current_metrics.timestamp
        }
    
    def generate_performance_report(self, operation_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate comprehensive performance report.
        
        Args:
            operation_name: Specific operation to report on, or None for all
            
        Returns:
            Dictionary containing performance report
        """
        if operation_name:
            operations = [operation_name] if operation_name in self.benchmark_history else []
        else:
            operations = list(self.benchmark_history.keys())
        
        report = {
            "generated_at": datetime.now().isoformat(),
            "operations": {},
            "summary": {
                "total_operations": len(operations),
                "total_benchmarks": sum(len(self.benchmark_history[op]) for op in operations),
                "performance_targets_met": 0,
                "performance_targets_total": 0,
                "regressions_detected": 0
            }
        }
        
        for op in operations:
            history = self.benchmark_history[op]
            latest_metrics = history[-1] if history else None
            
            if latest_metrics:
                # Validate against targets
                target_validation = self.validate_performance_targets(latest_metrics)
                
                # Check for regression
                regression_analysis = self.detect_performance_regression(op, latest_metrics)
                
                # Update summary
                if target_validation.get("has_target"):
                    report["summary"]["performance_targets_total"] += 1
                    if target_validation.get("target_met"):
                        report["summary"]["performance_targets_met"] += 1
                
                if regression_analysis.get("has_regression"):
                    report["summary"]["regressions_detected"] += 1
                
                # Operation details
                report["operations"][op] = {
                    "latest_metrics": {
                        "average_time": latest_metrics.average_time,
                        "throughput": latest_metrics.throughput,
                        "memory_usage": latest_metrics.memory_average,
                        "success_rate": latest_metrics.success_rate,
                        "timestamp": latest_metrics.timestamp.isoformat()
                    },
                    "target_validation": target_validation,
                    "regression_analysis": regression_analysis,
                    "benchmark_count": len(history),
                    "trend": self._calculate_trend(history)
                }
        
        return report
    
    def _calculate_trend(self, history: List[BenchmarkMetrics]) -> Dict[str, str]:
        """Calculate performance trend from history."""
        if len(history) < 2:
            return {"trend": "insufficient_data"}
        
        recent = history[-3:] if len(history) >= 3 else history
        
        # Calculate trend for average time
        times = [m.average_time for m in recent]
        if len(times) >= 2:
            if times[-1] < times[0] * 0.95:  # 5% improvement
                time_trend = "improving"
            elif times[-1] > times[0] * 1.05:  # 5% degradation
                time_trend = "degrading"
            else:
                time_trend = "stable"
        else:
            time_trend = "stable"
        
        return {"trend": time_trend}
    
    def export_benchmark_data(self, filename: str) -> None:
        """Export benchmark data to JSON file."""
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "benchmark_history": {
                op: [
                    {
                        "operation": m.operation,
                        "iterations": m.iterations,
                        "average_time": m.average_time,
                        "throughput": m.throughput,
                        "memory_average": m.memory_average,
                        "success_rate": m.success_rate,
                        "timestamp": m.timestamp.isoformat()
                    }
                    for m in metrics
                ]
                for op, metrics in self.benchmark_history.items()
            },
            "performance_targets": {
                op: {
                    "max_average_time": target.max_average_time,
                    "max_memory_usage": target.max_memory_usage,
                    "min_throughput": target.min_throughput,
                    "max_error_rate": target.max_error_rate
                }
                for op, target in self.performance_targets.items()
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2)
        
        logger.info(f"Benchmark data exported to {filename}")


# Global benchmarking instance
_advanced_benchmarking = None


def get_advanced_benchmarking() -> AdvancedBenchmarking:
    """
    Get or create the global advanced benchmarking instance.
    
    Returns:
        The advanced benchmarking instance
    """
    global _advanced_benchmarking
    if _advanced_benchmarking is None:
        logger.info("Creating new global AdvancedBenchmarking instance")
        _advanced_benchmarking = AdvancedBenchmarking()
    return _advanced_benchmarking
