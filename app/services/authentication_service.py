"""
Authentication Service gRPC Client

This module provides a gRPC client for communicating with the authentication service
for OAuth operations and credential management.
"""

import json
import logging
from typing import Any, Dict, List, Optional

import grpc

from app.core.config import settings
from app.core.oauth_providers import OAuthProvider
from app.grpc_ import authentication_pb2, authentication_pb2_grpc

logger = logging.getLogger(__name__)


class AuthenticationServiceClient:
    """gRPC client for authentication service."""

    def __init__(self):
        """Initialize the authentication service client."""
        self.channel = None
        self.stub = None
        self._initialized = False

    def _ensure_connection(self):
        """Ensure gRPC connection is initialized."""
        if not self._initialized:
            self._initialize_connection()
            self._initialized = True

    def _initialize_connection(self):
        """Initialize gRPC connection to authentication service."""
        try:
            # Get authentication service configuration
            auth_service_host = getattr(settings, "AUTH_SERVICE_HOST", "localhost")
            auth_service_port = getattr(settings, "AUTH_SERVICE_PORT", 50054)

            # Create gRPC channel
            self.channel = grpc.insecure_channel(
                f"{auth_service_host}:{auth_service_port}",
                options=[
                    ("grpc.keepalive_time_ms", 30000),
                    ("grpc.keepalive_timeout_ms", 5000),
                    ("grpc.keepalive_permit_without_calls", True),
                    ("grpc.max_receive_message_length", 4 * 1024 * 1024),  # 4MB
                    ("grpc.max_send_message_length", 4 * 1024 * 1024),  # 4MB
                ],
            )

            # Create stub
            self.stub = authentication_pb2_grpc.AuthenticationServiceStub(self.channel)

            logger.info(
                f"Authentication service client initialized: {auth_service_host}:{auth_service_port}"
            )

        except Exception as e:
            logger.error(f"Failed to initialize authentication service client: {e}")
            raise

    def _convert_provider_to_grpc(self, provider: OAuthProvider) -> int:
        """Convert OAuthProvider enum to gRPC enum value."""
        logger.info(f"Converting provider: {provider} (type: {type(provider)})")

        provider_map = {
            OAuthProvider.GOOGLE: authentication_pb2.OAUTH_PROVIDER_GOOGLE,
            OAuthProvider.MICROSOFT: authentication_pb2.OAUTH_PROVIDER_MICROSOFT,
            OAuthProvider.RAPID_HRMS: authentication_pb2.OAUTH_PROVIDER_RAPID_HRMS,
            OAuthProvider.GITHUB: authentication_pb2.OAUTH_PROVIDER_GITHUB,
            OAuthProvider.SLACK: authentication_pb2.OAUTH_PROVIDER_SLACK,
            OAuthProvider.CUSTOM: authentication_pb2.OAUTH_PROVIDER_CUSTOM,
            OAuthProvider.JIRA: authentication_pb2.OAUTH_PROVIDER_JIRA,
            OAuthProvider.ZOHO: authentication_pb2.OAUTH_PROVIDER_ZOHO,
        }

        result = provider_map.get(provider, authentication_pb2.OAUTH_PROVIDER_GOOGLE)
        logger.info(f"Converted to gRPC enum: {result} (type: {type(result)})")
        return result

    def _handle_grpc_error(self, error: grpc.RpcError) -> Dict[str, Any]:
        """Handle gRPC errors and convert to standard response format."""
        logger.error(f"gRPC error: {error.code()} - {error.details()}")

        if error.code() == grpc.StatusCode.UNAVAILABLE:
            return {"success": False, "message": "Authentication service is currently unavailable"}
        elif error.code() == grpc.StatusCode.UNAUTHENTICATED:
            return {"success": False, "message": "Authentication failed"}
        elif error.code() == grpc.StatusCode.INVALID_ARGUMENT:
            return {"success": False, "message": f"Invalid request: {error.details()}"}
        else:
            return {"success": False, "message": f"Authentication service error: {error.details()}"}

    async def initiate_oauth(
        self,
        user_id: str,
        tool_name: str,
        provider: OAuthProvider,
        scopes: Optional[List[str]] = None,
        redirect_uri: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Initiate OAuth authorization flow."""
        try:
            logger.info(f"🔍 Initiating OAuth - Provider: {provider} (type: {type(provider)})")
            logger.info(
                f"🔍 Provider value: {provider.value if hasattr(provider, 'value') else provider}"
            )

            self._ensure_connection()
            grpc_provider = self._convert_provider_to_grpc(provider)
            logger.info(f"🔍 Converted to gRPC provider: {grpc_provider}")

            request = authentication_pb2.OAuthAuthorizeRequest(
                user_id=user_id,
                tool_name=tool_name,
                provider=grpc_provider,
                scopes=scopes or [],
                redirect_uri=redirect_uri or "",
            )

            logger.info(f"🔍 Making gRPC call to InitiateOAuth")
            response = self.stub.InitiateOAuth(request)
            logger.info(
                f"🔍 Received gRPC response: success={response.success}, message={response.message}"
            )

            return {
                "success": response.success,
                "message": response.message,
                "authorization_url": response.authorization_url,
                "state": response.state,
            }

        except grpc.RpcError as e:
            logger.error(f"🔍 gRPC error in initiate_oauth: {e}")
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"🔍 Unexpected error in initiate_oauth: {e}", exc_info=True)
            return {"success": False, "message": "Failed to initiate OAuth flow"}

    async def handle_oauth_callback(
        self, code: Optional[str], state: str, error: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle OAuth callback."""
        try:
            print(f"[DEBUG] handle_oauth_callback")
            request = authentication_pb2.OAuthCallbackRequest(
                code=code or "", state=state, error=error or ""
            )

            response = self.stub.HandleOAuthCallback(request)

            return {
                "success": response.success,
                "message": response.message,
                "user_id": response.user_id,
                "tool_name": response.tool_name,
                "provider": response.provider,
                "redirect_url": response.redirect_url,
            }

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in handle_oauth_callback: {e}")
            return {"success": False, "message": "Failed to handle OAuth callback"}

    async def get_oauth_credentials(
        self, user_id: str, tool_name: str, provider: OAuthProvider
    ) -> Dict[str, Any]:
        """Get OAuth credentials for a user."""
        try:
            self._ensure_connection()
            request = authentication_pb2.OAuthCredentialRequest(
                user_id=user_id,
                tool_name=tool_name,
                provider=self._convert_provider_to_grpc(provider),
            )

            response = self.stub.GetOAuthCredentials(request)

            if response.success:
                return {
                    "success": True,
                    "message": response.message,
                    "user_id": response.user_id,
                    "tool_name": response.tool_name,
                    "provider": response.provider,
                    "access_token": response.access_token,
                    "refresh_token": response.refresh_token,
                    "token_type": response.token_type,
                    "expires_in": response.expires_in,
                    "scope": response.scope,
                    "bot_token": response.bot_token,
                    "user_token": response.user_token,
                    "bot_user_id": response.bot_user_id,
                    "user_id_slack": response.user_id_slack,
                    "team_id": response.team_id,
                    "team_name": response.team_name,
                    "user_scope": response.user_scope,
                }
            else:
                return {"success": False, "message": response.message}

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in get_oauth_credentials: {e}")
            return {"success": False, "message": "Failed to retrieve OAuth credentials"}

    async def get_server_oauth_credentials(
        self,
        server_auth_key: str,
        user_id: str,
        tool_name: str,
        provider: OAuthProvider,
    ) -> Dict[str, Any]:
        """Get OAuth credentials using server authentication."""
        try:
            logger.info(
                f"Creating gRPC request for server OAuth credentials: user_id={user_id}, provider={provider}"
            )

            # Ensure connection is established
            self._ensure_connection()

            # Convert provider to gRPC enum
            grpc_provider = self._convert_provider_to_grpc(provider)
            logger.info(f"Converted provider {provider} to gRPC enum: {grpc_provider}")
            logger.info(f"grpc_provider type: {type(grpc_provider)}")

            # Debug all parameters
            logger.info(f"server_auth_key: {server_auth_key} (type: {type(server_auth_key)})")
            logger.info(f"user_id: {user_id} (type: {type(user_id)})")
            logger.info(f"tool_name: {tool_name} (type: {type(tool_name)})")

            try:
                request = authentication_pb2.ServerOAuthCredentialRequest(
                    server_auth_key=server_auth_key,
                    user_id=user_id,
                    tool_name=tool_name,
                    provider=grpc_provider,
                )
                logger.info("Successfully created gRPC request")
            except Exception as req_error:
                logger.error(f"Error creating gRPC request: {req_error}")
                logger.error(f"Request error type: {type(req_error)}")
                raise

            logger.info(f"Making gRPC call to GetServerOAuthCredentials")
            response = self.stub.GetServerOAuthCredentials(request)
            logger.info(f"Received gRPC response: success={response.success}")

            if response.success:
                return {
                    "success": True,
                    "message": response.message,
                    "user_id": response.user_id,
                    "tool_name": response.tool_name,
                    "provider": response.provider,
                    "access_token": response.access_token,
                    "refresh_token": response.refresh_token,
                    "token_type": response.token_type,
                    "expires_in": response.expires_in,
                    "scope": response.scope,
                    "bot_token": response.bot_token,
                    "user_token": response.user_token,
                    "bot_user_id": response.bot_user_id,
                    "user_id_slack": response.user_id_slack,
                    "team_id": response.team_id,
                    "team_name": response.team_name,
                    "user_scope": response.user_scope,
                }
            else:
                return {"success": False, "message": response.message}

        except grpc.RpcError as e:
            logger.error(f"gRPC error in get_server_oauth_credentials: {e}")
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in get_server_oauth_credentials: {e}")
            logger.error(f"Error type: {type(e)}")
            import traceback

            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"success": False, "message": "Failed to retrieve server OAuth credentials"}

    async def delete_oauth_credentials(
        self, user_id: str, tool_name: str, provider: OAuthProvider
    ) -> Dict[str, Any]:
        """Delete OAuth credentials."""
        try:
            request = authentication_pb2.DeleteOAuthCredentialRequest(
                user_id=user_id,
                tool_name=tool_name,
                provider=self._convert_provider_to_grpc(provider),
            )

            response = self.stub.DeleteOAuthCredentials(request)

            return {"success": response.success, "message": response.message}

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in delete_oauth_credentials: {e}")
            return {"success": False, "message": "Failed to delete OAuth credentials"}

    async def list_oauth_providers(self) -> Dict[str, Any]:
        """List available OAuth providers."""
        try:
            self._ensure_connection()
            request = authentication_pb2.OAuthProvidersListRequest()
            response = self.stub.ListOAuthProviders(request)

            providers = []
            for provider_info in response.providers:
                providers.append(
                    {
                        "provider": provider_info.name,
                        "display_name": provider_info.display_name,
                        "supported_tools": list(provider_info.supported_tools),
                        "is_configured": provider_info.is_configured,
                    }
                )

            return {
                "success": response.success,
                "message": response.message,
                "providers": providers,
            }

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in list_oauth_providers: {e}")
            return {"success": False, "message": "Failed to list OAuth providers", "providers": []}

    async def get_tool_scopes(self, tool_name: str, provider: OAuthProvider) -> Dict[str, Any]:
        """Get required scopes for a tool and provider."""
        try:
            request = authentication_pb2.OAuthToolScopesRequest(
                tool_name=tool_name, provider=self._convert_provider_to_grpc(provider)
            )

            response = self.stub.GetToolScopes(request)

            return {
                "success": response.success,
                "message": response.message,
                "tool_name": response.tool_name,
                "provider": response.provider,
                "scopes": list(response.scopes),
                "description": response.description,
            }

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in get_tool_scopes: {e}")
            return {"success": False, "message": "Failed to get tool scopes", "scopes": []}

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on authentication service."""
        try:
            request = authentication_pb2.HealthCheckRequest()
            response = self.stub.HealthCheck(request)

            return {
                "healthy": response.healthy,
                "status": response.status,
                "version": response.version,
                "dependencies": dict(response.dependencies),
            }

        except grpc.RpcError as e:
            logger.error(f"Authentication service health check failed: {e}")
            return {
                "healthy": False,
                "status": "unhealthy",
                "version": "unknown",
                "dependencies": {},
            }
        except Exception as e:
            logger.error(f"Unexpected error in health_check: {e}")
            return {"healthy": False, "status": "error", "version": "unknown", "dependencies": {}}

    def close(self):
        """Close the gRPC connection."""
        if self.channel:
            self.channel.close()
            logger.info("Authentication service gRPC connection closed")


# Integration Management Methods (Admin Only)
    
    async def create_integration(
        self,
        admin_user_id: str,
        name: str,
        display_name: str,
        description: str,
        integration_type: str,

        is_enabled: bool = True,
        configuration: Optional[Dict[str, str]] = None,
        supported_scopes: Optional[List[str]] = None,
        api_key_config: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """Create a new integration."""
        try:
            self._ensure_connection()
            
            # Map integration type to connection type
            connection_type = authentication_pb2.CONNECTION_TYPE_OAUTH
            if integration_type.lower() == "api_key":
                connection_type = authentication_pb2.CONNECTION_TYPE_API_KEY
            elif integration_type.lower() == "oauth":
                connection_type = authentication_pb2.CONNECTION_TYPE_OAUTH
            
            request = authentication_pb2.CreateIntegrationRequest(
                admin_user_id=admin_user_id,
                logo="",  # Add logo field
                name=name,
                description=description,
                connection_type=connection_type,
                schema_definition=json.dumps(configuration if connection_type == "oauth" else api_key_config),
            )

            response = self.stub.CreateIntegration(request)
            print(f"[DEBUG] CreateIntegration response: {response}")
            return {
                "success": response.success,
                "message": response.message,
                "integration_id": response.integration.id,
            }

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in create_integration: {e}")
            return {"success": False, "message": "Failed to create integration"}

    async def get_integration(self, integration_id: str) -> Dict[str, Any]:
        """Get integration by ID."""
        try:
            self._ensure_connection()
            
            request = authentication_pb2.GetIntegrationRequest(
                integration_id=integration_id
            )

            response = self.stub.GetIntegration(request)

            if response.success:
                integration = response.integration
                # Parse schema definition from JSON string
                schema_definition = {}
                try:
                    import json
                    schema_definition = json.loads(integration.schema_definition) if integration.schema_definition else {}
                except json.JSONDecodeError:
                    schema_definition = {}
                
                reverse_connection_type_map = {
                    authentication_pb2.CONNECTION_TYPE_API_KEY: "api_key",
                    authentication_pb2.CONNECTION_TYPE_OAUTH: "oauth",
                }
                return {
                    "success": True,
                    "message": response.message,
                    "integration": {
                        "id": integration.id,
                        "name": integration.name,
                        "description": integration.description,
                        "integration_type": reverse_connection_type_map.get(integration.connection_type),
                        "is_enabled": integration.is_active if integration.is_active else False,
                        "status": "active" if integration.is_active else "inactive",
                        "configuration": schema_definition if integration.connection_type == authentication_pb2.CONNECTION_TYPE_OAUTH else None,
                        "supported_scopes": schema_definition.get("scopes", []) if integration.connection_type == authentication_pb2.CONNECTION_TYPE_OAUTH else [],
                        "api_key_config": schema_definition if integration.connection_type == authentication_pb2.CONNECTION_TYPE_API_KEY else None,
                        "created_at": integration.created_at,
                        "updated_at": integration.updated_at,
                    }
                }
            else:
                return {"success": False, "message": response.message}

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in get_integration: {e}")
            return {"success": False, "message": "Failed to get integration"}

    async def update_integration(
        self,
        admin_user_id: str,
        integration_id: str,
        display_name: Optional[str] = None,
        description: Optional[str] = None,
        is_enabled: Optional[bool] = None,
        configuration: Optional[Dict[str, str]] = None,
        supported_scopes: Optional[List[str]] = None,
        api_key_config: Optional[List[Dict[str, Any]]] = None,
        integration_type: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Update an existing integration."""
        try:
            self._ensure_connection()
            
            request = authentication_pb2.UpdateIntegrationRequest(
                admin_user_id=admin_user_id,
                integration_id=integration_id,
                logo="",  # Add logo field
                name="",  # Keep name empty for updates
                description=description or "",
                connection_type=authentication_pb2.CONNECTION_TYPE_OAUTH,  # Default to OAuth
                schema_definition=json.dumps({
                    configuration if integration_type == "oauth" else api_key_config
                }) if any([display_name, description, is_enabled is not None, configuration, supported_scopes]) else "",
                is_active=is_enabled if is_enabled is not None else True,
            )

            response = self.stub.UpdateIntegration(request)

            if response.success:
                integration = response.integration
                # Parse schema definition from JSON string
                schema_definition = {}
                try:
                    import json
                    schema_definition = json.loads(integration.schema_definition) if integration.schema_definition else {}
                except json.JSONDecodeError:
                    schema_definition = {}
                
                return {
                    "success": True,
                    "message": response.message,
                    "integration": {
                        "id": integration.id,
                        "name": integration.name,
                        "display_name": schema_definition.get("display_name", integration.name),
                        "description": integration.description,
                        "integration_type": schema_definition.get("integration_type", "oauth"),
                        "provider": schema_definition.get("provider", "custom"),
                        "is_enabled": integration.is_active,
                        "status": "active" if integration.is_active else "inactive",
                        "configuration": schema_definition.get("configuration", {}),
                        "supported_scopes": schema_definition.get("supported_scopes", []),
                        "api_key_config": schema_definition.get("api_key_config"),
                        "created_at": integration.created_at,
                        "updated_at": integration.updated_at,
                        "created_by": schema_definition.get("created_by", "system"),
                    }
                }
            else:
                return {"success": False, "message": response.message}

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in update_integration: {e}")
            return {"success": False, "message": "Failed to update integration"}

    async def delete_integration(
        self, admin_user_id: str, integration_id: str
    ) -> Dict[str, Any]:
        """Delete an integration."""
        try:
            self._ensure_connection()
            
            request = authentication_pb2.DeleteIntegrationRequest(
                admin_user_id=admin_user_id,
                integration_id=integration_id,
            )

            response = self.stub.DeleteIntegration(request)

            return {
                "success": response.success,
                "message": response.message,
            }

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in delete_integration: {e}")
            return {"success": False, "message": "Failed to delete integration"}

    async def list_integrations(
        self,
        page: int = 1,
        page_size: int = 10,
        provider: Optional[str] = None,
        integration_type: Optional[str] = None,
        is_enabled: Optional[bool] = None,
        include_inactive: bool = False,
        connection_type_filter: Optional[str] = None,
    ) -> Dict[str, Any]:
        """List integrations with optional filters and pagination."""
        try:
            self._ensure_connection()
            
            # Map connection type filter to proto enum
            connection_type_enum = authentication_pb2.CONNECTION_TYPE_UNSPECIFIED
            if connection_type_filter:
                connection_type_map = {
                    "api_key": authentication_pb2.CONNECTION_TYPE_API_KEY,
                    "oauth": authentication_pb2.CONNECTION_TYPE_OAUTH,
                }
                
                connection_type_enum = connection_type_map.get(connection_type_filter.lower(), authentication_pb2.CONNECTION_TYPE_UNSPECIFIED)
            
            request = authentication_pb2.ListIntegrationsRequest(
                page=page,
                page_size=page_size,
                provider=provider or "",
                integration_type=integration_type or "",
                is_enabled=is_enabled if is_enabled is not None else False,
                include_inactive=include_inactive,
                connection_type_filter=connection_type_enum,
            )

            response = self.stub.ListIntegrations(request)
            logger.info(f"List Integrations Response: success={response.success}, total={response.total}")
            
            
            
            reverse_connection_type_map = {
                authentication_pb2.CONNECTION_TYPE_API_KEY: "api_key",
                authentication_pb2.CONNECTION_TYPE_OAUTH: "oauth",
            }
            if response.success:
                print(f"[DEBUG] List Integrations Response:{response.integrations}")
                integrations = []
                for integration in response.integrations:
                    # Parse schema definition from JSON string
                    schema_definition = {}
                    try:
                        import json
                        schema_definition = json.loads(integration.schema_definition) if integration.schema_definition else {}
                    except json.JSONDecodeError:
                        schema_definition = {}
                    
                    integrations.append({
                        "id": integration.id,
                        "name": integration.name,
                        "description": integration.description,
                        "integration_type": reverse_connection_type_map.get(integration.connection_type),
                        "is_enabled": integration.is_active if integration.is_active else False,
                        "status": "active" if integration.is_active else "inactive",
                        "configuration": schema_definition if integration.connection_type == authentication_pb2.CONNECTION_TYPE_OAUTH else None,
                        "supported_scopes": schema_definition.get("scopes", []) if integration.connection_type == authentication_pb2.CONNECTION_TYPE_OAUTH else [],
                        "api_key_config": schema_definition if integration.connection_type == authentication_pb2.CONNECTION_TYPE_API_KEY else None,
                        "created_at": integration.created_at,
                        "updated_at": integration.updated_at,
                    })

                return {
                    "success": True,
                    "message": response.message,
                    "integrations": integrations,
                    "total": response.total,
                    "page": response.page,
                    "page_size": response.page_size,
                }
            else:
                return {"success": False, "message": response.message, "integrations": [], "total": 0, "page": page, "page_size": page_size}

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in list_integrations: {e}")
            return {"success": False, "message": "Failed to list integrations", "integrations": [], "total": 0, "page": page, "page_size": page_size}

    async def refresh_oauth_tokens(self, user_id: str, tool_name: str, provider: str = None, integration_id: str = None) -> Dict:
        """
        Refresh OAuth tokens for a user.
        """
        try:
            self._ensure_connection()
            
            request = authentication_pb2.RefreshOAuthTokensRequest(
                user_id=user_id,
                tool_name=tool_name,
                provider=provider or "",
                integration_id=integration_id or ""
            )
            response = self.stub.RefreshOAuthTokens(request)
            
            return {
                "success": response.success,
                "message": response.message,
                "access_token": response.access_token,
                "token_type": response.token_type,
                "expires_in": response.expires_in,
                "scope": response.scope
            }
        except grpc.RpcError as e:
            logger.error(f"gRPC error in refresh_oauth_tokens: {e}")
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in refresh_oauth_tokens: {e}")
            return {"success": False, "message": "Failed to refresh OAuth tokens"}

    async def list_user_integrations(self, user_id: str) -> List[Dict]:
        """
        List user's connected integrations with OAuth status.
        """
        try:
            self._ensure_connection()
            
            request = authentication_pb2.ListUserIntegrationsRequest(user_id=user_id)
            response = self.stub.ListUserIntegrations(request)
            reverse_connection_type_map = {
                authentication_pb2.CONNECTION_TYPE_API_KEY: "api_key",
                authentication_pb2.CONNECTION_TYPE_OAUTH: "oauth",
            }
            integrations = []
            for integration in response.integrations:
                # Parse schema_definition from JSON string
                schema_definition = []
                try:
                    schema_definition = json.loads(integration.schema_definition) if integration.schema_definition else []
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse schema_definition JSON for integration {integration.integration_id}")
                    schema_definition = []

                integrations.append({
                    "user_id": integration.user_id,
                    "integration_id": integration.integration_id,
                    "integration_name": integration.integration_name,
                    "is_connected": integration.is_connected,
                    "last_used_at": integration.last_used_at,
                    "created_at": integration.created_at,
                    "scopes": list(integration.scopes),
                    "connection_type": reverse_connection_type_map.get(integration.connection_type),
                    "schema_definition": schema_definition
                })
            
            return integrations
        except grpc.RpcError as e:
            logger.error(f"gRPC error in list_user_integrations: {e}")
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in list_user_integrations: {e}")
            return {"success": False, "message": "Failed to list user integrations", "integrations": []}

    async def initiate_oauth_by_integration(self, user_id: str, integration_id: str, redirect_url: str = None) -> Dict:
        """
        Initiate OAuth authorization flow using integration ID.
        """
        try:
            self._ensure_connection()
            
            request = authentication_pb2.InitiateOAuthByIntegrationRequest(
                user_id=user_id,
                integration_id=integration_id,
                redirect_uri=redirect_url or ""
            )
            response = self.stub.InitiateOAuthByIntegration(request)
            print(f"[DEBUG] authorization_url: {response.authorization_url}")
            return {
                "success": response.success,
                "message": response.message,
                "authorization_url": response.authorization_url,
                "state": response.state
            }
        except grpc.RpcError as e:
            logger.error(f"gRPC error in initiate_oauth_by_integration: {e}")
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in initiate_oauth_by_integration: {e}")
            return {"success": False, "message": "Failed to initiate OAuth by integration"}

    async def get_oauth_credentials_by_integration(self, user_id: str, integration_id: str) -> Dict:
        """
        Get OAuth credentials using integration ID.
        """
        try:
            self._ensure_connection()
            
            request = authentication_pb2.GetOAuthCredentialsByIntegrationRequest(
                user_id=user_id,
                integration_id=integration_id
            )
            response = self.stub.GetOAuthCredentialsByIntegration(request)
            
            # Extract user integration status if available
            user_integration_status = None
            reverse_connection_type_map = {
                authentication_pb2.CONNECTION_TYPE_API_KEY: "api_key",
                authentication_pb2.CONNECTION_TYPE_OAUTH: "oauth",
            }
            if response.user_integration_status:
                # Parse schema_definition from JSON string
                schema_definition = []
                try:
                    schema_definition = json.loads(response.user_integration_status.schema_definition) if response.user_integration_status.schema_definition else []
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse schema_definition JSON for integration {response.user_integration_status.integration_id}")
                    schema_definition = []

                user_integration_status = {
                    "user_id": response.user_integration_status.user_id,
                    "integration_id": response.user_integration_status.integration_id,
                    "integration_name": response.user_integration_status.integration_name,
                    "is_connected": response.user_integration_status.is_connected,
                    "last_used_at": response.user_integration_status.last_used_at,
                    "created_at": response.user_integration_status.created_at,
                    "scopes": list(response.user_integration_status.scopes),
                    "connection_type": reverse_connection_type_map.get(response.user_integration_status.connection_type),
                    "schema_definition": schema_definition
                }

            return {
                "success": response.success,
                "message": response.message,
                "access_token": response.access_token,
                "token_type": response.token_type,
                "expires_in": response.expires_in,
                "scope": response.scope,
                "user_integration_status": user_integration_status
            }
        except grpc.RpcError as e:
            logger.error(f"gRPC error in get_oauth_credentials_by_integration: {e}")
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in get_oauth_credentials_by_integration: {e}")
            return {"success": False, "message": "Failed to get OAuth credentials by integration"}

    async def delete_oauth_credentials_by_integration(self, user_id: str, integration_id: str) -> Dict:
        """
        Delete OAuth credentials using integration ID.
        """
        try:
            self._ensure_connection()
            
            request = authentication_pb2.DeleteOAuthCredentialsByIntegrationRequest(
                user_id=user_id,
                integration_id=integration_id
            )
            response = self.stub.DeleteOAuthCredentialsByIntegration(request)
            
            return {
                "success": response.success,
                "message": response.message
            }
        except grpc.RpcError as e:
            logger.error(f"gRPC error in delete_oauth_credentials_by_integration: {e}")
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in delete_oauth_credentials_by_integration: {e}")
            return {"success": False, "message": "Failed to delete OAuth credentials by integration"}

    async def handle_oauth_callback_new(
        self, code: Optional[str], state: str, error: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle OAuth callback."""
        try:
            print(f"[DEBUG] handle_oauth_callback_new")
            self._ensure_connection()
            
            request = authentication_pb2.OAuthCallbackRequest(
                code=code or "", state=state, error=error or ""
            )

            response = self.stub.HandleOAuthCallbackNew(request)

            print(f"[DEBUG] HandleOAuthCallbackNew response: {response}")
            return {
                "success": response.success,
                "message": response.message,
                "user_id": response.user_id,
                "tool_name": response.tool_name,
                "provider": response.provider,
                "redirect_url": response.redirect_url,
            }

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in handle_oauth_callback: {e}")
            return {"success": False, "message": "Failed to handle OAuth callback"}

    def close(self):
        """Close the gRPC connection."""
        if self.channel:
            self.channel.close()
            logger.info("Authentication service gRPC connection closed")

    # API Key Credential Management Methods

    async def store_api_key_credentials(
        self, user_id: str, integration_id: str, credentials: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Store API key credentials for a user and integration."""
        try:
            self._ensure_connection()
            
            # Convert credentials dict to JSON string
            credentials_json = json.dumps(credentials)
            
            request = authentication_pb2.StoreAPIKeyCredentialsRequest(
                user_id=user_id,
                integration_id=integration_id,
                credentials=credentials_json,
            )

            response = self.stub.StoreAPIKeyCredentials(request)

            return {
                "success": response.success,
                "message": response.message,
                "integration_id": response.integration_id,
            }

        except grpc.RpcError as e:
            logger.error(f"gRPC error in store_api_key_credentials: {e}")
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in store_api_key_credentials: {e}")
            return {"success": False, "message": "Failed to store API key credentials"}

    async def get_api_key_credentials(
        self, user_id: str, integration_id: str
    ) -> Dict[str, Any]:
        """Get API key credentials for a user and integration."""
        try:
            self._ensure_connection()
            
            request = authentication_pb2.GetAPIKeyCredentialsRequest(
                user_id=user_id,
                integration_id=integration_id,
            )

            response = self.stub.GetAPIKeyCredentials(request)

            if response.success:
                # Parse credentials from JSON string
                credentials = {}
                try:
                    credentials = json.loads(response.credentials) if response.credentials else {}
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse credentials JSON for user {user_id}, integration {integration_id}")
                    credentials = {}

                # Parse schema_definition from JSON string
                schema_definition = {}
                try:
                    schema_definition = json.loads(response.schema_definition) if response.schema_definition else {}
                    print(f"[DEBUG] schema_definition: {schema_definition}")
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse schema_definition JSON for user {user_id}, integration {integration_id}")
                    schema_definition = {}

                return {
                    "success": True,
                    "message": response.message,
                    "user_id": response.user_id,
                    "integration_id": response.integration_id,
                    "credentials": credentials,
                    "is_connected": response.is_connected,
                    "last_used_at": response.last_used_at,
                    "schema_definition": schema_definition,
                }
            else:
                return {"success": False, "message": response.message}

        except grpc.RpcError as e:
            logger.error(f"gRPC error in get_api_key_credentials: {e}")
            return self._handle_grpc_error(e)

    async def update_api_key_credentials(
        self, user_id: str, integration_id: str, credentials: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update API key credentials for a user and integration."""
        try:
            self._ensure_connection()
            
            # Convert credentials dict to JSON string
            credentials_json = json.dumps(credentials)
            
            request = authentication_pb2.UpdateAPIKeyCredentialsRequest(
                user_id=user_id,
                integration_id=integration_id,
                credentials=credentials_json,
            )

            response = self.stub.UpdateAPIKeyCredentials(request)

            return {
                "success": response.success,
                "message": response.message,
                "integration_id": response.integration_id,
            }

        except grpc.RpcError as e:
            logger.error(f"gRPC error in update_api_key_credentials: {e}")
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in update_api_key_credentials: {e}")
            return {"success": False, "message": "Failed to update API key credentials"}

    async def delete_api_key_credentials(
        self, user_id: str, integration_id: str
    ) -> Dict[str, Any]:
        """Delete API key credentials for a user and integration."""
        try:
            self._ensure_connection()
            
            request = authentication_pb2.DeleteAPIKeyCredentialsRequest(
                user_id=user_id,
                integration_id=integration_id,
            )

            response = self.stub.DeleteAPIKeyCredentials(request)

            return {
                "success": response.success,
                "message": response.message,
            }

        except grpc.RpcError as e:
            logger.error(f"gRPC error in delete_api_key_credentials: {e}")
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in delete_api_key_credentials: {e}")
            return {"success": False, "message": "Failed to delete API key credentials"}


# Global authentication service client instance - lazy initialization
auth_service_client = None


def get_auth_service_client() -> AuthenticationServiceClient:
    """Get the global authentication service client instance."""
    global auth_service_client
    if auth_service_client is None:
        auth_service_client = AuthenticationServiceClient()
    return auth_service_client
