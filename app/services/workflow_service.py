import grpc
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.services.version_functions import WorkflowVersionFunctions
from app.services.workflow_functions import WorkflowFunctions
from app.services.marketplace_functions import WorkflowMarketplaceFunctions
from app.utils.logger import setup_logger
from app.services.workflow_builder.workflow_builder_service import WorkflowBuilderFunctions

logger = setup_logger("workflow-service")


class WorkflowService(workflow_pb2_grpc.WorkflowServiceServicer):
    def __init__(self):
        """Initialize the WorkflowService with a KafkaProducer instance"""
        self.workflow_functions = WorkflowFunctions()
        self.marketplace_functions = WorkflowMarketplaceFunctions()
        self.workflow_builder_functions = WorkflowBuilderFunctions()
        self.workflow_version_functions = WorkflowVersionFunctions()

        logger.info("WorkflowService initialized")

    def createWorkflow(
        self, request: workflow_pb2.CreateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CreateWorkflowResponse:
        try:
            logger.info("createWorkflow request received")
            return self.workflow_functions.createWorkflow(request, context)
        except Exception as e:
            logger.error(f"createWorkflow failed{str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def getWorkflow(
        self, request: workflow_pb2.GetWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.WorkflowResponse:
        try:
            logger.info("getWorkflow request received")
            return self.workflow_functions.getWorkflow(request, context)
        except Exception as e:
            logger.error(f"getWorkflow failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to get conversation: {str(e)}")

    def deleteWorkflow(
        self, request: workflow_pb2.DeleteWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.DeleteWorkflowResponse:
        try:
            logger.info("deleteWorkflow request received")
            return self.workflow_functions.deleteWorkflow(request, context)
        except Exception as e:
            logger.error(f"deleteWorkflow failed{str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to delete conversation: {str(e)}"
            )

    def listWorkflows(
        self, request: workflow_pb2.ListWorkflowsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ListWorkflowsResponse:
        try:
            logger.info("listWorkflows request received")
            return self.workflow_functions.listWorkflows(request, context)
        except Exception as e:
            logger.error(f"listWorkflows failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to list workflows: {str(e)}")

    def listWorkflowsByUserId(
        self, request: workflow_pb2.ListWorkflowsByUserIdRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ListWorkflowsResponse:
        try:
            logger.info("listWorkflowsByUserId request received")
            return self.workflow_functions.listWorkflowsByUserId(request, context)
        except Exception as e:
            logger.error(f"listWorkflowsByUserId failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to list workflows: {str(e)}")

    def getWorkflowsByIds(
        self, request: workflow_pb2.GetWorkflowsByIdsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetWorkflowsByIdsResponse:
        try:
            logger.info("getWorkflowsByIds request received")
            return self.workflow_functions.getWorkflowsByIds(request, context)
        except Exception as e:
            logger.error(f"getWorkflowsByIds failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to get workflows: {str(e)}")

    def toggleWorkflowVisibility(
        self, request: workflow_pb2.ToggleWorkflowVisibilityRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ToggleWorkflowVisibilityResponse:
        try:
            logger.info("toggleWorkflowVisibility request received")
            return self.workflow_functions.toggleWorkflowVisibility(request, context)
        except Exception as e:
            logger.error(f"toggleWorkflowVisibility failed{str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to toggle workflow visibility: {str(e)}"
            )

    def updateWorkflow(
        self, request: workflow_pb2.UpdateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UpdateWorkflowResponse:
        try:
            logger.info("updateWorkflow request received")
            return self.workflow_functions.updateWorkflow(request, context)
        except Exception as e:
            logger.error(f"updateWorkflow failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to update workflow: {str(e)}")

    def updateWorkflowSettings(
        self, request: workflow_pb2.UpdateWorkflowSettingsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UpdateWorkflowSettingsResponse:
        try:
            logger.info("updateWorkflowSettings request received")
            return self.workflow_functions.updateWorkflowSettings(request, context)
        except Exception as e:
            logger.error(f"updateWorkflowSettings failed{str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to update workflow settings: {str(e)}"
            )


    def getTemplate(
        self, request: workflow_pb2.GetTemplateRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetTemplateResponse:
        try:
            logger.info("getTemplate request received")
            return self.marketplace_functions.getTemplate(request, context)
        except Exception as e:
            logger.error(f"getTemplate failed{str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to get marketplace listing: {str(e)}"
            )


    def getMarketplaceWorkflows(
        self, request: workflow_pb2.GetMarketplaceWorkflowsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetMarketplaceWorkflowsResponse:
        try:
            logger.info("getMarketplaceWorkflows request received")
            return self.marketplace_functions.getMarketplaceWorkflows(request, context)
        except Exception as e:
            logger.error(f"discoverComponents failed{str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to discover components: {str(e)}"
            )

    def discoverComponents(
        self, request: workflow_pb2.DiscoverComponentsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.DiscoverComponentsResponse:
        try:
            logger.info("discoverComponents request received")
            return self.workflow_builder_functions.discoverComponents(request, context)
        except Exception as e:
            logger.error(f"discoverComponents failed: {str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to discover components: {str(e)}"
            )

    def validateWorkflow(
        self, request: workflow_pb2.ValidateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ValidateWorkflowResponse:
        try:
            logger.info("validateWorkflow request received")
            return self.workflow_builder_functions.validateWorkflow(request, context)
        except Exception as e:
            logger.error(f"validateWorkflow failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to validate workflow: {str(e)}")

    def rateWorkflow(
        self, request: workflow_pb2.RateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.RateWorkflowResponse:
        try:
            logger.info("getMarketplaceWorkflows request received")
            return self.marketplace_functions.rateWorkflow(request, context)
        except Exception as e:
            logger.error(f"rateWorkflow failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to get rateWorkflow: {str(e)}")

    def useWorkflow(
        self, request: workflow_pb2.UseWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UseWorkflowResponse:
        try:
            logger.info("getMarketplaceWorkflows request received")
            return self.marketplace_functions.useWorkflow(request, context)
        except Exception as e:
            logger.error(f"useWorkflow failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to get useWorkflow: {str(e)}")

    # Version management endpoints
    def listWorkflowVersions(
        self, request: workflow_pb2.ListWorkflowVersionsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ListWorkflowVersionsResponse:
        try:
            logger.info("listWorkflowVersions request received")
            return self.workflow_version_functions.listWorkflowVersions(request, context)
        except Exception as e:
            logger.error(f"listWorkflowVersions failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to list workflow versions: {str(e)}")

    def getWorkflowVersion(
        self, request: workflow_pb2.GetWorkflowVersionRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetWorkflowVersionResponse:
        try:
            logger.info("getWorkflowVersion request received")
            return self.workflow_version_functions.getWorkflowVersion(request, context)
        except Exception as e:
            logger.error(f"getWorkflowVersion failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to get workflow version: {str(e)}")

    def switchWorkflowVersion(
        self, request: workflow_pb2.SwitchWorkflowVersionRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.SwitchWorkflowVersionResponse:
        try:
            logger.info("switchWorkflowVersion request received")
            return self.workflow_version_functions.switchWorkflowVersion(request, context)
        except Exception as e:
            logger.error(f"switchWorkflowVersion failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to switch workflow version: {str(e)}")

    def pullUpdatesFromSource(
        self, request: workflow_pb2.PullUpdatesFromSourceRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.PullUpdatesFromSourceResponse:
        try:
            logger.info("pullUpdatesFromSource request received")
            return self.marketplace_functions.pullUpdatesFromSource(request, context)
        except Exception as e:
            logger.error(f"pullUpdatesFromSource failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to pull updates from source: {str(e)}")

    def checkForUpdates(
        self, request: workflow_pb2.CheckForUpdatesRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CheckForUpdatesResponse:
        try:
            logger.info("checkForUpdates request received")
            return self.marketplace_functions.checkForUpdates(request, context)
        except Exception as e:
            logger.error(f"checkForUpdates failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to check for updates: {str(e)}")
    
    def createVersionAndPublish(
        self, request: workflow_pb2.CreateVersionAndPublishRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CreateVersionAndPublishResponse:
        try:
            logger.info("createVersionAndPublish request received")
            return self.workflow_version_functions.createVersionAndPublish(request, context)
        except Exception as e:
            logger.error(f"createVersionAndPublish failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to create version and publish: {str(e)}")
