import os
import json
import grpc
from typing import Dict, Any, List, Optional
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.core.config import settings


class WorkflowServiceClient:
    """Client for the Workflow gRPC service."""

    def __init__(self):
        """Initialize the client with the service address."""
        self.channel = grpc.insecure_channel(
            f"{settings.WORKFLOW_SERVICE_HOST}:{settings.WORKFLOW_SERVICE_PORT}"
        )
        self.stub = workflow_pb2_grpc.WorkflowServiceStub(self.channel)

    def _handle_error(self, e: grpc.RpcError) -> Exception:
        """Handle gRPC errors and convert them to appropriate exceptions."""
        if e.code() == grpc.StatusCode.NOT_FOUND:
            return ValueError(e.details())
        elif e.code() == grpc.StatusCode.INVALID_ARGUMENT:
            return ValueError(e.details())
        elif e.code() == grpc.StatusCode.PERMISSION_DENIED:
            return PermissionError(e.details())
        else:
            return RuntimeError(f"gRPC error: {e.details()}")

    def _get_category_enum(self, category: str) -> int:
        """Convert category string to enum value."""
        category_map = {
            "AUTOMATION": workflow_pb2.WorkflowCategory.AUTOMATION,
            "INTEGRATION": workflow_pb2.WorkflowCategory.INTEGRATION,
            "DATA_PROCESSING": workflow_pb2.WorkflowCategory.DATA_PROCESSING,
            "COMMUNICATION": workflow_pb2.WorkflowCategory.COMMUNICATION,
            "ANALYTICS": workflow_pb2.WorkflowCategory.ANALYTICS,
            "OTHER": workflow_pb2.WorkflowCategory.OTHER,
        }
        return category_map.get(category, workflow_pb2.WorkflowCategory.OTHER)

    async def get_marketplace_workflows(
        self,
        page: int = 1,
        page_size: int = 10,
        search: Optional[str] = None,
        category: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        sort_by: Optional[str] = None,
    ) -> Any:
        """
        Retrieves a paginated list of public workflows for the marketplace.

        Args:
            page: Page number for pagination
            page_size: Number of items per page
            search: Optional search term to filter by name or description
            category: Optional category filter
            tags: Optional tags filter as a dictionary of key-value pairs
            sort_by: Optional sort criteria (NEWEST, OLDEST, MOST_POPULAR, HIGHEST_RATED)

        Returns:
            Response containing the list of marketplace workflows and pagination metadata
        """
        try:
            request_args = {
                "page": page,
                "page_size": page_size,
                "visibility": "WORKFLOW_VISIBILITY_PUBLIC",  # Marketplace only shows public workflows
            }

            if search:
                request_args["search"] = search
            if category:
                request_args["category"] = self._get_category_enum(category)
            if tags:
                request_args["tags"] = json.dumps(tags)
            if sort_by:
                request_args["sort_by"] = sort_by

            request = workflow_pb2.GetMarketplaceWorkflowsRequest(**request_args)

            print(f"[DEBUG] gRPC Client: Sending GetMarketplaceWorkflowsRequest: {request}")
            response = self.stub.getMarketplaceWorkflows(request)
            print(f"[DEBUG] gRPC Client: Received response: {response}")

            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC Client: RpcError in get_marketplace_workflows: {e.details()}")
            raise self._handle_error(e)

    async def get_workflow(self, workflow_id: str) -> Any:
        """
        Retrieves a specific workflow by ID.

        Args:
            workflow_id: The ID of the workflow to retrieve

        Returns:
            Response containing the workflow details
        """
        try:
            request = workflow_pb2.GetWorkflowRequest(id=workflow_id)
            response = self.stub.getWorkflow(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC Client: RpcError in get_workflow: {e.details()}")
            raise self._handle_error(e)
