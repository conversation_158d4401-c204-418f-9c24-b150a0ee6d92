# app/services/agent_service.py
import grpc
import json
import structlog
from app.core.config import settings
from app.grpc import agent_pb2, agent_pb2_grpc
from app.services.agent_functions import AgentFunctionsService
from app.services.marketplace_functions import AgentMarketplaceFunctionsService
from app.services.agent_avatar_functions import AgentAvatarFunctionsService
from app.services.agent_version_functions import AgentVersionFunctions

logger = structlog.get_logger()


class AgentService(agent_pb2_grpc.AgentServiceServicer):
    """
    Service for managing agent configurations.

    This service handles CRUD operations for agent configurations, including
    validation, storage in Google Cloud Storage, and database operations.
    """

    def __init__(self):
        self.agent_functions = AgentFunctionsService()
        self.marketplace_functions = AgentMarketplaceFunctionsService()
        self.avatar_functions = AgentAvatarFunctionsService()
        self.agent_version_functions = AgentVersionFunctions()

    def createAgent(
        self, request: agent_pb2.CreateAgentRequest, context: grpc.ServicerContext
    ) -> agent_pb2.CreateAgentResponse:
        try:
            logger.info("create_agent_request")
            return self.agent_functions.createAgent(request, context)
        except Exception as e:
            logger.error(f"create agent failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def getAgent(
        self, request: agent_pb2.GetAgentRequest, context: grpc.ServicerContext
    ) -> agent_pb2.AgentResponse:
        try:
            logger.info("get_agent_request")
            return self.agent_functions.getAgent(request, context)
        except Exception as e:
            logger.error(f"get agent failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def UpdateAgentCoreDetails(
        self, request: agent_pb2.UpdateAgentCoreDetailsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            logger.info("update_agent_core_details_request")
            return self.agent_functions.UpdateAgentCoreDetails(request, context)
        except Exception as e:
            logger.error(f"update agent core details failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def UpdateAgentKnowledge(
        self, request: agent_pb2.UpdateAgentKnowledgeRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            logger.info("update_agent_knowledge_request")
            return self.agent_functions.UpdateAgentKnowledge(request, context)
        except Exception as e:
            logger.error(f"update agent knowledge failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def UpdateAgentMcpServers(
        self, request: agent_pb2.UpdateAgentMcpServersRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            logger.info("update_agent_mcp_servers_request")
            return self.agent_functions.UpdateAgentMcpServers(request, context)
        except Exception as e:
            logger.error(f"update agent mcp servers failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def UpdateAgentWorkflows(
        self, request: agent_pb2.UpdateAgentWorkflowsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            logger.info("update_agent_workflows_request")
            return self.agent_functions.UpdateAgentWorkflows(request, context)
        except Exception as e:
            logger.error(f"update agent workflows failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def UpdateAgentSettings(
        self, request: agent_pb2.UpdateAgentSettingsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            logger.info("update_agent_settings_request")
            return self.agent_functions.UpdateAgentSettings(request, context)
        except Exception as e:
            logger.error(f"update agent settings failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def ToggleAgentVisibility(
        self, request: agent_pb2.ToggleAgentVisibilityRequest, context: grpc.ServicerContext
    ) -> agent_pb2.ToggleAgentVisibilityResponse:
        try:
            logger.info("toggle_agent_visibility_request")
            return self.agent_functions.ToggleAgentVisibility(request, context)
        except Exception as e:
            logger.error(f"toggle agent visibility failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def deleteAgent(
        self, request: agent_pb2.DeleteAgentRequest, context: grpc.ServicerContext
    ) -> agent_pb2.DeleteAgentResponse:
        try:
            logger.info("delete_agent_request")
            return self.agent_functions.deleteAgent(request, context)
        except Exception as e:
            logger.error(f"delete agent failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def listAgents(
        self, request: agent_pb2.ListAgentsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.ListAgentsResponse:
        try:
            logger.info("list_agents_request")
            return self.agent_functions.listAgents(request, context)
        except Exception as e:
            logger.error(f"list agents failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def getAgentsByIds(
        self, request: agent_pb2.GetAgentsByIdsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.GetAgentsByIdsResponse:
        try:
            logger.info("get_agents_by_ids_request")
            return self.agent_functions.getAgentsByIds(request, context)
        except Exception as e:
            logger.error(f"get agents by ids failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def createAgentFromTemplate(
        self, request: agent_pb2.CreateAgentFromTemplateRequest, context: grpc.ServicerContext
    ) -> agent_pb2.CreateAgentFromTemplateResponse:
        try:
            logger.info("create_agent_from_template_request")
            return self.marketplace_functions.createAgentFromTemplate(request, context)
        except Exception as e:
            logger.error(f"create agent from template failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def getTemplate(
        self, request: agent_pb2.GetTemplateRequest, context: grpc.ServicerContext
    ) -> agent_pb2.GetTemplateResponse:
        try:
            logger.info("get_template_request")
            return self.marketplace_functions.getTemplate(request, context)
        except Exception as e:
            logger.error(f"get template failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def listTemplates(
        self, request: agent_pb2.ListTemplatesRequest, context: grpc.ServicerContext
    ) -> agent_pb2.ListTemplatesResponse:
        try:
            logger.info("list_templates_request")
            return self.marketplace_functions.listTemplates(request, context)
        except Exception as e:
            logger.error(f"list templates failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def getMarketplaceAgents(
        self, request: agent_pb2.GetMarketplaceAgentsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.GetMarketplaceAgentsResponse:
        try:
            logger.info("get_marketplace_agents_request")
            return self.marketplace_functions.getMarketplaceAgents(request, context)
        except Exception as e:
            logger.error(f"get marketplace agents failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def getMarketplaceAgentDetail(
        self, request: agent_pb2.GetMarketplaceAgentDetailRequest, context: grpc.ServicerContext
    ) -> agent_pb2.GetMarketplaceAgentDetailResponse:
        try:
            logger.info("get_marketplace_agent_detail_request")
            return self.marketplace_functions.getMarketplaceAgentDetail(request, context)
        except Exception as e:
            logger.error(f"get marketplace agent detail failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def rateAgent(
        self, request: agent_pb2.RateAgentRequest, context: grpc.ServicerContext
    ) -> agent_pb2.RateAgentResponse:
        try:
            logger.info("rate_agent_request")
            return self.agent_functions.rateAgent(request, context)
        except Exception as e:
            logger.error(f"rate agent failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def useAgent(
        self, request: agent_pb2.UseAgentRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UseAgentResponse:
        try:
            logger.info("use_agent_request")
            return self.marketplace_functions.useAgent(request, context)
        except Exception as e:
            logger.error(f"use agent failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def createAgentAvatar(
        self, request: agent_pb2.CreateAgentAvatarRequest, context: grpc.ServicerContext
    ) -> agent_pb2.CreateAgentAvatarResponse:
        try:
            logger.info("create_agent_avatar_request")
            return self.avatar_functions.createAgentAvatar(request, context)
        except Exception as e:
            logger.error(f"create agent avatar failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def getAgentAvatar(
        self, request: agent_pb2.GetAgentAvatarRequest, context: grpc.ServicerContext
    ) -> agent_pb2.GetAgentAvatarResponse:
        try:
            logger.info("get_agent_avatar_request")
            return self.avatar_functions.getAgentAvatar(request, context)
        except Exception as e:
            logger.error(f"get agent avatar failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def listAgentAvatars(
        self, request: agent_pb2.ListAgentAvatarsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.ListAgentAvatarsResponse:
        try:
            logger.info("list_agent_avatars_request")
            return self.avatar_functions.listAgentAvatars(request, context)
        except Exception as e:
            logger.error(f"list agent avatars failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def deleteAgentAvatar(
        self, request: agent_pb2.DeleteAgentAvatarRequest, context: grpc.ServicerContext
    ) -> agent_pb2.DeleteAgentAvatarResponse:
        try:
            logger.info("delete_agent_avatar_request")
            return self.avatar_functions.deleteAgentAvatar(request, context)
        except Exception as e:
            logger.error(f"delete agent avatar failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    def UpdateAgentCapabilities(
        self, request: agent_pb2.UpdateAgentCapabilitiesRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            logger.info("update_agent_capabilities_request")
            return self.agent_functions.UpdateAgentCapabilities(request, context)
        except Exception as e:
            logger.error(f"update agent capabilities failed{str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Internal server error: {str(e)}")

    # Version management endpoints
    def listAgentVersions(
        self, request: agent_pb2.ListAgentVersionsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.ListAgentVersionsResponse:
        try:
            logger.info("listAgentVersions request received")
            return self.agent_version_functions.listAgentVersions(request, context)
        except Exception as e:
            logger.error(f"listAgentVersions failed: {str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to list agent versions: {str(e)}"
            )

    def getAgentVersion(
        self, request: agent_pb2.GetAgentVersionRequest, context: grpc.ServicerContext
    ) -> agent_pb2.GetAgentVersionResponse:
        try:
            logger.info("getAgentVersion request received")
            return self.agent_version_functions.getAgentVersion(request, context)
        except Exception as e:
            logger.error(f"getAgentVersion failed: {str(e)}")
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to get agent version: {str(e)}")

    def switchAgentVersion(
        self, request: agent_pb2.SwitchAgentVersionRequest, context: grpc.ServicerContext
    ) -> agent_pb2.SwitchAgentVersionResponse:
        try:
            logger.info("switchAgentVersion request received")
            return self.agent_version_functions.switchAgentVersion(request, context)
        except Exception as e:
            logger.error(f"switchAgentVersion failed: {str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to switch agent version: {str(e)}"
            )

    def createVersionAndPublish(
        self, request: agent_pb2.CreateVersionAndPublishRequest, context: grpc.ServicerContext
    ) -> agent_pb2.CreateVersionAndPublishResponse:
        try:
            logger.info("createVersionAndPublish request received")
            return self.agent_version_functions.createVersionAndPublish(request, context)
        except Exception as e:
            logger.error(f"createVersionAndPublish failed: {str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create version and publish: {str(e)}"
            )

    def UpdateAgentVariables(
        self, request: agent_pb2.UpdateAgentVariablesRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            print("update_agent_variables_request")
            logger.info("update_agent_variables_request")
            return self.agent_functions.UpdateAgentVariables(request, context)
        except Exception as e:
            logger.error(f"update agent variables failed: {str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to update agent variables: {str(e)}"
            )

    def UpdateAgentCombined(
        self, request: agent_pb2.UpdateAgentCombinedRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            logger.info("update_agent_combined_request")
            return self.agent_functions.UpdateAgentCombined(request, context)
        except Exception as e:
            logger.error(f"update_agent_combined_request failed: {str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to update_agent_combined_request: {str(e)}"
            )
