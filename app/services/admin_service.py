import grpc
from typing import Optional
from app.core.config import settings
from app.grpc_ import admin_pb2, admin_pb2_grpc
from fastapi import HTTPException


class AdminServiceClient:

    def __init__(self):
        self.channel = grpc.insecure_channel(
          f"{settings.ADMIN_SERVICE_HOST}:{settings.ADMIN_SERVICE_PORT}"
        )
        self.stub = admin_pb2_grpc.AdminServiceStub(self.channel)

    async def login(self, email: str, password: str):
        request = admin_pb2.LoginRequest(email=email, password=password)
        try:
            response = self.stub.login(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
        except Exception as e:
            print(f"Unexpected error during login: {str(e)}")
            raise Exception(f"Login failed: {str(e)}")

    async def access_token(self, refresh_token: str):
        request = admin_pb2.AccessTokenRequest(refreshToken=refresh_token)
        try:
            response = self.stub.accessToken(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def create_admin(
        self, email: str, password: str, full_name: str, role_ids: Optional[list[str]] = None
    ):
        request = admin_pb2.CreateAdminRequest(
            email=email, password=password, fullName=full_name, roleIds=role_ids or []
        )
        try:
            response = self.stub.createAdmin(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_admin(self, admin_id: str):
        request = admin_pb2.GetAdminRequest(adminId=admin_id)
        try:
            response = self.stub.getAdmin(request)
            print(f"get_admin response: {response}")  # Add logging
            if hasattr(response, 'admin') and response.admin:
                return admin_pb2.AdminResponse(success=True, message="Success", admin=response.admin)
            else:
                # Create a minimal AdminInfo object with correct protobuf field names
                admin_info = admin_pb2.AdminInfo(
                    adminId=admin_id,
                    email="<EMAIL>",  # From token
                    fullName="Admin User",
                    roles=[],  # Empty roles list
                    createdAt="2025-07-14T00:00:00Z",
                    updatedAt="2025-07-14T00:00:00Z"
                )
                return admin_pb2.AdminResponse(success=False, message="Admin not found", admin=admin_info)
        except grpc.RpcError as e:
            print(f"gRPC error in get_admin: {str(e)}")  # Add logging
            if e.code() == grpc.StatusCode.NOT_FOUND:
                # Create a minimal AdminInfo object
                admin_info = admin_pb2.AdminInfo(
                    adminId=admin_id,
                    email="<EMAIL>",  # From token
                    fullName="Admin User",
                    roles=[],  # Empty roles list
                    createdAt="2025-07-14T00:00:00Z",
                    updatedAt="2025-07-14T00:00:00Z"
                )
                return admin_pb2.AdminResponse(success=False, message="Admin not found", admin=admin_info)
            raise self._handle_error(e)

    async def update_admin(
        self,
        admin_id: str,
        full_name: Optional[str] = None,
        email: Optional[str] = None,
        password: Optional[str] = None,
        role_ids: Optional[list[str]] = None,
    ):
        request = admin_pb2.UpdateAdminRequest(
            adminId=admin_id,
            fullName=full_name,
            email=email,
            password=password,
            roleIds=role_ids or [],
        )
        try:
            response = self.stub.updateAdmin(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_admin(self, admin_id: str):
        request = admin_pb2.DeleteAdminRequest(adminId=admin_id)
        try:
            response = self.stub.deleteAdmin(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_admins(self, page: int = 1, page_size: int = 10):
        request = admin_pb2.ListAdminsRequest(page=page, pageSize=page_size)
        try:
            response = self.stub.listAdmins(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def create_role(self, name: str, description: str, permissions: list[str]):
        request = admin_pb2.CreateRoleRequest(
            name=name, description=description, permissions=permissions
        )
        try:
            response = self.stub.createRole(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_role(self, role_id: str):
        request = admin_pb2.GetRoleRequest(roleId=role_id)
        try:
            response = self.stub.getRole(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_role(
        self,
        role_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        permissions: Optional[list[str]] = None,
    ):
        request = admin_pb2.UpdateRoleRequest(
            roleId=role_id, name=name, description=description, permissions=permissions or []
        )
        try:
            response = self.stub.updateRole(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_role(self, role_id: str):
        request = admin_pb2.DeleteRoleRequest(roleId=role_id)
        try:
            response = self.stub.deleteRole(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_roles(self, page: int = 1, page_size: int = 10):
        request = admin_pb2.ListRolesRequest(page=page, pageSize=page_size)
        try:
            response = self.stub.listRoles(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def assign_role(self, admin_id: str, role_ids: list[str]):
        request = admin_pb2.AssignRoleRequest(adminId=admin_id, roleIds=role_ids)
        try:
            response = self.stub.assignRole(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_all_organisations(
            self, 
            page: int = 1, 
            page_size: int = 10,
            industry: Optional[str] = None,
            plan_type: Optional[str] = None,
            search: Optional[str] = None,
        ):
        """
        Get all organisations with limited data and pagination support.
        Returns only specific fields: id, name, description, logo, industry,
        created_at, status, total_departments, total_users, type,
        total_payments, total_rcu, rcu_used
        """
        request = admin_pb2.GetAllOrganisationsRequest(page=page, page_size=page_size,industry=industry,plan_type = plan_type,search = search)
        try:
            response = self.stub.getAllOrganisations(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_organisations_active_users(
        self,
        organisation_id: str,
        department_id: Optional[str] = None,
        page: int = 1,
        page_size: int = 10
    ):
        """
        Get active users belonging to a specific organization, with optional department filtering and pagination.
        Returns users list with metadata including their departments.
        """
        request = admin_pb2.GetOrganisationActiveUsersRequest(
            organisation_id=organisation_id,
            department_id=department_id,
            page_number=page,
            page_size=page_size
        )
        try:
            response = self.stub.getActiveUsersInOrganisation(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
        
    async def get_organisation_details(self, organisation_id: str):
        """
        Get complete organisation details by organisation ID.
        Returns all organisation data including admin, departments, and statistics.
        """
        request = admin_pb2.GetOrganisationDetailsRequest(organisation_id=organisation_id)
        try:
            response = self.stub.getOrganisationDetails(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
        
    async def list_sources(self, organisation_id: str):
        """
        List all sources for an organisation.
        
        Args:
            organisation_id: ID of the organisation to list sources for
            
        Returns:
            The gRPC response containing the list of sources
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = admin_pb2.ListSourcesRequest(
            organisation_id=organisation_id
        )
        
        try:
            response = self.stub.listSources(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def get_all_agents_from_organisation(
        self,
        organisation_id: str,
        page: int = 1,
        page_size: int = 12
    ) -> admin_pb2.ListAgentsResponse:
        """
        Get all agents from all departments of an organization with pagination support.

        Args:
            organisation_id (str): The ID of the organization
            page (int): Page number (default: 1)
            page_size (int): Number of agents per page (default: 12)

        Returns:
            admin_pb2.ListAgentsResponse: Response containing paginated list of agents

        Raises:
            HTTPException: If the gRPC call fails
        """
        try:
            request = admin_pb2.GetAllAgentsFromOrganisationRequest(
                organisation_id=organisation_id,
                page=page,
                page_size=page_size
            )
            response = self.stub.getAllAgentsFromOrganisation(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in get_all_agents_from_organisation: {e}")
            self._handle_error(e)
        except Exception as e:
            print(f"[ERROR] Unexpected error in get_all_agents_from_organisation: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")
        
    async def get_all_users(
        self,
        page: int = 1,
        page_size: int = 12,
        company:Optional[str] = None,
        department: Optional[str] = None,
        role: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_email_verified: Optional[bool] = None,
        search: Optional[str] = None
    ):
        """
        Get all users with filtering and pagination support.
        Returns users list with metadata.
        """
        request = admin_pb2.GetUsersRequest(
            page=page,
            page_size=page_size,
            company=company,
            department=department,
            role=role,
            is_active=is_active,
            is_email_verified=is_email_verified,
            search=search
        )
        try:
            response = self.stub.getAllUsers(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_all_agents(
        self,
        page: int = 1,
        page_size: int = 12,
        visibility: Optional[str] = None,
        agent_category: Optional[str] = None,
        department: Optional[str] = None,
        is_bench_employee: Optional[bool] = None,
        search: Optional[str] = None
    ):
        """
        Get all agents with filtering and pagination support.
        Returns agents list with metadata.
        """
        request = admin_pb2.GetAgentsRequest(
            page=page,
            page_size=page_size,
            visibility=visibility,
            agent_category=agent_category,
            department=department,
            is_bench_employee=is_bench_employee,
            search=search
        )
        try:
            response = self.stub.getAllAgents(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_all_workflows(
        self,
        page: int = 1,
        page_size: int = 10,
        visibility: Optional[str] = None,
        status: Optional[str] = None,
        category: Optional[str] = None,
        search: Optional[str] = None,
        tags: Optional[str] = None
    ) -> admin_pb2.GetWorkflowsResponse:
        """Get all workflows with filtering and pagination support.
        Returns workflows list with metadata."""
        
        try:
            request = admin_pb2.GetWorkflowsRequest(
                page=page,
                page_size=page_size,
                visibility=visibility,
                status=status,
                category=category,
                search=search,
                tags=tags
            )
            response = self.stub.getAllWorkflows(request)
            return response
            
        except grpc.RpcError as e:
            print(f"gRPC error in get_all_workflows: {e.code()}, {e.details()}")
            if e.code() == grpc.StatusCode.INTERNAL:
                from fastapi import HTTPException
                raise HTTPException(
                    status_code=500,
                    detail=f"Admin service error: {e.details()}"
                )
            raise self._handle_error(e)
            
        except Exception as e:
            print(f"Unexpected error in get_all_workflows: {str(e)}")
            from fastapi import HTTPException
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error: {str(e)}"
            )

    def _handle_error(self, e: grpc.RpcError):
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            from fastapi import HTTPException

            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            from fastapi import HTTPException

            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            from fastapi import HTTPException

            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            from fastapi import HTTPException

            raise HTTPException(status_code=403, detail=details)
        else:
            from fastapi import HTTPException

            raise HTTPException(status_code=500, detail="Internal server error")

