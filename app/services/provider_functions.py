import grpc
import structlog
import requests
from datetime import datetime
from typing import Optional, Dict, List, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, func
from app.db.session import get_db
from app.models.provider import Provider, Model
from app.grpc import provider_pb2

logger = structlog.get_logger()


class ProviderFunctionsService:
    """Service for managing provider CRUD operations."""

    def __init__(self):
        pass

    def _get_db_session(self) -> Session:
        """Get database session."""
        return next(get_db())

    def _provider_to_protobuf(self, provider: Provider, model_count: int = 0) -> provider_pb2.ProviderInfo:
        """Convert Provider model to protobuf ProviderInfo."""
        return provider_pb2.ProviderInfo(
            id=provider.id,
            provider=provider.provider,
            description=provider.description or "",
            baseUrl=provider.base_url,
            isActive=provider.is_active,
            isDefault=provider.is_default,
            createdAt=provider.created_at.isoformat(),
            updatedAt=provider.updated_at.isoformat(),
            modelCount=model_count,
            platform=provider.platform
        )

    def _ensure_single_default(self, db: Session, exclude_id: Optional[str] = None):
        """Ensure only one provider is marked as default."""
        query = db.query(Provider).filter(Provider.is_default == True)
        if exclude_id:
            query = query.filter(Provider.id != exclude_id)

        for provider in query.all():
            provider.is_default = False
        db.commit()

    def createProvider(self, request: provider_pb2.CreateProviderRequest, context: grpc.ServicerContext) -> provider_pb2.ProviderResponse:
        """Create a new provider."""
        try:
            db = self._get_db_session()

            # Check if provider name already exists
            existing_provider = db.query(Provider).filter(Provider.provider == request.provider).first()
            if existing_provider:
                return provider_pb2.ProviderResponse(
                    success=False,
                    message=f"Provider with name '{request.provider}' already exists"
                )

            # If this provider should be default, ensure no other provider is default
            if request.isDefault:
                self._ensure_single_default(db)

            # Create new provider
            new_provider = Provider(
                provider=request.provider,
                description=request.description if request.HasField('description') else None,
                base_url=request.baseUrl,
                is_active=request.isActive if request.HasField('isActive') else True,
                is_default=request.isDefault if request.HasField('isDefault') else False,
                platform=request.platform,
            )

            db.add(new_provider)
            db.commit()
            db.refresh(new_provider)

            logger.info(f"Provider created successfully: {new_provider.id}")
            return provider_pb2.ProviderResponse(
                success=True,
                message="Provider created successfully",
                provider=self._provider_to_protobuf(new_provider)
            )

        except Exception as e:
            logger.error(f"Error creating provider: {str(e)}")
            db.rollback()
            return provider_pb2.ProviderResponse(
                success=False,
                message=f"Failed to create provider: {str(e)}"
            )
        finally:
            db.close()

    def getProvider(self, request: provider_pb2.GetByIdRequest, context: grpc.ServicerContext) -> provider_pb2.ProviderResponse:
        """Get provider by ID."""
        try:
            db = self._get_db_session()

            provider = db.query(Provider).filter(Provider.id == request.id).first()
            if not provider:
                return provider_pb2.ProviderResponse(
                    success=False,
                    message=f"Provider with ID '{request.id}' not found"
                )

            # Get model count for this provider
            model_count = db.query(func.count(Model.id)).filter(Model.provider_id == provider.id).scalar()

            return provider_pb2.ProviderResponse(
                success=True,
                message="Provider retrieved successfully",
                provider=self._provider_to_protobuf(provider, model_count)
            )

        except Exception as e:
            logger.error(f"Error getting provider: {str(e)}")
            return provider_pb2.ProviderResponse(
                success=False,
                message=f"Failed to get provider: {str(e)}"
            )
        finally:
            db.close()

    def updateProvider(self, request: provider_pb2.UpdateProviderRequest, context: grpc.ServicerContext) -> provider_pb2.ProviderResponse:
        """Update an existing provider."""
        try:
            db = self._get_db_session()

            provider = db.query(Provider).filter(Provider.id == request.id).first()
            if not provider:
                return provider_pb2.ProviderResponse(
                    success=False,
                    message=f"Provider with ID '{request.id}' not found"
                )

            # Check if provider name already exists (excluding current provider)
            if request.HasField('provider'):
                existing_provider = db.query(Provider).filter(
                    and_(Provider.provider == request.provider, Provider.id != request.id)
                ).first()
                if existing_provider:
                    return provider_pb2.ProviderResponse(
                        success=False,
                        message=f"Provider with name '{request.provider}' already exists"
                    )

            # If this provider should be default, ensure no other provider is default
            if request.HasField('isDefault') and request.isDefault:
                self._ensure_single_default(db, exclude_id=request.id)

            # Update fields
            if request.HasField('provider'):
                provider.provider = request.provider
            if request.HasField('description'):
                provider.description = request.description
            if request.HasField('baseUrl'):
                provider.base_url = request.baseUrl
            if request.HasField('isActive'):
                provider.is_active = request.isActive
            if request.HasField('isDefault'):
                provider.is_default = request.isDefault
            if request.HasField('platform'):
                provider.platform = request.platform

            provider.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(provider)

            # Get model count for this provider
            model_count = db.query(func.count(Model.id)).filter(Model.provider_id == provider.id).scalar()

            logger.info(f"Provider updated successfully: {provider.id}")
            return provider_pb2.ProviderResponse(
                success=True,
                message="Provider updated successfully",
                provider=self._provider_to_protobuf(provider, model_count)
            )

        except Exception as e:
            logger.error(f"Error updating provider: {str(e)}")
            db.rollback()
            return provider_pb2.ProviderResponse(
                success=False,
                message=f"Failed to update provider: {str(e)}"
            )
        finally:
            db.close()

    def deleteProvider(self, request: provider_pb2.DeleteRequest, context: grpc.ServicerContext) -> provider_pb2.DeleteResponse:
        """Delete a provider."""
        try:
            db = self._get_db_session()

            provider = db.query(Provider).filter(Provider.id == request.id).first()
            if not provider:
                return provider_pb2.DeleteResponse(
                    success=False,
                    message=f"Provider with ID '{request.id}' not found"
                )

            # Check if provider has associated models
            model_count = db.query(func.count(Model.id)).filter(Model.provider_id == provider.id).scalar()
            if model_count > 0:
                return provider_pb2.DeleteResponse(
                    success=False,
                    message=f"Cannot delete provider. It has {model_count} associated models. Delete models first."
                )

            db.delete(provider)
            db.commit()

            logger.info(f"Provider deleted successfully: {request.id}")
            return provider_pb2.DeleteResponse(
                success=True,
                message="Provider deleted successfully"
            )

        except Exception as e:
            logger.error(f"Error deleting provider: {str(e)}")
            db.rollback()
            return provider_pb2.DeleteResponse(
                success=False,
                message=f"Failed to delete provider: {str(e)}"
            )
        finally:
            db.close()

    def listProviders(self, request: provider_pb2.ListRequest, context: grpc.ServicerContext) -> provider_pb2.ListProvidersResponse:
        """List providers with pagination."""
        try:
            print("Listing providers with request:", request)
            db = self._get_db_session()

            # Build query
            query = db.query(Provider)

            # Filter by active status if specified
            if request.HasField('isActive'):
                query = query.filter(Provider.is_active == request.isActive)

            # Filter by platform if specified
            if request.HasField('platform'):
                query = query.filter(Provider.platform == request.platform)

            # Get total count
            total_count = query.count()

            # Apply pagination
            page = max(1, request.page) if request.page > 0 else 1
            page_size = min(100, max(1, request.pageSize)) if request.pageSize > 0 else 10
            offset = (page - 1) * page_size

            providers = query.offset(offset).limit(page_size).all()

            # Get model counts for each provider
            provider_infos = []
            for provider in providers:
                model_count = db.query(func.count(Model.id)).filter(Model.provider_id == provider.id).scalar()
                provider_infos.append(self._provider_to_protobuf(provider, model_count))

            # Calculate pagination info
            total_pages = (total_count + page_size - 1) // page_size

            pagination = provider_pb2.PaginationInfo(
                currentPage=page,
                totalPages=total_pages,
                totalItems=total_count,
                pageSize=page_size
            )

            return provider_pb2.ListProvidersResponse(
                success=True,
                message="Providers retrieved successfully",
                providers=provider_infos,
                pagination=pagination
            )

        except Exception as e:
            logger.error(f"Error listing providers: {str(e)}")
            return provider_pb2.ListProvidersResponse(
                success=False,
                message=f"Failed to list providers: {str(e)}",
                providers=[],
                pagination=provider_pb2.PaginationInfo()
            )
        finally:
            db.close()

    def syncModels(
        self, request: provider_pb2.SyncModelsRequest, context: grpc.ServicerContext
    ) -> provider_pb2.SyncModelsResponse:
        """Sync models from the external API with the database."""
        try:
            db = self._get_db_session()

            # Initialize stats
            stats = {
                "providers_added": 0,
                "providers_updated": 0,
                "providers_removed": 0,
                "models_added": 0,
                "models_updated": 0,
                "models_removed": 0,
                "total_processed": 0,
            }

            # Fetch data from the external API
            api_url = "https://api.requesty.ai/router/models"
            logger.info(f"Fetching models from API: {api_url}")

            try:
                response = requests.get(api_url, timeout=30)
                response.raise_for_status()
                api_data = response.json()
                logger.info(f"Successfully fetched {len(api_data)} models from API")
            except requests.RequestException as e:
                logger.error(f"Failed to fetch data from API: {str(e)}")
                return provider_pb2.SyncModelsResponse(
                    success=False,
                    message=f"Failed to fetch data from API: {str(e)}",
                    stats=provider_pb2.SyncStats(),
                )

            # Parse API data
            api_providers = {}
            api_models = {}
            for item in api_data:
                provider_name = item.get("provider")
                model_name = item.get("model")

                if not provider_name or not model_name:
                    continue

                provider_data = api_providers.setdefault(
                    provider_name,
                    {
                        "description": f"{provider_name.capitalize()} is an integrated model provider using Requestly router.",
                        "base_url": "https://router.requesty.ai/v1",
                        "models": [],
                    },
                )

                model_data = {
                    "model": model_name,
                    "model_id": model_name,
                    "description": item.get(
                        "description", f"{provider_name} provides model {model_name}."
                    ),
                    "input_price_per_token": self._safe_float_conversion(
                        item.get("input_tokens_price_per_million")
                    ),
                    "output_price_per_token": self._safe_float_conversion(
                        item.get("output_tokens_price_per_million")
                    ),
                    "max_tokens": item.get("max_output_tokens"),
                    "context_window": item.get("context_window"),
                    "provider_type": "chat",
                }

                provider_data["models"].append(model_data)
                api_models[(provider_name, model_name)] = model_data

            stats["total_processed"] = len(api_models)

            # Load existing providers/models from DB
            existing_providers = {p.provider: p for p in db.query(Provider).all()}
            existing_models = {}
            model_rows = (
                db.query(Model).join(Provider).with_entities(Model, Provider.provider)
            ).all()

            for model, provider_name in model_rows:
                existing_models[(provider_name, model.model)] = model

            # Sync providers
            for provider_name, pdata in api_providers.items():
                if provider_name in existing_providers:
                    provider = existing_providers[provider_name]
                    updated = False
                    if provider.description != pdata["description"]:
                        provider.description = pdata["description"]
                        updated = True
                    if provider.base_url != pdata["base_url"]:
                        provider.base_url = pdata["base_url"]
                        updated = True
                    if updated:
                        provider.updated_at = datetime.utcnow()
                        stats["providers_updated"] += 1
                        logger.info(f"Updated provider: {provider_name}")
                else:
                    new_provider = Provider(
                        provider=provider_name,
                        description=pdata["description"],
                        base_url=pdata["base_url"],
                        is_active=True,
                        is_default=False,
                        platform="requesty",  # Default to requesty for synced providers
                    )
                    db.add(new_provider)
                    db.flush()  # get ID
                    existing_providers[provider_name] = new_provider
                    stats["providers_added"] += 1
                    logger.info(f"Added new provider: {provider_name}")

            # Sync models
            for (provider_name, model_name), mdata in api_models.items():
                provider = existing_providers[provider_name]
                if (provider_name, model_name) in existing_models:
                    model = existing_models[(provider_name, model_name)]
                    updated = False
                    if model.description != mdata["description"]:
                        model.description = mdata["description"]
                        updated = True
                    if model.input_price_per_token != mdata["input_price_per_token"]:
                        model.input_price_per_token = mdata["input_price_per_token"]
                        updated = True
                    if model.output_price_per_token != mdata["output_price_per_token"]:
                        model.output_price_per_token = mdata["output_price_per_token"]
                        updated = True
                    if model.max_tokens != mdata["max_tokens"]:
                        model.max_tokens = mdata["max_tokens"]
                        updated = True
                    if model.context_window != mdata["context_window"]:
                        model.context_window = mdata["context_window"]
                        updated = True
                    if updated:
                        model.updated_at = datetime.utcnow()
                        stats["models_updated"] += 1
                        logger.info(f"Updated model: {provider_name}:{model_name}")
                else:
                    new_model = Model(
                        provider_id=provider.id,
                        model=mdata["model"],
                        model_id=mdata["model_id"],
                        description=mdata["description"],
                        input_price_per_token=mdata["input_price_per_token"],
                        output_price_per_token=mdata["output_price_per_token"],
                        max_tokens=mdata["max_tokens"],
                        context_window=mdata["context_window"],
                        temperature=0.7,
                        provider_type=mdata["provider_type"],
                        is_active=True,
                        is_default=False,
                    )
                    db.add(new_model)
                    stats["models_added"] += 1
                    logger.info(f"Added new model: {provider_name}:{model_name}")

            # Remove stale models
            for key, model in existing_models.items():
                if key not in api_models:
                    db.delete(model)
                    stats["models_removed"] += 1
                    logger.info(f"Removed model: {key[0]}:{key[1]}")

            # Remove stale providers (only if they have no models)
            for provider_name, provider in existing_providers.items():
                if provider_name not in api_providers:
                    model_count = (
                        db.query(func.count(Model.id)).filter_by(provider_id=provider.id).scalar()
                    )
                    if model_count == 0:
                        db.delete(provider)
                        stats["providers_removed"] += 1
                        logger.info(f"Removed provider: {provider_name}")

            # Commit all changes
            db.commit()

            # Create response
            sync_stats = provider_pb2.SyncStats(
                providersAdded=stats["providers_added"],
                providersUpdated=stats["providers_updated"],
                providersRemoved=stats["providers_removed"],
                modelsAdded=stats["models_added"],
                modelsUpdated=stats["models_updated"],
                modelsRemoved=stats["models_removed"],
                totalProcessed=stats["total_processed"],
            )

            message = "Sync completed successfully"

            logger.info(message)
            return provider_pb2.SyncModelsResponse(success=True, message=message, stats=sync_stats)

        except Exception as e:
            logger.error(f"Error during sync: {str(e)}")
            db.rollback()
            return provider_pb2.SyncModelsResponse(
                success=False, message=f"Sync failed: {str(e)}", stats=provider_pb2.SyncStats()
            )
        finally:
            db.close()

    def _safe_float_conversion(self, value: Any) -> Optional[float]:
        """Safely convert a value to float, handling per-million pricing."""
        if value is None:
            return None
        try:
            # Convert from per-million to per-token
            return float(value) / 1_000_000
        except (ValueError, TypeError):
            return None
