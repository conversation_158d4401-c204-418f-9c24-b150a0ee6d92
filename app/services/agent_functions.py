# app/services/agent_service.py
import json
from datetime import datetime, timezone

import grpc
import requests  # Added for GCS file size
import structlog
from sqlalchemy import desc
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.grpc import agent_pb2, agent_pb2_grpc
from app.helpers.agent_helpers import _create_marketplace_listing_from_agent
from app.models.agent import (
    AgentCapabilities,
    AgentConfig,
    AgentConfigVersion,
    AgentKnowledgeBase,
    AgentMarketplaceListing,
    AgentModelConfig,
    AgentVariables,
)
from app.models.agent_rating import AgentRating
from app.utils.constants.constants import (
    AgentCategoryEnum,
    AgentStatusEnum,
    AgentToneEnum,
    AgentVisibilityEnum,
    CategoryEnum,
)
from app.utils.helpers.agent_to_protobuf import _agent_to_protobuf, _agents_to_protobuf
from app.utils.kafka.kafka_service import KafkaProducer

logger = structlog.get_logger()


class AgentFunctionsService(agent_pb2_grpc.AgentServiceServicer):
    """
    Service for managing agent configurations.

    This service handles CRUD operations for agent configurations, including
    validation, storage in Google Cloud Storage, and database operations.
    """

    def _get_gcs_file_size(self, gcs_url: str) -> int:
        """
        Retrieves the size of a file from a GCS URL using a HEAD request.
        Returns 0 if the size cannot be determined or on error.
        """
        try:
            # For GCS URLs, a direct HEAD request might work if public or signed URL.
            # If it's a private GCS URL, this will require authentication.
            # Assuming public or pre-signed URLs for simplicity here.
            response = requests.head(gcs_url, allow_redirects=True, timeout=5)
            response.raise_for_status()  # Raise an exception for HTTP errors

            content_length = response.headers.get("Content-Length")
            if content_length:
                return int(content_length)
            return 0
        except requests.exceptions.RequestException as e:
            logger.warning(f"Could not get size for GCS URL {gcs_url}: {e}")
            return 0
        except ValueError:  # For int conversion error
            logger.warning(f"Invalid Content-Length header for GCS URL {gcs_url}")
            return 0

    def __init__(self):
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        """
        Creates and returns a new database session.

        Returns:
            Session: SQLAlchemy database session
        """
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def createAgent(
        self, request: agent_pb2.CreateAgentRequest, context: grpc.ServicerContext
    ) -> agent_pb2.CreateAgentResponse:
        """
        Creates a new agent configuration.

        This method handles the creation of a new agent configuration, including:
        - Validating the user ID
        - Validating the workflow JSON schema
        - Uploading the workflow to Google Cloud Storage
        - Storing the configuration in the database

        Args:
            request (agent_pb2.CreateAgentRequest): The create agent request
            context (grpc.ServicerContext): The gRPC service context

        Returns:
            agent_pb2.AgentResponse: Response containing the created agent details
        """
        db = self.get_db()
        logger.info("create_agent_request", name=request.name)
        logger.info("create_agent_request", owner_id=request.tone)

        final_capabilities_id = None

        # Check if agent_capabilities is provided (and not empty)
        if request.agent_capabilities:
            cap_data = request.agent_capabilities
            logger.info("Creating new agent capabilities from agent_capabilities.")
            new_capabilities = AgentCapabilities(
                capabilities=(
                    json.loads(cap_data.capabilities_json) if cap_data.capabilities_json else None
                ),
                input_modes=list(cap_data.input_modes) if cap_data.input_modes else None,
                output_modes=list(cap_data.output_modes) if cap_data.output_modes else None,
                response_model=list(cap_data.response_model) if cap_data.response_model else None,
            )
            db.add(new_capabilities)
            db.flush()  # To get the ID before creating AgentConfig
            final_capabilities_id = new_capabilities.id
            logger.info(f"New AgentCapabilities created with ID: {final_capabilities_id}")

        try:
            # Create new agent (without legacy model and knowledge fields)
            new_agent = AgentConfig(
                name=request.name,
                description=request.description,
                avatar=request.avatar,
                owner_id=request.owner.id,
                # Access Control
                user_ids=[request.owner.id],
                owner_type=agent_pb2.OwnerType.Name(request.owner_type).lower(),
                # Template Reference (now marketplace listing reference)
                agent_template_id=None,  # Set to None for new agents
                template_owner_id=None,  # Set to None for new agents
                is_imported=False,  # Default to False for new agents
                # Basic Configuration
                agent_category=AgentCategoryEnum.AI_AGENT.value,
                system_message=request.system_message,
                # Tools Configuration
                workflow_ids=request.workflow_ids if request.workflow_ids else None,
                mcp_server_ids=request.mcp_server_ids if request.mcp_server_ids else None,
                # New fields
                department=request.department,
                organization_id=request.organization_id,
                tone=agent_pb2.Tone.Name(request.tone).lower(),
                # Messaging Configuration
                agent_topic_type=request.agent_topic_type,
                visibility=agent_pb2.Visibility.Name(request.visibility).lower(),
                tags=list(request.tags) if request.tags else None,
                status=agent_pb2.Status.Name(request.status).lower(),
                is_a2a=request.is_a2a,
                is_customizable=request.is_customizable,
                capabilities_id=final_capabilities_id,
                example_prompts=list(request.example_prompts) if request.example_prompts else None,
                category=(
                    agent_pb2.Category.Name(request.category).lower()
                    if request.category
                    else CategoryEnum.GENERAL.value
                ),
            )

            # --- NEW: Create and associate AgentVariables from request.variables ---
            if request.variables:  # request.variables is the 'repeated AgentVariableData'
                # logger.info(f"Processing {len(request.variables)} variables for agent '{request.name}'.")
                for var_proto_data in request.variables:
                    # Convert proto VariableType enum (int) to Python VariableTypeEnum (str value)
                    var_type_name_for_py_enum = var_proto_data.type

                    db_variable = AgentVariables(
                        name=var_proto_data.name,
                        description=(
                            var_proto_data.description
                            if var_proto_data.HasField("description")
                            else None
                        ),
                        type=var_type_name_for_py_enum,  # Use Python Enum member
                        default_value=(
                            var_proto_data.default_value
                            if var_proto_data.HasField("default_value")
                            else None
                        ),
                        # agent_config_id will be set by SQLAlchemy relationship append
                    )
                    new_agent.variables.append(db_variable)

            db.add(new_agent)
            db.flush()  # Get the agent ID before creating version

            # Create the first version (v1.0.0) automatically
            try:
                # Handle model config for version
                model_config_id = None
                if (
                    request.model_provider
                    or request.model_name
                    or request.temperature
                    or request.max_tokens
                ):
                    model_config = AgentModelConfig(
                        model_provider=request.model_provider if request.model_provider else None,
                        model_name=request.model_name if request.model_name else None,
                        temperature=request.temperature if request.temperature else None,
                        max_tokens=request.max_tokens if request.max_tokens else None,
                    )
                    db.add(model_config)
                    db.flush()
                    model_config_id = model_config.id

                # Handle knowledge base for version
                knowledge_base_id = None
                if request.files or request.urls:
                    file_entries_data = []
                    for file_url in request.files:
                        file_size = self._get_gcs_file_size(file_url)
                        file_entries_data.append(
                            {
                                "file": file_url,
                                "created_at": datetime.now(timezone.utc).isoformat(),
                                "size": file_size,
                            }
                        )

                    url_entries_data = []
                    for url_str in request.urls:
                        url_entries_data.append(
                            {"url": url_str, "created_at": datetime.now(timezone.utc).isoformat()}
                        )

                    knowledge_base = AgentKnowledgeBase(
                        files=file_entries_data,
                        urls=url_entries_data,
                    )
                    db.add(knowledge_base)
                    db.flush()
                    knowledge_base_id = knowledge_base.id

                # Create the initial version
                initial_version = AgentConfigVersion(
                    agent_config_id=new_agent.id,
                    model_config_id=model_config_id,
                    knowledge_base_id=knowledge_base_id,
                    version_number="1.0.0",
                    name=new_agent.name,
                    description=new_agent.description,
                    avatar=new_agent.avatar,
                    agent_category=new_agent.agent_category,
                    system_message=new_agent.system_message,
                    workflow_ids=new_agent.workflow_ids if new_agent.workflow_ids else [],
                    mcp_server_ids=new_agent.mcp_server_ids if new_agent.mcp_server_ids else [],
                    agent_topic_type=new_agent.agent_topic_type,
                    department=new_agent.department,
                    organization_id=new_agent.organization_id,
                    tone=new_agent.tone,
                    is_bench_employee=new_agent.is_bench_employee,
                    is_changes_marketplace=new_agent.is_changes_marketplace,
                    is_a2a=new_agent.is_a2a,
                    is_customizable=new_agent.is_customizable,
                    capabilities_id=new_agent.capabilities_id,
                    example_prompts=new_agent.example_prompts if new_agent.example_prompts else [],
                    category=new_agent.category,
                    tags=new_agent.tags if new_agent.tags else [],
                    status=new_agent.status,
                    version_notes="Initial version",
                )

                db.add(initial_version)
                db.flush()  # Get the version ID

                # Set the current version ID on the agent
                new_agent.current_version_id = initial_version.id
                db.add(new_agent)

                logger.info(f"Created initial version 1.0.0 for agent {new_agent.id}")

            except Exception as version_error:
                logger.error(
                    f"Failed to create initial version for agent {new_agent.id}: {str(version_error)}"
                )
                # Continue without failing the agent creation
                pass

            db.commit()
            db.refresh(new_agent)

            # print(f"[AGENT TO PROTOBUF] {_agent_to_protobuf(db=db, agent=new_agent)}")
            return agent_pb2.CreateAgentResponse(
                success=True,
                message=f"Agent {request.name} created successfully",
                agent=_agent_to_protobuf(db=db, agent=new_agent),
            )

        except Exception as e:
            db.rollback()
            print(f"[ERROR] {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.CreateAgentResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def getAgent(
        self, request: agent_pb2.GetAgentRequest, context: grpc.ServicerContext
    ) -> agent_pb2.AgentResponse:
        """
        Retrieves an agent configuration by ID.
        """
        db = self.get_db()
        logger.info("get_agent_request", agent_id=request.id)
        try:
            agent_proto = _agents_to_protobuf(db, request.id)

            if agent_proto is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.id} not found")
                return agent_pb2.AgentResponse(
                    success=False, message=f"Agent with ID {request.id} not found"
                )

            return agent_pb2.AgentResponse(
                success=True,
                message=f"Agent {agent_proto.name} retrieved successfully",
                agent=agent_proto,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.AgentResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def UpdateAgentCoreDetails(
        self, request: agent_pb2.UpdateAgentCoreDetailsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        db = self.get_db()
        try:
            print(f"[UPDATE AGENT CORE DETAILS REQUEST] {request}")
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.UpdateAgentPartResponse(success=False, message="Agent not found.")

            if agent.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied to update this agent.")
                return agent_pb2.UpdateAgentPartResponse(
                    success=False, message="Permission denied."
                )

            fields_changed = False
            updated_field_names = []

            for path in request.update_mask.paths:
                updated_field_names.append(path)
                if path == "name" and agent.name != request.name:
                    agent.name = request.name
                    fields_changed = True
                elif path == "description" and agent.description != request.description:
                    agent.description = request.description
                    fields_changed = True
                elif path == "avatar" and agent.avatar != request.avatar:
                    if request.avatar == "":
                        agent.avatar = None
                    else:
                        agent.avatar = request.avatar
                    fields_changed = True
                elif path == "system_message" and agent.system_message != request.system_message:
                    agent.system_message = request.system_message
                    fields_changed = True
                elif (
                    path == "model_provider"
                    or path == "model_name"
                    or path == "temperature"
                    or path == "max_tokens"
                ):
                    # Update model config in current version instead of agent directly
                    if agent.current_version_id:
                        current_version = (
                            db.query(AgentConfigVersion)
                            .filter(AgentConfigVersion.id == agent.current_version_id)
                            .first()
                        )
                        if current_version and current_version.model_config_id:
                            model_config = (
                                db.query(AgentModelConfig)
                                .filter(AgentModelConfig.id == current_version.model_config_id)
                                .first()
                            )
                            if model_config:
                                if path == "model_provider":
                                    model_config.model_provider = (
                                        request.model_provider
                                        if request.model_provider != ""
                                        else None
                                    )
                                elif path == "model_name":
                                    model_config.model_name = (
                                        request.model_name if request.model_name != "" else None
                                    )
                                elif path == "temperature":
                                    model_config.temperature = (
                                        request.temperature if request.temperature != 0.0 else None
                                    )
                                elif path == "max_tokens":
                                    model_config.max_tokens = (
                                        request.max_tokens if request.max_tokens != 0 else None
                                    )
                                db.add(model_config)
                                fields_changed = True
                        elif current_version:
                            # Create new model config if it doesn't exist
                            model_config = AgentModelConfig(
                                model_provider=(
                                    request.model_provider
                                    if path == "model_provider" and request.model_provider != ""
                                    else None
                                ),
                                model_name=(
                                    request.model_name
                                    if path == "model_name" and request.model_name != ""
                                    else None
                                ),
                                temperature=(
                                    request.temperature
                                    if path == "temperature" and request.temperature != 0.0
                                    else None
                                ),
                                max_tokens=(
                                    request.max_tokens
                                    if path == "max_tokens" and request.max_tokens != 0
                                    else None
                                ),
                            )
                            db.add(model_config)
                            db.flush()
                            current_version.model_config_id = model_config.id
                            db.add(current_version)
                            fields_changed = True
                elif path == "organization_id":
                    agent.organization_id = request.organization_id
                    fields_changed = True
                elif path == "department":
                    agent.department = request.department
                    fields_changed = True
                elif path == "tone":
                    # print(f"[DEBUG] tone: {agent_pb2.Tone.Name(request.tone).lower()}")
                    tone_str = agent_pb2.Tone.Name(request.tone).lower()
                    # Ensure it matches one of the enum values
                    if tone_str in [e.value for e in AgentToneEnum]:
                        agent.tone = tone_str
                        fields_changed = True
                        print(f"[DEBUG] Updated tone to: {agent.tone}")
                    else:
                        print(f"[WARNING] Invalid tone value: {tone_str}")
                    fields_changed = True
                elif (
                    path == "agent_topic_type"
                    and agent.agent_topic_type != request.agent_topic_type
                ):
                    agent.agent_topic_type = request.agent_topic_type
                    fields_changed = True
                elif path == "category" and agent.category != request.category:
                    agent.category = request.category
                    fields_changed = True
            logger.info(f"Attempting to update fields: {updated_field_names} for agent {agent.id}")

            # Mark agent as updated if any fields changed
            if fields_changed:
                agent.is_updated = True
                logger.info(f"Agent {agent.id} marked as updated due to field changes")

            db.add(agent)
            db.commit()
            db.refresh(agent)

            return agent_pb2.UpdateAgentPartResponse(
                success=True,
                message="Agent core details updated successfully.",
                agent=_agent_to_protobuf(db=db, agent=agent),
            )
        except Exception as e:
            logger.error(
                f"Error updating agent core details {request.agent_id}: {e}", exc_info=True
            )
            if db.is_active:
                db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.UpdateAgentPartResponse(
                success=False, message="Internal server error."
            )
        finally:
            db.close()

    def UpdateAgentKnowledge(
        self, request: agent_pb2.UpdateAgentKnowledgeRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        db = self.get_db()
        try:
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:  # ... (Not Found, Permission Denied checks) ...
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.UpdateAgentPartResponse(success=False, message="Agent not found.")
            if agent.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied.")
                return agent_pb2.UpdateAgentPartResponse(
                    success=False, message="Permission denied."
                )

            fields_changed = False
            for path in request.update_mask.paths:
                if path == "files" or path == "urls":
                    # Update knowledge base in current version instead of agent directly
                    if agent.current_version_id:
                        current_version = (
                            db.query(AgentConfigVersion)
                            .filter(AgentConfigVersion.id == agent.current_version_id)
                            .first()
                        )
                        if current_version and current_version.knowledge_base_id:
                            knowledge_base = (
                                db.query(AgentKnowledgeBase)
                                .filter(AgentKnowledgeBase.id == current_version.knowledge_base_id)
                                .first()
                            )
                            if knowledge_base:
                                # Process files
                                # Process files
                                new_files_data = []
                                for file_url in request.files:
                                    file_size = self._get_gcs_file_size(file_url)
                                    new_files_data.append(
                                        {
                                            "file": file_url,
                                            "created_at": datetime.now(timezone.utc).isoformat(),
                                            "size": file_size,
                                        }
                                    )
                                if knowledge_base.files != new_files_data:
                                    knowledge_base.files = new_files_data
                                    fields_changed = True

                                # Process URLs
                                new_urls_data = []
                                for url_str in request.urls:
                                    new_urls_data.append(
                                        {
                                            "url": url_str,
                                            "created_at": datetime.now(timezone.utc).isoformat(),
                                        }
                                    )
                                if knowledge_base.urls != new_urls_data:
                                    knowledge_base.urls = new_urls_data
                                    fields_changed = True
                                if fields_changed:
                                    db.add(knowledge_base)
                        elif current_version:
                            # Create new knowledge base if it doesn't exist
                            knowledge_base = AgentKnowledgeBase(
                                files=list(request.files) if path == "files" else [],
                                urls=list(request.urls) if path == "urls" else [],
                            )
                            db.add(knowledge_base)
                            db.flush()
                            current_version.knowledge_base_id = knowledge_base.id
                            db.add(current_version)
                            fields_changed = True

            # Mark agent as updated if any fields changed
            if fields_changed:
                agent.is_updated = True
                logger.info(f"Agent {agent.id} marked as updated due to knowledge changes")
            db.add(agent)
            db.commit()
            db.refresh(agent)
            return agent_pb2.UpdateAgentPartResponse(
                success=True,
                message="Agent knowledge sources updated.",
                agent=_agent_to_protobuf(db, agent),
            )
        # ... (Exception handling, finally db.close()) ...
        except Exception as e:
            print(e)
            if db.is_active:
                db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return agent_pb2.UpdateAgentPartResponse(success=False, message=str(e))
        finally:
            db.close()

    def UpdateAgentMcpServers(
        self, request: agent_pb2.UpdateAgentMcpServersRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        db = self.get_db()
        try:
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            # ... (Not Found, Permission Denied checks) ...
            if not agent:  # ... (Not Found, Permission Denied checks) ...
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.UpdateAgentPartResponse(success=False, message="Agent not found.")
            if agent.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied.")
                return agent_pb2.UpdateAgentPartResponse(
                    success=False, message="Permission denied."
                )

            fields_changed = False
            if agent.mcp_server_ids != list(request.mcp_server_ids):  # Always full list replacement
                agent.mcp_server_ids = list(request.mcp_server_ids)
                fields_changed = True

            # Mark agent as updated if any fields changed
            if fields_changed:
                agent.is_updated = True
                logger.info(f"Agent {agent.id} marked as updated due to MCP server changes")

            db.add(agent)
            db.commit()
            db.refresh(agent)
            return agent_pb2.UpdateAgentPartResponse(
                success=True,
                message="Agent MCP servers updated.",
            )
        # ... (Exception handling, finally db.close()) ...
        except Exception as e:  # Simplified
            if db.is_active:
                db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return agent_pb2.UpdateAgentPartResponse(success=False, message=str(e))
        finally:
            db.close()

    def UpdateAgentWorkflows(
        self, request: agent_pb2.UpdateAgentWorkflowsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        db = self.get_db()
        try:
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            # ... (Not Found, Permission Denied checks) ...
            if not agent:  # ... (Not Found, Permission Denied checks) ...
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.UpdateAgentPartResponse(success=False, message="Agent not found.")
            if agent.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied.")
                return agent_pb2.UpdateAgentPartResponse(
                    success=False, message="Permission denied."
                )

            fields_changed = False
            if agent.workflow_ids != list(request.workflow_ids):  # Always full list replacement
                agent.workflow_ids = list(request.workflow_ids)
                fields_changed = True

            # Mark agent as updated if any fields changed
            if fields_changed:
                agent.is_updated = True
                logger.info(f"Agent {agent.id} marked as updated due to workflow changes")

            db.add(agent)
            db.commit()
            db.refresh(agent)
            return agent_pb2.UpdateAgentPartResponse(
                success=True,
                message="Agent workflows updated.",
            )
        # ... (Exception handling, finally db.close()) ...
        except Exception as e:  # Simplified
            if db.is_active:
                db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return agent_pb2.UpdateAgentPartResponse(success=False, message=str(e))
        finally:
            db.close()

    def UpdateAgentSettings(
        self, request: agent_pb2.UpdateAgentSettingsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        db = self.get_db()
        try:
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            # ... (Not Found, Permission Denied checks) ...
            if not agent:  # ... (Not Found, Permission Denied checks) ...
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.UpdateAgentPartResponse(success=False, message="Agent not found.")
            if agent.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied.")
                return agent_pb2.UpdateAgentPartResponse(
                    success=False, message="Permission denied."
                )

            fields_changed = False
            for path in request.update_mask.paths:
                if path == "user_ids" and agent.user_ids != list(request.user_ids):
                    agent.user_ids = list(request.user_ids)
                    fields_changed = True
                elif (
                    path == "agent_topic_type"
                    and agent.agent_topic_type != request.agent_topic_type
                ):
                    agent.agent_topic_type = request.agent_topic_type
                    fields_changed = True
                elif path == "tags":
                    new_tags = list(request.tags) if request.tags else None
                    if agent.tags != new_tags:
                        agent.tags = new_tags
                        fields_changed = True
                elif path == "status":
                    status_str = agent_pb2.Status.Name(request.status).lower()
                    if agent.status != status_str:
                        agent.status = status_str
                        fields_changed = True
                elif path == "is_bench_employee":
                    if agent.is_bench_employee != request.is_bench_employee:
                        agent.is_bench_employee = request.is_bench_employee
                        fields_changed = True
                elif path == "is_changes_marketplace":
                    print(f"[DEBUG] is_changes_marketplace: {request.is_changes_marketplace}")
                    if agent.is_changes_marketplace != request.is_changes_marketplace:
                        agent.is_changes_marketplace = request.is_changes_marketplace
                        fields_changed = True
                elif path == "is_customizable":
                    if agent.is_customizable != request.is_customizable:
                        agent.is_customizable = request.is_customizable
                        fields_changed = True
                elif path == "example_prompts":
                    if agent.example_prompts != list(request.example_prompts):
                        agent.example_prompts = list(request.example_prompts)
                        fields_changed = True
                elif path == "is_a2a":
                    if agent.is_a2a != request.is_a2a:
                        agent.is_a2a = request.is_a2a
                        fields_changed = True

            # Mark agent as updated if any fields changed
            if fields_changed:
                agent.is_updated = True
                logger.info(f"Agent {agent.id} marked as updated due to settings changes")

            db.add(agent)
            db.commit()
            db.refresh(agent)
            return agent_pb2.UpdateAgentPartResponse(
                success=True, message="Agent settings updated."
            )
        except Exception as e:
            print(f"[ERROR] {e}")
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return agent_pb2.UpdateAgentPartResponse(success=False, message=str(e))
        finally:
            db.close()

    def ToggleAgentVisibility(
        self, request: agent_pb2.ToggleAgentVisibilityRequest, context: grpc.ServicerContext
    ) -> agent_pb2.ToggleAgentVisibilityResponse:
        db: Session = self.get_db()
        logger.info(
            "toggle_agent_visibility_request",
            extra={"agent_id": request.agent_id, "owner_id": request.owner.id},
        )
        try:
            agent = db.get(AgentConfig, request.agent_id)  # Get agent by PK

            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.ToggleAgentVisibilityResponse(
                    success=False, message="Agent not found."
                )

            # Permission Check: Only the agent's owner can change its visibility
            if agent.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied. You are not the owner of this agent.")
                return agent_pb2.ToggleAgentVisibilityResponse(
                    success=False, message="Permission denied. You are not the owner."
                )

            message = ""
            new_visibility_for_response = ""  # To send back the new visibility in proto if needed

            if agent.visibility == AgentVisibilityEnum.PRIVATE:
                # --- Going from PRIVATE to PUBLIC ---

                # Constraint: Imported agents from different owners might have restrictions
                if (
                    agent.is_imported
                    and agent.template_owner_id  # Check if it has a template owner
                    and agent.template_owner_id != agent.owner_id  # And template owner is different
                ):
                    # This is a business rule: what does "minimally altered" mean?
                    # For now, we'll keep the restriction. You might add more checks.
                    err_msg = (
                        "This agent was imported from a template owned by another party. "
                        "To share your version publicly, please ensure it is significantly customized. "
                        "Direct republishing of minimally-altered cloned templates is restricted."
                    )
                    context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                    context.set_details(err_msg)
                    return agent_pb2.ToggleAgentVisibilityResponse(success=False, message=err_msg)

                if not agent.current_version_id:
                    err_msg = "Agent has no current version. Please create a version before making it public."
                    context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                    context.set_details(err_msg)
                    return agent_pb2.ToggleAgentVisibilityResponse(success=False, message=err_msg)

                agent.visibility = AgentVisibilityEnum.PUBLIC
                # agent.is_changes_marketplace = True # Consider if this flag is still needed if listing is auto-managed

                # Create or update marketplace listing for the current version
                marketplace_listing = _create_marketplace_listing_from_agent(db, agent)
                if not marketplace_listing:
                    db.rollback()  # Rollback any changes if listing creation fails
                    context.set_code(grpc.StatusCode.INTERNAL)
                    details = "Failed to create or update marketplace listing."
                    context.set_details(details)
                    return agent_pb2.ToggleAgentVisibilityResponse(success=False, message=details)

                logger.info(
                    f"Marketplace listing {marketplace_listing.id} is active for agent {agent.id}."
                )
                message = f"Agent '{agent.name}' is now PUBLIC and listed in the marketplace with version {marketplace_listing.version_number}."
                new_visibility_for_response = AgentVisibilityEnum.PUBLIC.value

            elif agent.visibility == AgentVisibilityEnum.PUBLIC:
                # --- Going from PUBLIC to PRIVATE ---
                agent.visibility = AgentVisibilityEnum.PRIVATE
                # agent.is_changes_marketplace = False # Reset flag

                # Deactivate (or delete) marketplace listings for this agent
                # We'll deactivate them to keep history, but you could delete them.
                marketplace_listings = (
                    db.query(AgentMarketplaceListing).filter(
                        AgentMarketplaceListing.agent_config_id == agent.id
                    )
                    # .filter(AgentMarketplaceListing.listed_by_user_id == agent.owner_id) # Ensure only owner's listings
                    .all()
                )

                if not marketplace_listings:
                    logger.info(
                        f"No marketplace listings found for agent {agent.id} to deactivate."
                    )
                else:
                    for listing in marketplace_listings:
                        listing.status = (
                            AgentStatusEnum.INACTIVE
                        )  # Or DELETED if you prefer to remove
                        listing.visibility = AgentVisibilityEnum.PRIVATE  # Make listing private too
                        listing.updated_at = datetime.now(timezone.utc)
                        db.add(listing)
                        logger.info(
                            f"Deactivated marketplace listing {listing.id} as agent {agent.id} made private."
                        )

                message = f"Agent '{agent.name}' is now PRIVATE. Its marketplace listings have been deactivated."
                new_visibility_for_response = AgentVisibilityEnum.PRIVATE.value

            else:
                # Should not happen if visibility is always one of the defined enums
                err_msg = f"Agent {agent.id} has an unknown visibility state: {agent.visibility}"
                logger.error(err_msg)
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)  # Or INTERNAL
                context.set_details(err_msg)
                return agent_pb2.ToggleAgentVisibilityResponse(
                    success=False, message="Agent has an unknown visibility state."
                )

            agent.updated_at = datetime.now(timezone.utc)
            db.add(agent)
            db.commit()
            db.refresh(agent)  # To get the updated state from DB

            # Construct the response
            # You might want to include the updated agent in the response
            # For now, just success and message.
            # If your proto response has a field for new_visibility:
            # response_kwargs = {"success": True, "message": message, "new_visibility": new_visibility_for_response}
            response_kwargs = {"success": True, "message": message}
            # If your proto response can take an Agent message:
            # response_kwargs["agent"] = _agent_to_protobuf(db, agent) # Ensure _agent_to_protobuf exists

            return agent_pb2.ToggleAgentVisibilityResponse(**response_kwargs)

        except Exception as e:
            if db and db.is_active:  # Check if db is not None before checking is_active
                db.rollback()
            logger.error(
                f"Error toggling agent visibility for {request.agent_id}: {str(e)}", exc_info=True
            )
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.ToggleAgentVisibilityResponse(
                success=False, message="Internal server error."
            )
        finally:
            if db:
                db.close()

    def deleteAgent(
        self, request: agent_pb2.DeleteAgentRequest, context: grpc.ServicerContext
    ) -> agent_pb2.DeleteAgentResponse:
        """
        Deletes an agent configuration.
        """
        db = self.get_db()
        logger.info("delete_agent_request", agent_id=request.id)

        try:
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.id).first()
            if agent is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent {request.name} not found")
                return agent_pb2.DeleteAgentResponse(
                    success=False, message=f"Agent {request.name} not found"
                )

            # Store agent details before deletion for notification
            agent_name = agent.name

            db.delete(agent)
            db.commit()

            return agent_pb2.DeleteAgentResponse(
                success=True, message=f"Agent {agent_name} deleted successfully"
            )

        except Exception as e:
            print(e)
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.DeleteAgentResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def listAgents(
        self, request: agent_pb2.ListAgentsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.ListAgentsResponse:
        """
        Retrieves a paginated list of agent configurations.
        """
        db = self.get_db()
        try:
            print(f"[LIST AGENTS REQUEST] {request}")
            # Build filters from request
            filters = {}

            if request.owner_id:
                filters["owner_id"] = request.owner_id

            if hasattr(request, "department") and request.HasField("department"):
                filters["department"] = request.department

            if hasattr(request, "organization_id") and request.HasField("organization_id"):
                filters["organization_id"] = request.organization_id

            if hasattr(request, "status") and request.HasField("status"):
                filters["status"] = agent_pb2.Status.Name(request.status).lower()

            if hasattr(request, "visibility") and request.HasField("visibility"):
                filters["visibility"] = agent_pb2.Visibility.Name(request.visibility).lower()

            if hasattr(request, "is_bench_employee") and request.HasField("is_bench_employee"):
                filters["is_bench_employee"] = request.is_bench_employee

            if hasattr(request, "is_customizable") and request.HasField("is_customizable"):
                filters["is_customizable"] = request.is_customizable

            if hasattr(request, "is_a2a") and request.HasField("is_a2a"):
                filters["is_a2a"] = request.is_a2a

            if hasattr(request, "search") and getattr(request, "search", None):
                filters["search"] = request.search

            # Build pagination
            pagination = {"page": request.page, "page_size": request.page_size}

            # Use optimized function
            result = _agents_to_protobuf(
                db=db, agent_ids=None, filters=filters, pagination=pagination
            )

            logger.info(f"Found {len(result['agents'])} agents")

            return agent_pb2.ListAgentsResponse(
                success=True,
                agents=result["agents"],
                total=result["total"],
                page=result["page"],
                total_pages=result["total_pages"],
            )
        except Exception as e:
            print(f"[ERROR] {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.ListAgentsResponse(success=False)
        finally:
            db.close()

    def getAgentsByIds(
        self, request: agent_pb2.GetAgentsByIdsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.GetAgentsByIdsResponse:
        """
        Retrieves multiple agents by their IDs.

        Args:
            request: Contains the list of agent IDs to retrieve
            context: gRPC service context

        Returns:
            agent_pb2.GetAgentsByIdsResponse: Response containing the list of agents
        """
        db = self.get_db()
        try:
            logger.info("get_agents_by_ids_request", agent_ids=request.ids)

            if not request.ids:
                return agent_pb2.GetAgentsByIdsResponse(
                    success=False, message="No agent IDs provided", agents=[]
                )

            agents_proto = _agents_to_protobuf(db, request.ids)

            return agent_pb2.GetAgentsByIdsResponse(
                success=True,
                message=f"Successfully retrieved {len(agents_proto)} agents",
                agents=agents_proto,
            )

        except Exception as e:
            logger.error(f"Error retrieving agents by IDs: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.GetAgentsByIdsResponse(
                success=False, message="Internal server error", agents=[]
            )
        finally:
            db.close()

    def rateAgent(
        self, request: agent_pb2.RateAgentRequest, context: grpc.ServicerContext
    ) -> agent_pb2.RateAgentResponse:
        """
        Rate an agent and update its average rating.

        Args:
            request: Contains the agent ID, user ID, and rating value
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated average rating
        """
        db = self.get_db()
        try:
            logger.info(
                "rate_agent_request",
                agent_id=request.agent_id,
                user_id=request.user_id,
                rating=request.rating,
            )

            # Validate rating value (between 1.0 and 5.0)
            if request.rating < 1.0 or request.rating > 5.0:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Rating must be between 1.0 and 5.0")
                return agent_pb2.RateAgentResponse(
                    success=False, message="Rating must be between 1.0 and 5.0", average_rating=0.0
                )

            # Check if agent exists
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.RateAgentResponse(
                    success=False,
                    message=f"Agent with ID {request.agent_id} not found",
                    average_rating=0.0,
                )

            # Check if user has already rated this agent
            existing_rating = (
                db.query(AgentRating)
                .filter(
                    AgentRating.agent_id == request.agent_id, AgentRating.user_id == request.user_id
                )
                .first()
            )

            if existing_rating:
                # Update existing rating
                old_rating = existing_rating.rating
                existing_rating.rating = request.rating
                existing_rating.updated_at = datetime.now(timezone.utc)
                db.commit()
                logger.info(
                    "updated_agent_rating",
                    agent_id=request.agent_id,
                    user_id=request.user_id,
                    old_rating=old_rating,
                    new_rating=request.rating,
                )
            else:
                # Create new rating
                new_rating = AgentRating(
                    agent_id=request.agent_id, user_id=request.user_id, rating=request.rating
                )
                db.add(new_rating)
                db.commit()
                logger.info(
                    "created_agent_rating",
                    agent_id=request.agent_id,
                    user_id=request.user_id,
                    rating=request.rating,
                )

            # Calculate new average rating
            ratings = db.query(AgentRating).filter(AgentRating.agent_id == request.agent_id).all()
            total_rating = sum(r.rating for r in ratings)
            average_rating = total_rating / len(ratings) if ratings else 0.0

            # Update agent with new average rating
            agent.average_rating = average_rating
            agent.updated_at = datetime.now(timezone.utc)
            db.commit()

            # If this is an agent created from a template, update the template's rating too
            # Commenting out for now as it's causing test failures
            # We'll implement this in a separate PR after fixing the tests
            """
            if agent.template_id:
                template = (
                    db.query(AgentTemplate).filter(AgentTemplate.id == agent.template_id).first()
                )

                if template:
                    # Get all ratings for all agents created from this template
                    template_agents = (
                        db.query(AgentConfig).filter(AgentConfig.template_id == template.id).all()
                    )

                    template_agent_ids = [a.id for a in template_agents]

                    if template_agent_ids:
                        template_ratings = (
                            db.query(AgentRating)
                            .filter(AgentRating.agent_id.in_(template_agent_ids))
                            .all()
                        )

                        if template_ratings:
                            template_total_rating = sum(r.rating for r in template_ratings)
                            template_average_rating = template_total_rating / len(template_ratings)

                            template.average_rating = template_average_rating
                            template.updated_at = datetime.now(timezone.utc)
                            db.commit()

                            logger.info(
                                "updated_template_rating",
                                template_id=template.id,
                                average_rating=template_average_rating,
                            )
            """

            return agent_pb2.RateAgentResponse(
                success=True,
                message=f"Rating for agent {agent.name} updated successfully",
                average_rating=average_rating,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error rating agent: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.RateAgentResponse(
                success=False, message="Failed to update agent rating", average_rating=0.0
            )
        finally:
            db.close()

    def UpdateAgentCapabilities(
        self, request: agent_pb2.UpdateAgentCapabilitiesRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        """
        Updates the capabilities of an agent.

        This method handles updating the capabilities of an agent, including:
        - Validating the agent exists and the user has permission
        - Creating or updating the capabilities record
        - Updating the agent's capabilities_id reference

        Args:
            request (agent_pb2.UpdateAgentCapabilitiesRequest): The update capabilities request
            context (grpc.ServicerContext): The gRPC service context

        Returns:
            agent_pb2.UpdateAgentPartResponse: Response containing the updated agent details
        """
        db = self.get_db()
        try:
            # Find the agent
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.UpdateAgentPartResponse(success=False, message="Agent not found.")

            # Check permissions
            if agent.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied.")
                return agent_pb2.UpdateAgentPartResponse(
                    success=False, message="Permission denied."
                )

            # Check if we need to create or update capabilities
            capabilities = None
            if agent.capabilities_id:
                # Update existing capabilities
                capabilities = (
                    db.query(AgentCapabilities)
                    .filter(AgentCapabilities.id == agent.capabilities_id)
                    .first()
                )

            if not capabilities:
                # Create new capabilities
                capabilities = AgentCapabilities(
                    capabilities=json.loads(request.capabilities) if request.capabilities else None,
                    input_modes=list(request.input_modes) if request.input_modes else None,
                    output_modes=list(request.output_modes) if request.output_modes else None,
                    response_model=list(request.response_model) if request.response_model else None,
                )
                db.add(capabilities)
                db.flush()  # Generate ID
                agent.capabilities_id = capabilities.id
            else:
                # Update existing capabilities based on field mask
                for path in request.update_mask.paths:
                    if path == "capabilities" and request.capabilities:
                        capabilities.capabilities = json.loads(request.capabilities)
                    elif path == "input_modes":
                        capabilities.input_modes = list(request.input_modes)
                    elif path == "output_modes":
                        capabilities.output_modes = list(request.output_modes)
                    elif path == "response_model":
                        capabilities.response_model = list(request.response_model)
            # Mark agent as updated since capabilities changed
            agent.is_updated = True
            logger.info(f"Agent {agent.id} marked as updated due to capabilities changes")

            # Update the agent and capabilities
            db.add(capabilities)
            db.add(agent)
            db.commit()
            db.refresh(agent)

            return agent_pb2.UpdateAgentPartResponse(
                success=True,
                message="Agent capabilities updated successfully.",
                agent=_agent_to_protobuf(db=db, agent=agent),
            )
        except Exception as e:
            logger.error(
                f"Error updating agent capabilities {request.agent_id}: {e}", exc_info=True
            )
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return agent_pb2.UpdateAgentPartResponse(success=False, message=str(e))
        finally:
            db.close()

    def UpdateAgentCombined(
        self, request: agent_pb2.UpdateAgentCombinedRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UpdateAgentPartResponse:
        db = self.get_db()
        try:
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.UpdateAgentPartResponse(success=False, message="Agent not found.")

            if agent.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied to update this agent.")
                return agent_pb2.UpdateAgentPartResponse(
                    success=False, message="Permission denied."
                )

            fields_changed = False

            # Process Core Details updates (direct agent fields)
            for path in request.update_mask.paths:
                if path == "name" and request.HasField("name") and agent.name != request.name:
                    agent.name = request.name
                    fields_changed = True
                elif (
                    path == "description"
                    and request.HasField("description")
                    and agent.description != request.description
                ):
                    agent.description = request.description
                    fields_changed = True
                elif (
                    path == "avatar"
                    and hasattr(request, "avatar")
                    and agent.avatar != request.avatar
                ):
                    if request.avatar == "":
                        agent.avatar = None
                    else:
                        agent.avatar = request.avatar
                    fields_changed = True
                elif (
                    path == "system_message"
                    and hasattr(request, "system_message")
                    and agent.system_message != request.system_message
                ):
                    agent.system_message = request.system_message
                    fields_changed = True
                elif (
                    path == "department"
                    and hasattr(request, "department")
                    and agent.department != request.department
                ):
                    agent.department = request.department
                    fields_changed = True
                elif path == "tone" and hasattr(request, "tone"):
                    tone_str = agent_pb2.Tone.Name(request.tone).lower()
                    if agent.tone != tone_str:
                        agent.tone = tone_str
                        fields_changed = True
                elif (
                    path == "agent_topic_type"
                    and hasattr(request, "agent_topic_type")
                    and agent.agent_topic_type != request.agent_topic_type
                ):
                    agent.agent_topic_type = request.agent_topic_type
                    fields_changed = True
                elif path == "category" and hasattr(request, "category"):
                    category_str = agent_pb2.Category.Name(request.category).lower()
                    if agent.category != category_str:
                        agent.category = category_str
                        fields_changed = True

            # Process Model Configuration updates
            if any(
                p in request.update_mask.paths
                for p in ["model_provider", "model_name", "temperature", "max_tokens"]
            ):
                if agent.current_version_id:
                    current_version = (
                        db.query(AgentConfigVersion)
                        .filter(AgentConfigVersion.id == agent.current_version_id)
                        .first()
                    )
                    if current_version:
                        model_config = None
                        if current_version.model_config_id:
                            model_config = (
                                db.query(AgentModelConfig)
                                .filter(AgentModelConfig.id == current_version.model_config_id)
                                .first()
                            )

                        if not model_config:
                            model_config = AgentModelConfig()
                            db.add(model_config)
                            db.flush()
                            current_version.model_config_id = model_config.id
                            db.add(current_version)
                            db.flush()

                        for path in request.update_mask.paths:
                            if path == "model_provider" and hasattr(request, "model_provider"):
                                if model_config.model_provider != request.model_provider:
                                    model_config.model_provider = (
                                        request.model_provider
                                        if request.model_provider != ""
                                        else None
                                    )
                                    fields_changed = True
                            elif path == "model_name" and hasattr(request, "model_name"):
                                if model_config.model_name != request.model_name:
                                    model_config.model_name = (
                                        request.model_name if request.model_name != "" else None
                                    )
                                    fields_changed = True
                            elif path == "temperature" and hasattr(request, "temperature"):
                                if model_config.temperature != request.temperature:
                                    model_config.temperature = (
                                        request.temperature if request.temperature != 0.0 else None
                                    )
                                    fields_changed = True
                            elif path == "max_tokens" and hasattr(request, "max_tokens"):
                                if model_config.max_tokens != request.max_tokens:
                                    model_config.max_tokens = (
                                        request.max_tokens if request.max_tokens != 0 else None
                                    )
                                    fields_changed = True
                        if fields_changed:
                            db.add(model_config)

            # Process Knowledge updates (files and urls)
            if any(p in request.update_mask.paths for p in ["files", "urls"]):
                if agent.current_version_id:
                    current_version = (
                        db.query(AgentConfigVersion)
                        .filter(AgentConfigVersion.id == agent.current_version_id)
                        .first()
                    )
                    if current_version and current_version.knowledge_base_id:
                        knowledge_base = (
                            db.query(AgentKnowledgeBase)
                            .filter(AgentKnowledgeBase.id == current_version.knowledge_base_id)
                            .first()
                        )
                        if knowledge_base:
                            for path in request.update_mask.paths:
                                if path == "files":
                                    new_files = []
                                    for file_str in request.files:
                                        new_files.append(
                                            {
                                                "file": file_str,
                                                "created_at": datetime.utcnow().isoformat(),
                                                "size": self._get_gcs_file_size(file_str),
                                            }
                                        )
                                    if knowledge_base.files != new_files:
                                        knowledge_base.files = new_files
                                        fields_changed = True
                                elif path == "urls":
                                    new_urls = []
                                    for url_str in request.urls:
                                        new_urls.append(
                                            {
                                                "url": url_str,
                                                "created_at": datetime.utcnow().isoformat(),
                                            }
                                        )
                                    if knowledge_base.urls != new_urls:
                                        knowledge_base.urls = new_urls
                                        fields_changed = True
                            if fields_changed:
                                db.add(knowledge_base)

            # Process MCP Servers updates
            if "mcp_server_ids" in request.update_mask.paths:
                if agent.mcp_server_ids != list(request.mcp_server_ids):
                    agent.mcp_server_ids = list(request.mcp_server_ids)
                    fields_changed = True

            # Process Workflows updates
            if "workflow_ids" in request.update_mask.paths:
                if agent.workflow_ids != list(request.workflow_ids):
                    agent.workflow_ids = list(request.workflow_ids)
                    fields_changed = True

            # Process Settings updates
            for path in request.update_mask.paths:
                if (
                    path == "user_ids"
                    and hasattr(request, "user_ids")
                    and agent.user_ids != list(request.user_ids)
                ):
                    agent.user_ids = list(request.user_ids)
                    fields_changed = True
                elif path == "tags" and hasattr(request, "tags"):
                    new_tags = list(request.tags) if request.tags else None
                    if agent.tags != new_tags:
                        agent.tags = new_tags
                        fields_changed = True
                elif path == "status" and hasattr(request, "status"):
                    status_str = agent_pb2.Status.Name(request.status).lower()
                    if agent.status != status_str:
                        agent.status = status_str
                        fields_changed = True
                elif path == "is_changes_marketplace" and hasattr(
                    request, "is_changes_marketplace"
                ):
                    if agent.is_changes_marketplace != request.is_changes_marketplace:
                        agent.is_changes_marketplace = request.is_changes_marketplace
                        fields_changed = True
                elif path == "is_bench_employee" and hasattr(request, "is_bench_employee"):
                    if agent.is_bench_employee != request.is_bench_employee:
                        agent.is_bench_employee = request.is_bench_employee
                        fields_changed = True
                elif path == "is_a2a" and hasattr(request, "is_a2a"):
                    if agent.is_a2a != request.is_a2a:
                        agent.is_a2a = request.is_a2a
                        fields_changed = True
                elif path == "is_customizable" and hasattr(request, "is_customizable"):
                    if agent.is_customizable != request.is_customizable:
                        agent.is_customizable = request.is_customizable
                        fields_changed = True
                elif path == "example_prompts" and hasattr(request, "example_prompts"):
                    new_example_prompts = (
                        list(request.example_prompts) if request.example_prompts else None
                    )
                    if agent.example_prompts != new_example_prompts:
                        agent.example_prompts = new_example_prompts
                        fields_changed = True

            # Process Variables updates
            if "variables" in request.update_mask.paths:
                # Remove all existing variables
                db.query(AgentVariables).filter(AgentVariables.agent_config_id == agent.id).delete()
                # Add new variables
                for var_proto in request.variables:
                    db_variable = AgentVariables(
                        name=var_proto.name,
                        description=(
                            var_proto.description if hasattr(var_proto, "description") else None
                        ),
                        type=var_proto.type,
                        default_value=(
                            var_proto.default_value if hasattr(var_proto, "default_value") else None
                        ),
                        agent_config_id=agent.id,
                    )
                    db.add(db_variable)
                fields_changed = True

            # Process Capabilities updates
            if any(
                p in request.update_mask.paths
                for p in ["capabilities", "input_modes", "output_modes", "response_model"]
            ):
                capabilities = None
                if agent.capabilities_id:
                    capabilities = (
                        db.query(AgentCapabilities)
                        .filter(AgentCapabilities.id == agent.capabilities_id)
                        .first()
                    )

                if not capabilities:
                    capabilities = AgentCapabilities()
                    db.add(capabilities)
                    db.flush()
                    agent.capabilities_id = capabilities.id
                    db.add(agent)
                    db.flush()

                for path in request.update_mask.paths:
                    if path == "capabilities":
                        if capabilities.capabilities != (
                            json.loads(request.capabilities) if request.capabilities else None
                        ):
                            capabilities.capabilities = (
                                json.loads(request.capabilities) if request.capabilities else None
                            )
                            fields_changed = True
                    elif path == "input_modes":
                        if capabilities.input_modes != list(request.input_modes):
                            capabilities.input_modes = list(request.input_modes)
                            fields_changed = True
                    elif path == "output_modes":
                        if capabilities.output_modes != list(request.output_modes):
                            capabilities.output_modes = list(request.output_modes)
                            fields_changed = True
                    elif path == "response_model":
                        if capabilities.response_model != list(request.response_model):
                            capabilities.response_model = list(request.response_model)
                            fields_changed = True
                if fields_changed:
                    db.add(capabilities)

            if fields_changed:
                agent.is_updated = True
                logger.info(f"Agent {agent.id} marked as updated due to combined changes")

            db.add(agent)
            db.commit()
            db.refresh(agent)

            return agent_pb2.UpdateAgentPartResponse(
                success=True,
                message="Agent updated successfully.",
                agent=_agent_to_protobuf(db=db, agent=agent),
            )
        except Exception as e:
            logger.error(
                f"Error updating agent combined details {request.agent_id}: {e}", exc_info=True
            )
            if db.is_active:
                db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.UpdateAgentPartResponse(
                success=False, message="Internal server error."
            )
        finally:
            db.close()

    def UpdateAgentVariables(
        self, request: agent_pb2.UpdateAgentVariablesRequest, context: grpc.ServicerContext
    ):
        """
        Updates all variables for a given agent.
        Replaces existing variables with those provided in the request.
        """
        from app.models.agent import AgentConfig, AgentVariables

        db = self.get_db()
        try:
            # Authenticate owner
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.UpdateAgentPartResponse(success=False, message="Agent not found.")

            if agent.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied to update this agent's variables.")
                return agent_pb2.UpdateAgentPartResponse(
                    success=False, message="Permission denied."
                )

            # Remove all existing variables
            db.query(AgentVariables).filter(AgentVariables.agent_config_id == agent.id).delete()

            # Add new variables
            for var_proto in request.variables:
                db_variable = AgentVariables(
                    name=var_proto.name,
                    description=(
                        var_proto.description if hasattr(var_proto, "description") else None
                    ),
                    type=var_proto.type,
                    default_value=(
                        var_proto.default_value if hasattr(var_proto, "default_value") else None
                    ),
                    agent_config_id=agent.id,
                )
                db.add(db_variable)

            db.commit()
            db.refresh(agent)
            return agent_pb2.UpdateAgentPartResponse(
                success=True,
                message="Agent variables updated successfully.",
                agent=_agent_to_protobuf(db=db, agent=agent),
            )
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.UpdateAgentPartResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def createAgentVersion(self, request, context: grpc.ServicerContext):
        """
        Creates a new version of an agent configuration.

        This method:
        - Validates the agent exists and user has permission
        - Creates a snapshot of the current agent configuration
        - Resets the is_updated flag to False

        Args:
            request: The create version request containing agent_id and owner info
            context: gRPC service context

        Returns:
            Response containing success status and version details
        """
        db = self.get_db()
        try:
            # Find the agent
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return {"success": False, "message": "Agent not found."}

            # Check permissions
            if agent.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied.")
                return {"success": False, "message": "Permission denied."}

            # Check if agent has been updated since last version
            if not agent.is_updated:
                return {
                    "success": False,
                    "message": "No changes detected since last version. Cannot create version.",
                }

            # Handle model config for version
            model_config_id = None
            if agent.model_provider or agent.model_name or agent.model_api_key:
                model_config = AgentModelConfig(
                    model_provider=agent.model_provider,
                    model_name=agent.model_name,
                    model_api_key=agent.model_api_key,
                )
                db.add(model_config)
                db.flush()
                model_config_id = model_config.id

            # Handle knowledge base for version
            knowledge_base_id = None
            if agent.files or agent.urls:
                # Assuming agent.files and agent.urls are already lists of dicts from the DB
                # If they are still plain strings, they would need to be parsed here.
                knowledge_base = AgentKnowledgeBase(
                    files=agent.files if agent.files else [],
                    urls=agent.urls if agent.urls else [],
                )
                db.add(knowledge_base)
                db.flush()
                knowledge_base_id = knowledge_base.id

            # Create version snapshot
            version = AgentConfigVersion(
                agent_config_id=agent.id,
                model_config_id=model_config_id,
                knowledge_base_id=knowledge_base_id,
                version_number=getattr(request, "version_number", "1.1.0"),
                name=agent.name,
                description=agent.description,
                avatar=agent.avatar,
                agent_category=agent.agent_category,
                system_message=agent.system_message,
                workflow_ids=agent.workflow_ids if agent.workflow_ids else [],
                mcp_server_ids=agent.mcp_server_ids if agent.mcp_server_ids else [],
                agent_topic_type=agent.agent_topic_type,
                department=agent.department,
                organization_id=agent.organization_id,
                tone=agent.tone,
                is_bench_employee=agent.is_bench_employee,
                is_changes_marketplace=agent.is_changes_marketplace,
                is_a2a=agent.is_a2a,
                is_customizable=agent.is_customizable,
                capabilities_id=agent.capabilities_id,
                example_prompts=agent.example_prompts if agent.example_prompts else [],
                category=agent.category,
                tags=agent.tags if agent.tags else [],
                status=agent.status,
                version_notes=getattr(request, "version_notes", "New version created"),
            )

            db.add(version)
            db.flush()  # Get the version ID

            # Reset the is_updated flag
            agent.is_updated = False
            db.add(agent)

            db.commit()
            db.refresh(version)

            logger.info(f"Created version {version.version_number} for agent {agent.id}")

            # Return a simple dict for now since protobuf types don't exist yet
            return {
                "success": True,
                "message": f"Version {version.version_number} created successfully",
                "version_number": version.version_number,
            }

        except Exception as e:
            logger.error(f"Error creating agent version: {e}", exc_info=True)
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return {"success": False, "message": "Internal server error"}
        finally:
            db.close()

    def publishAgentToMarketplace(self, request, context: grpc.ServicerContext):
        """
        Publishes an agent version to the marketplace.

        Args:
            request: Contains agent_id, version_number, and marketplace details
            context: gRPC service context

        Returns:
            Response containing success status and marketplace listing details
        """
        db = self.get_db()
        try:
            # Find the agent
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return {"success": False, "message": "Agent not found."}

            # Check permissions
            if agent.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied.")
                return {"success": False, "message": "Permission denied."}

            # Find the specific version
            version = (
                db.query(AgentConfigVersion)
                .filter(
                    AgentConfigVersion.agent_config_id == request.agent_id,
                    AgentConfigVersion.version_number == request.version_number,
                )
                .first()
            )

            if not version:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(
                    f"Version {request.version_number} not found for agent {request.agent_id}"
                )
                return {"success": False, "message": "Version not found."}

            # Check if already published
            existing_listing = (
                db.query(AgentMarketplaceListing)
                .filter(
                    AgentMarketplaceListing.agent_config_id == request.agent_id,
                    AgentMarketplaceListing.version_number == request.version_number,
                )
                .first()
            )

            if existing_listing:
                return {
                    "success": False,
                    "message": f"Version {request.version_number} is already published to marketplace.",
                }

            # Create marketplace listing
            listing = AgentMarketplaceListing(
                agent_config_id=agent.id,
                agent_config_version_id=version.id,  # Use version ID
                version_number=version.version_number,  # Use version number from AgentConfigVersion
                listed_by_user_id=request.owner.id,  # Add listed_by_user_id
                title=getattr(request, "title", agent.name),
                description=getattr(request, "description", agent.description),
                status=AgentStatusEnum.ACTIVE.value,
            )

            db.add(listing)
            db.commit()
            db.refresh(listing)

            logger.info(
                f"Published agent {agent.id} version {version.version_number} to marketplace"
            )

            return {
                "success": True,
                "message": f"Agent version {version.version_number} published to marketplace successfully",
            }

        except Exception as e:
            logger.error(f"Error publishing agent to marketplace: {e}", exc_info=True)
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return {"success": False, "message": "Internal server error"}
        finally:
            db.close()

    def listAgentVersions(self, request, context: grpc.ServicerContext):
        """
        Lists all versions of an agent.

        Args:
            request: Contains agent_id and pagination info
            context: gRPC service context

        Returns:
            Response containing list of agent versions
        """
        db = self.get_db()
        try:
            # Find the agent
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return {"success": False, "message": "Agent not found."}

            # Check permissions (owner or public agent)
            if (
                agent.owner_id != request.owner.id
                and agent.visibility != AgentVisibilityEnum.PUBLIC
            ):
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied.")
                return {"success": False, "message": "Permission denied."}

            # Get versions with pagination
            query = (
                db.query(AgentConfigVersion)
                .filter(AgentConfigVersion.agent_config_id == request.agent_id)
                .order_by(desc(AgentConfigVersion.version_number))
            )

            total = query.count()

            # Apply pagination if provided
            page = getattr(request, "page", 1)
            page_size = getattr(request, "page_size", 10)

            versions = query.offset((page - 1) * page_size).limit(page_size).all()

            # Convert to protobuf format (simplified for now)
            version_list = []
            for version in versions:
                version_list.append(
                    {
                        "version_number": version.version_number,
                        "created_at": version.created_at.isoformat(),
                        "version_notes": version.version_notes,
                    }
                )

            return {
                "success": True,
                "message": f"Found {len(version_list)} versions",
                "total": total,
                "versions": version_list,
            }

        except Exception as e:
            logger.error(f"Error listing agent versions: {e}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return {"success": False, "message": "Internal server error"}
        finally:
            db.close()

    def getMarketplaceListings(self, request, context: grpc.ServicerContext):
        """
        Gets marketplace listings with optional filtering.

        Args:
            request: Contains filtering and pagination options
            context: gRPC service context

        Returns:
            Response containing marketplace listings
        """
        db = self.get_db()
        try:
            query = db.query(AgentMarketplaceListing).filter(
                AgentMarketplaceListing.status == AgentStatusEnum.ACTIVE.value
            )

            # Apply filters if provided
            if hasattr(request, "category") and request.category:
                # Join with AgentConfig to filter by category
                query = query.join(AgentConfig).filter(AgentConfig.category == request.category)

            if hasattr(request, "search") and request.search:
                # Search in title and description
                search_term = f"%{request.search}%"
                query = query.filter(
                    (AgentMarketplaceListing.title.ilike(search_term))
                    | (AgentMarketplaceListing.description.ilike(search_term))
                )

            # Get total count
            total = query.count()

            # Apply pagination
            page = getattr(request, "page", 1)
            page_size = getattr(request, "page_size", 20)

            listings = query.offset((page - 1) * page_size).limit(page_size).all()

            # Convert to simple dict format for now (protobuf conversion can be added later)
            listing_list = []
            for listing in listings:
                listing_list.append(
                    {
                        "id": str(listing.id),
                        "agent_config_id": str(listing.agent_config_id),
                        "version_number": (
                            listing.version_number if hasattr(listing, "version_number") else ""
                        ),
                        "title": listing.title or "",
                        "description": listing.description or "",
                        "created_at": listing.created_at.isoformat() if listing.created_at else "",
                    }
                )

            return {
                "success": True,
                "message": f"Found {len(listing_list)} marketplace listings",
                "total": total,
                "listings": listing_list,
            }

        except Exception as e:
            logger.error(f"Error getting marketplace listings: {e}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return {"success": False, "message": "Internal server error"}
        finally:
            db.close()

    def updateCallActions(self, request, context):
        """
        Update call actions for a specific agent.

        Args:
            request: UpdateCallActionsRequest containing agent_id and call_actions
            context: gRPC context

        Returns:
            UpdateCallActionsResponse with success status and message
        """
        db = SessionLocal()
        try:
            logger.info(f"Updating call actions for agent: {request.agent_id}")

            # Find the agent
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Agent not found")
                return agent_pb2.UpdateCallActionsResponse(
                    success=False,
                    message="Agent not found"
                )

            # Convert protobuf call actions to JSON format for database storage
            call_actions_data = []
            for call_action in request.call_actions:
                action_data = {
                    "action_type": agent_pb2.ActionType.Name(call_action.action_type),
                    "execution_type": agent_pb2.ExecutionType.Name(call_action.execution_type),
                    "id": call_action.id
                }
                call_actions_data.append(action_data)

            # Update the agent's call_actions field
            agent.call_actions = call_actions_data

            # Also update the current version if it exists
            if agent.current_version_id:
                current_version = db.query(AgentConfigVersion).filter(
                    AgentConfigVersion.id == agent.current_version_id
                ).first()
                if current_version:
                    current_version.call_actions = call_actions_data

            db.commit()

            logger.info(f"Successfully updated call actions for agent: {request.agent_id}")
            return agent_pb2.UpdateCallActionsResponse(
                success=True,
                message="Call actions updated successfully"
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating call actions for agent {request.agent_id}: {e}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.UpdateCallActionsResponse(
                success=False,
                message="Internal server error"
            )
        finally:
            db.close()

    def getCallActions(self, request, context):
        """
        Get call actions for a specific agent.

        Args:
            request: GetCallActionsRequest containing agent_id
            context: gRPC context

        Returns:
            GetCallActionsResponse with success status, message, and call_actions
        """
        db = SessionLocal()
        try:
            logger.info(f"Getting call actions for agent: {request.agent_id}")

            # Find the agent
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Agent not found")
                return agent_pb2.GetCallActionsResponse(
                    success=False,
                    message="Agent not found",
                    call_actions=[]
                )

            # Convert JSON call actions to protobuf format
            call_actions_proto = []
            if agent.call_actions:
                for action_data in agent.call_actions:
                    # Convert string enum values back to protobuf enums with defaults
                    action_type_str = action_data.get("action_type", "POST_CALL")
                    execution_type_str = action_data.get("execution_type", "WORKFLOW")

                    # Handle case where the enum string might not exist
                    try:
                        action_type = getattr(agent_pb2.ActionType, action_type_str)
                    except AttributeError:
                        action_type = agent_pb2.ActionType.POST_CALL

                    try:
                        execution_type = getattr(agent_pb2.ExecutionType, execution_type_str)
                    except AttributeError:
                        execution_type = agent_pb2.ExecutionType.WORKFLOW

                    call_action = agent_pb2.CallAction(
                        action_type=action_type,
                        execution_type=execution_type,
                        id=action_data.get("id", "")
                    )
                    call_actions_proto.append(call_action)

            response = agent_pb2.GetCallActionsResponse(
                success=True,
                message="Call actions retrieved successfully",
                call_actions=call_actions_proto
            )
            print(f"[DEBUG] Returning call actions: {response}")
            logger.info(f"Successfully retrieved call actions for agent: {request.agent_id}")
            return response

        except Exception as e:
            logger.error(f"Error getting call actions for agent {request.agent_id}: {e}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.GetCallActionsResponse(
                success=False,
                message="Internal server error",
                call_actions=[]
            )
        finally:
            db.close()
