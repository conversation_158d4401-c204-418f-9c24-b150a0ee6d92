# components/core/base_node.py
from pydantic import Field
from typing import List, Dict, Any, Type, Optional, ClassVar
import asyncio
import logging
import warnings
import inspect
from abc import ABC, abstractmethod
from app.models.workflow_builder.components import InputBase, HandleInput, DynamicHandleInput
from app.models.workflow_builder.components import Output
from app.utils.workflow_builder.validation import (
    validate_component_inputs,
    InputValidationError,
)

logger = logging.getLogger(__name__)


class BaseNode(ABC):
    """
    Base class for all workflow nodes/components.

    This is the standardized interface for all components in the workflow system.
    All components should inherit from this class and implement the execute method.
    The build method is deprecated and will be removed in a future version.
    """

    # These should be overridden by subclasses
    name: ClassVar[str] = "BaseNode"  # Unique internal name (matches class name often)
    display_name: ClassVar[str] = "Base Node"  # User-facing name
    description: ClassVar[str] = "Base description"
    category: ClassVar[str] = "Core"  # Category for UI grouping
    icon: ClassVar[str] = "Cog"  # Icon name (e.g., from an icon library)
    beta: ClassVar[bool] = False  # If the component is experimental
    requires_approval: ClassVar[bool] = (
        False  # Whether this component requires approval before execution
    )
    inputs: ClassVar[List[InputBase]] = []  # List of input definitions
    outputs: ClassVar[List[Output]] = []  # List of output definitions

    # Flag to indicate if this is an abstract base class that shouldn't be displayed in the UI
    is_abstract: ClassVar[bool] = False

    @classmethod
    def get_definition(cls) -> Dict[str, Any]:
        """
        Generates the JSON-serializable definition for the frontend.

        This method creates a dictionary representation of the component
        that can be sent to the frontend for display and configuration.

        Returns:
            A dictionary containing the component definition.
        """
        logger.debug(f"get_definition called for {cls.__name__} with category {cls.category}")

        # Skip abstract components
        if cls.is_abstract:
            logger.debug(f"Skipping abstract component {cls.__name__}")
            return None

        return {
            "name": cls.name,
            "display_name": cls.display_name,
            "description": cls.description,
            "category": cls.category,
            "icon": cls.icon,
            "beta": cls.beta,
            "requires_approval": cls.requires_approval,
            # Serialize Pydantic models to dicts
            "inputs": [inp.model_dump() for inp in cls.inputs],
            "outputs": [out.model_dump() for out in cls.outputs],
            "is_valid": True,  # Assume valid unless loading fails elsewhere
            "is_abstract": cls.is_abstract,  # Include the abstract flag
            # Path is often determined during discovery or by convention
            # Special handling for AI components
            "path": (
                f"components.ai.{cls.name.lower()}"
                if cls.category == "AI"
                else f"components.{cls.category.lower()}.{cls.name.lower()}"
            ),
        }

    # Legacy method for backward compatibility
    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy placeholder for node execution logic.

        DEPRECATED: This method is deprecated and will be removed in a future version.
        Please implement the execute method instead.

        This method is kept for backward compatibility and will be called by the
        execute method if implemented by subclasses.

        Args:
            **kwargs: Input values for the component.

        Returns:
            A dictionary with the component's outputs.
        """
        warnings.warn(
            f"The build method in {self.__class__.__name__} is deprecated and will be removed in a future version. "
            f"Please implement the execute method instead.",
            DeprecationWarning,
            stacklevel=2,
        )
        raise NotImplementedError(f"Build logic not implemented for {self.name}")

    @staticmethod
    def generate_dynamic_handles(
        base_name: str,
        base_display_name: str,
        count: int,
        input_types: Optional[List[str]] = None,
        required: bool = False,
        info: Optional[str] = None,
    ) -> List[InputBase]:
        """
        Generate a list of handle inputs with sequential names.

        Args:
            base_name: The base name for the handles (e.g., "input" -> "input_1", "input_2", etc.)
            base_display_name: The base display name for the handles
            count: The number of handles to generate
            input_types: The types of inputs this handle accepts
            required: Whether the input is required
            info: Description of the input

        Returns:
            A list of HandleInput objects
        """
        handles = []
        for i in range(1, count + 1):
            handles.append(
                HandleInput(
                    name=f"{base_name}_{i}",
                    display_name=f"{base_display_name} {i}",
                    required=required,
                    is_handle=True,
                    input_types=input_types or ["Any"],
                    info=info or f"Connect input {i}",
                )
            )
        return handles

    @staticmethod
    def get_dynamic_handle_inputs(inputs: Dict[str, Any], base_name: str) -> List[Any]:
        """
        Extract values from dynamic handle inputs.

        Args:
            inputs: The input dictionary
            base_name: The base name for the handles (e.g., "input" -> "input_1", "input_2", etc.)

        Returns:
            A list of values from the dynamic handles, excluding None values
        """
        values = []
        i = 1
        while True:
            handle_name = f"{base_name}_{i}"
            if handle_name not in inputs:
                break
            value = inputs.get(handle_name)
            if value is not None:
                values.append(value)
            i += 1
        return values

    def __init_subclass__(cls, **kwargs):
        """
        Initialize a subclass of BaseNode.

        This method is called when a new subclass of BaseNode is created.
        It performs validation and setup for the new class.

        Args:
            **kwargs: Additional arguments passed to the superclass.
        """
        super().__init_subclass__(**kwargs)

        # Set name based on class name if not overridden
        if cls.name == "BaseNode" and cls.__name__ != "BaseNode":
            cls.name = cls.__name__

        # Validate input and output names
        input_names = [inp.name for inp in cls.inputs]
        output_names = [out.name for out in cls.outputs]

        # Check for duplicate input names
        duplicate_inputs = [name for name in set(input_names) if input_names.count(name) > 1]
        if duplicate_inputs:
            warnings.warn(
                f"Component {cls.__name__} has duplicate input names: {duplicate_inputs}",
                UserWarning,
                stacklevel=2,
            )

        # Check for duplicate output names
        duplicate_outputs = [name for name in set(output_names) if output_names.count(name) > 1]
        if duplicate_outputs:
            warnings.warn(
                f"Component {cls.__name__} has duplicate output names: {duplicate_outputs}",
                UserWarning,
                stacklevel=2,
            )
