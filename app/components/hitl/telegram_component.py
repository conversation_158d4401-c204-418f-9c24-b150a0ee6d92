from typing import Dict, Any, List, ClassVar

from app.components.hitl.base_hitl_component import (
    BaseHITLComponent,
)
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    StringInput,
    MultilineInput,
    BoolInput,
)
from app.models.workflow_builder.components import Output


class TelegramComponent(BaseHITLComponent):
    """
    Sends messages via Telegram Bot API.

    This component allows workflows to send messages to Telegram chats,
    optionally waiting for a reply. It uses the Telegram Bot API to send
    messages and can monitor for replies.
    """

    name: ClassVar[str] = "TelegramComponent"
    display_name: ClassVar[str] = "Send Telegram Message"
    description: ClassVar[str] = (
        "Sends a message to a Telegram chat and optionally waits for a reply."
    )

    icon: ClassVar[str] = "Send"  # Using a generic send icon as Telegram might not be available

    inputs: ClassVar[List[InputBase]] = [
        # Inherit input_data and timeout_seconds from base class
        # Chat ID - connection handle
        HandleInput(
            name="chat_id_handle",
            display_name="Chat ID",
            is_handle=True,
            input_types=["string", "int"],
            info="Connect the Telegram chat ID to send the message to.",
        ),
        # Chat ID - direct input in inspector
        StringInput(
            name="chat_id",
            display_name="Chat ID (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The Telegram chat ID to send the message to. Used if no connection is provided.",
        ),
        # Message - connection handle (inherited from base but overridden for clarity)
        HandleInput(
            name="message_handle",
            display_name="Message",
            is_handle=True,
            input_types=["string"],
            info="Connect the message content to send to Telegram.",
        ),
        # Message - direct input in inspector (overridden from base)
        MultilineInput(
            name="message",
            display_name="Message (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The message content to send to Telegram. Used if no connection is provided.",
        ),
        # Wait for reply
        BoolInput(
            name="wait_for_reply",
            display_name="Wait for Reply",
            required=False,
            is_handle=False,
            value=False,
            info="If enabled, the component will wait for a reply in the chat before continuing.",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        # Inherit response_data, timed_out, and error from base class
        Output(
            name="message_id",
            display_name="Message ID",
            output_type="string",
            # "The unique identifier of the sent Telegram message."
        ),
        Output(
            name="sent_status",
            display_name="Sent Status",
            output_type="bool",
            # "True if the message was sent successfully, False otherwise."
        ),
        # Override action_taken from base class for clarity
        Output(
            name="action_taken",
            display_name="Action Taken",
            output_type="string",
            # "The action taken by the component, e.g., 'SENT' or 'FAILED'."
        ),
    ]

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Sends a Telegram message and optionally waits for a reply.

        Args:
            **kwargs: Contains the input values:
                - chat_id_handle: Connected Telegram chat ID
                - chat_id: Direct input Telegram chat ID
                - message_handle: Connected message content
                - message: Direct input message content
                - wait_for_reply: Whether to wait for a reply
                - timeout_seconds: Maximum time to wait for a reply
                - input_data: Additional data for context

        Returns:
            A dictionary with:
                - message_id: The ID of the sent Telegram message
                - sent_status: Whether the message was sent successfully
                - response_data: The reply message content (if wait_for_reply is True)
                - timed_out: Whether waiting for a reply timed out
                - error: Any error message
                - action_taken: The action taken ('SENT', 'FAILED', etc.)
        """
        self.log("info", "Executing TelegramComponent...")

        # Initialize output variables
        message_id = None
        sent_status = False
        response_data = None
        timed_out = False
        error_msg = None
        action_taken = "PENDING"

        # Get inputs - prioritize handle inputs over direct inputs
        chat_id_handle = kwargs.get("chat_id_handle")
        chat_id_direct = kwargs.get("chat_id")
        message_handle = kwargs.get("message_handle")
        message_direct = kwargs.get("message")
        wait_for_reply = kwargs.get("wait_for_reply", False)
        timeout_seconds = kwargs.get("timeout_seconds", 3600)

        # Process inputs - prioritize handle inputs over direct inputs
        chat_id = chat_id_handle if chat_id_handle is not None else chat_id_direct
        message_text = message_handle if message_handle is not None else message_direct

        # Validate inputs
        if not chat_id:
            error_msg = "Telegram chat ID is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        if not message_text:
            error_msg = "Message content is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        try:
            # Log attempt
            self.log("info", f"Attempting to send Telegram message to chat {chat_id}")

            # Placeholder: Get Telegram Bot Token
            # bot_token = self.get_config("TELEGRAM_BOT_TOKEN")

            # Placeholder: Construct API URL
            # api_url = f"https://api.telegram.org/bot{bot_token}/sendMessage"

            # Placeholder: Send message to Telegram
            # import requests
            # response = requests.post(
            #     api_url,
            #     data={
            #         "chat_id": chat_id,
            #         "text": message_text
            #     }
            # )

            # Placeholder: Check if message was sent successfully
            # response_json = response.json()
            # if response_json.get("ok"):
            #     message_id = str(response_json["result"]["message_id"])
            #     sent_status = True
            #     action_taken = "SENT"
            #     self.log("info", f"Telegram message sent successfully with ID {message_id}")
            # else:
            #     raise Exception(f"Failed to send message: {response_json.get('description', 'Unknown error')}")

            # For demonstration, simulate successful sending
            message_id = "12345"
            sent_status = True
            action_taken = "SENT"
            self.log("info", "Telegram message sent successfully")

            # Placeholder: If wait_for_reply is True, implement waiting logic
            if wait_for_reply:
                self.log("info", f"Waiting for reply (timeout: {timeout_seconds}s)")

                # Placeholder: Complex monitoring logic would be needed here
                # This could involve:
                # 1. Polling the getUpdates method
                # 2. Setting up a webhook
                # 3. Filtering messages by chat ID and message thread

                # For demonstration, simulate no reply received
                response_data = None
                timed_out = True
                action_taken = "TIMED_OUT"
                self.log("info", "Waiting for reply timed out")

        except Exception as e:
            error_msg = f"Failed to send Telegram message: {str(e)}"
            self.log("error", error_msg)
            sent_status = False
            action_taken = "FAILED"

        # Return all outputs
        return {
            "message_id": message_id,
            "sent_status": sent_status,
            "response_data": response_data,
            "timed_out": timed_out,
            "error": error_msg,
            "action_taken": action_taken,
        }
