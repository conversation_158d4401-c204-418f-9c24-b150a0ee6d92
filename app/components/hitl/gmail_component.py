from typing import Dict, Any, List, ClassVar

from app.components.hitl.base_hitl_component import (
    BaseHITLComponent,
)
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    StringInput,
    MultilineInput,
    BoolInput,
)
from app.models.workflow_builder.components import Output


class GmailComponent(BaseHITLComponent):
    """
    Sends email specifically via Gmail API.

    This component allows workflows to send emails using the Gmail API,
    optionally waiting for a reply. It provides more Gmail-specific features
    compared to the generic EmailComponent.
    """

    name: ClassVar[str] = "GmailComponent"
    display_name: ClassVar[str] = "Send Gmail"
    description: ClassVar[str] = "Sends an email via Gmail API and optionally waits for a reply."

    icon: ClassVar[str] = "Mail"  # Using a generic mail icon as Gmail might not be available

    inputs: ClassVar[List[InputBase]] = [
        # Inherit input_data and timeout_seconds from base class
        # Recipient - connection handle
        HandleInput(
            name="recipient_handle",
            display_name="Recipient Email(s)",
            is_handle=True,
            input_types=["string"],
            info="Connect the recipient email address(es). Multiple addresses can be separated by commas.",
        ),
        # Recipient - direct input in inspector
        StringInput(
            name="recipient",
            display_name="Recipient Email(s) (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The recipient email address(es). Multiple addresses can be separated by commas. Used if no connection is provided.",
        ),
        # Subject - connection handle
        HandleInput(
            name="subject_handle",
            display_name="Subject",
            is_handle=True,
            input_types=["string"],
            info="Connect the email subject.",
        ),
        # Subject - direct input in inspector
        StringInput(
            name="subject",
            display_name="Subject (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The email subject. Used if no connection is provided.",
        ),
        # Message - connection handle (inherited from base but overridden for clarity)
        HandleInput(
            name="message_handle",
            display_name="Email Body",
            is_handle=True,
            input_types=["string"],
            info="Connect the email body content.",
        ),
        # Message - direct input in inspector (overridden from base)
        MultilineInput(
            name="message",
            display_name="Email Body (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The email body content. Used if no connection is provided.",
        ),
        # Wait for reply
        BoolInput(
            name="wait_for_reply",
            display_name="Wait for Reply",
            required=False,
            is_handle=False,
            value=False,
            info="If enabled, the component will wait for a reply email before continuing.",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        # Inherit response_data, timed_out, and error from base class
        Output(
            name="message_id",
            display_name="Message ID",
            output_type="string",
            # "The unique identifier of the sent email in Gmail."
        ),
        Output(
            name="sent_status",
            display_name="Sent Status",
            output_type="bool",
            # "True if the email was sent successfully, False otherwise."
        ),
        # Override action_taken from base class for clarity
        Output(
            name="action_taken",
            display_name="Action Taken",
            output_type="string",
            # "The action taken by the component, e.g., 'SENT' or 'FAILED'."
        ),
    ]

    def _check_google_api_installed(self) -> bool:
        """
        Checks if the required Google API packages are installed.

        Returns:
            True if the required packages are installed, False otherwise.
        """
        try:
            import googleapiclient.discovery
            import google.auth

            return True
        except ImportError:
            return False

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Sends an email via Gmail API and optionally waits for a reply.

        Args:
            **kwargs: Contains the input values:
                - recipient_handle: Connected recipient email address(es)
                - recipient: Direct input recipient email address(es)
                - subject_handle: Connected email subject
                - subject: Direct input email subject
                - message_handle: Connected email body
                - message: Direct input email body
                - wait_for_reply: Whether to wait for a reply
                - timeout_seconds: Maximum time to wait for a reply
                - input_data: Additional data for context

        Returns:
            A dictionary with:
                - message_id: The ID of the sent email in Gmail
                - sent_status: Whether the email was sent successfully
                - response_data: The reply email content (if wait_for_reply is True)
                - timed_out: Whether waiting for a reply timed out
                - error: Any error message
                - action_taken: The action taken ('SENT', 'FAILED', etc.)
        """
        self.log("info", "Executing GmailComponent...")

        # Check if required packages are installed
        if not self._check_google_api_installed():
            error_msg = "The required Google API packages are not installed. Please install them with 'pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib'."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        # Initialize output variables
        message_id = None
        sent_status = False
        response_data = None
        timed_out = False
        error_msg = None
        action_taken = "PENDING"

        # Get inputs - prioritize handle inputs over direct inputs
        recipient_handle = kwargs.get("recipient_handle")
        recipient_direct = kwargs.get("recipient")
        subject_handle = kwargs.get("subject_handle")
        subject_direct = kwargs.get("subject")
        message_handle = kwargs.get("message_handle")
        message_direct = kwargs.get("message")
        wait_for_reply = kwargs.get("wait_for_reply", False)
        timeout_seconds = kwargs.get("timeout_seconds", 3600)

        # Process inputs - prioritize handle inputs over direct inputs
        recipient = recipient_handle if recipient_handle is not None else recipient_direct
        subject = subject_handle if subject_handle is not None else subject_direct
        message_body = message_handle if message_handle is not None else message_direct

        # Validate inputs
        if not recipient:
            error_msg = "Recipient email address is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        if not subject:
            error_msg = "Email subject is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        if not message_body:
            error_msg = "Email body is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        try:
            # Log attempt
            self.log("info", f"Attempting to send Gmail to {recipient}")

            # Placeholder: Get Gmail API credentials
            # credentials_path = self.get_config("GMAIL_CREDENTIALS_PATH")

            # Placeholder: Perform Google API OAuth 2.0 authentication
            # import os.path
            # from google.auth.transport.requests import Request
            # from google.oauth2.credentials import Credentials
            # from google_auth_oauthlib.flow import InstalledAppFlow
            # from googleapiclient.discovery import build
            # from googleapiclient.errors import HttpError
            # import base64
            # from email.message import EmailMessage

            # SCOPES = ['https://www.googleapis.com/auth/gmail.send']
            # creds = None

            # if os.path.exists('token.json'):
            #     creds = Credentials.from_authorized_user_file('token.json', SCOPES)

            # if not creds or not creds.valid:
            #     if creds and creds.expired and creds.refresh_token:
            #         creds.refresh(Request())
            #     else:
            #         flow = InstalledAppFlow.from_client_secrets_file(credentials_path, SCOPES)
            #         creds = flow.run_local_server(port=0)
            #     with open('token.json', 'w') as token:
            #         token.write(creds.to_json())

            # Placeholder: Build Gmail service
            # service = build('gmail', 'v1', credentials=creds)

            # Placeholder: Create email message
            # message = EmailMessage()
            # message['To'] = recipient
            # message['Subject'] = subject
            # message.set_content(message_body)

            # Placeholder: Encode message
            # encoded_message = base64.urlsafe_b64encode(message.as_bytes()).decode()

            # Placeholder: Send message
            # send_message = service.users().messages().send(
            #     userId='me',
            #     body={'raw': encoded_message}
            # ).execute()

            # message_id = send_message['id']

            # For demonstration, simulate successful sending
            message_id = "18abc123def456gh"
            sent_status = True
            action_taken = "SENT"
            self.log("info", "Gmail sent successfully")

            # Placeholder: If wait_for_reply is True, implement waiting logic
            if wait_for_reply:
                self.log("info", f"Waiting for reply (timeout: {timeout_seconds}s)")

                # Placeholder: Complex monitoring logic would be needed here
                # This could involve:
                # 1. Polling Gmail API for new messages
                # 2. Using Gmail's push notifications via Google Cloud Pub/Sub
                # 3. Filtering messages by thread ID or subject

                # For demonstration, simulate no reply received
                response_data = None
                timed_out = True
                action_taken = "TIMED_OUT"
                self.log("info", "Waiting for reply timed out")

        except Exception as e:
            error_msg = f"Failed to send Gmail: {str(e)}"
            self.log("error", error_msg)
            sent_status = False
            action_taken = "FAILED"

        # Return all outputs
        return {
            "message_id": message_id,
            "sent_status": sent_status,
            "response_data": response_data,
            "timed_out": timed_out,
            "error": error_msg,
            "action_taken": action_taken,
        }
