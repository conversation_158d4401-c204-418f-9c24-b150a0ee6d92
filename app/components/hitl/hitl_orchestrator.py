from typing import Dict, Any, List, ClassVar
import time

from app.components.hitl.base_hitl_component import (
    BaseHITLComponent,
)
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    StringInput,
    MultilineInput,
    IntInput,
    ListInput,
)
from app.models.workflow_builder.components import Output


class HITLOrchestrator(BaseHITLComponent):
    """
    Manages tasks on an external HITL platform.

    This component allows workflows to create and manage human tasks on an external
    HITL platform. It can create tasks, assign them to specific users, and wait for
    their completion. The component supports structured response schemas for collecting
    specific data from humans.
    """

    name: ClassVar[str] = "HITLOrchestrator"
    display_name: ClassVar[str] = "Human Task Orchestrator"
    description: ClassVar[str] = "Creates and manages human tasks on an external HITL platform."

    icon: ClassVar[str] = "Users"
    beta: ClassVar[bool] = True

    inputs: ClassVar[List[InputBase]] = [
        # Task Data - connection handle
        HandleInput(
            name="task_data_handle",
            display_name="Task Data",
            required=True,
            is_handle=True,
            input_types=["dict", "Any"],
            info="Connect structured data to be included with the task.",
        ),
        # Task Title - connection handle
        HandleInput(
            name="task_title_handle",
            display_name="Task Title",
            is_handle=True,
            input_types=["string"],
            info="Connect the title of the task.",
        ),
        # Task Title - direct input in inspector
        StringInput(
            name="task_title",
            display_name="Task Title (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The title of the task. Used if no connection is provided.",
        ),
        # Instructions - connection handle (overrides base message)
        HandleInput(
            name="instructions_handle",
            display_name="Instructions",
            is_handle=True,
            input_types=["string"],
            info="Connect detailed instructions for the human to complete the task.",
        ),
        # Instructions - direct input in inspector (overrides base message)
        MultilineInput(
            name="instructions",
            display_name="Instructions (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="Detailed instructions for the human to complete the task. Used if no connection is provided.",
        ),
        # Assignee - connection handle
        HandleInput(
            name="assignee_handle",
            display_name="Assignee",
            is_handle=True,
            input_types=["string"],
            info="Connect the username or ID of the person to assign the task to.",
        ),
        # Assignee - direct input in inspector
        StringInput(
            name="assignee",
            display_name="Assignee (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The username or ID of the person to assign the task to. Leave empty for unassigned tasks. Used if no connection is provided.",
        ),
        # Response Schema - connection handle
        HandleInput(
            name="response_schema_handle",
            display_name="Response Schema",
            is_handle=True,
            input_types=["list", "dict"],
            info="Connect a schema defining the structure of the expected response.",
        ),
        # Response Schema - direct input in inspector
        ListInput(
            name="response_schema",
            display_name="Response Schema (Direct)",
            required=False,
            is_handle=False,
            value=[],
            info="A schema defining the structure of the expected response. Used if no connection is provided.",
        ),
        # Timeout - direct input in inspector (override base with longer default)
        IntInput(
            name="timeout_seconds",
            display_name="Timeout (s)",
            required=False,
            is_handle=False,
            value=86400,  # 24 hours
            info="Maximum time (in seconds) to wait for the task to be completed.",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(
            name="task_id",
            display_name="Task ID",
            output_type="string",
            # "The unique identifier of the created task."
        ),
        # Override response_data from base class for clarity
        Output(
            name="response_data",
            display_name="Human Response",
            output_type="dict",
            # "The structured response data provided by the human."
        ),
        # Override action_taken from base class for clarity
        Output(
            name="action_taken",
            display_name="Action/Status",
            output_type="string",
            # "The final status of the task, e.g., 'COMPLETED', 'TIMED_OUT', 'FAILED'."
        ),
        # Inherit timed_out and error from base class
    ]

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Creates and manages a human task on an external HITL platform.

        Args:
            **kwargs: Contains the input values:
                - task_data_handle: Connected task data
                - task_title_handle: Connected task title
                - task_title: Direct input task title
                - instructions_handle: Connected instructions
                - instructions: Direct input instructions
                - assignee_handle: Connected assignee
                - assignee: Direct input assignee
                - response_schema_handle: Connected response schema
                - response_schema: Direct input response schema
                - timeout_seconds: Maximum time to wait for task completion

        Returns:
            A dictionary with:
                - task_id: The ID of the created task
                - response_data: The structured response data provided by the human
                - action_taken: The final status of the task
                - timed_out: Whether waiting for task completion timed out
                - error: Any error message
        """
        self.log("info", "Executing HITLOrchestrator...")

        # Initialize output variables
        task_id = None
        response_data = None
        action_taken = "PENDING"
        timed_out = False
        error_msg = None

        # Get inputs - prioritize handle inputs over direct inputs
        task_data = kwargs.get("task_data_handle")  # This is required
        task_title_handle = kwargs.get("task_title_handle")
        task_title_direct = kwargs.get("task_title")
        instructions_handle = kwargs.get("instructions_handle")
        instructions_direct = kwargs.get("instructions")
        assignee_handle = kwargs.get("assignee_handle")
        assignee_direct = kwargs.get("assignee")
        response_schema_handle = kwargs.get("response_schema_handle")
        response_schema_direct = kwargs.get("response_schema")
        timeout_seconds = kwargs.get("timeout_seconds", 86400)

        # Process inputs - prioritize handle inputs over direct inputs
        task_title = task_title_handle if task_title_handle is not None else task_title_direct
        instructions = (
            instructions_handle if instructions_handle is not None else instructions_direct
        )
        assignee = assignee_handle if assignee_handle is not None else assignee_direct
        response_schema = (
            response_schema_handle if response_schema_handle is not None else response_schema_direct
        )

        # Validate inputs
        if not task_data:
            error_msg = "Task data is missing. Please connect task data."
            self.log("error", error_msg)
            return {
                "task_id": None,
                "response_data": None,
                "action_taken": "FAILED",
                "timed_out": False,
                "error": error_msg,
            }

        if not task_title:
            error_msg = "Task title is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "task_id": None,
                "response_data": None,
                "action_taken": "FAILED",
                "timed_out": False,
                "error": error_msg,
            }

        if not instructions:
            error_msg = "Task instructions are missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "task_id": None,
                "response_data": None,
                "action_taken": "FAILED",
                "timed_out": False,
                "error": error_msg,
            }

        try:
            # Log attempt
            self.log("info", f"Creating human task: {task_title}")

            # Placeholder: Get HITL Platform API details
            # api_endpoint = self.get_config("HITL_PLATFORM_API_ENDPOINT")
            # api_key = self.get_config("HITL_PLATFORM_API_KEY")

            # Placeholder: Format task creation payload
            # task_payload = {
            #     "title": task_title,
            #     "instructions": instructions,
            #     "data": task_data,
            #     "response_schema": response_schema
            # }
            # if assignee:
            #     task_payload["assignee"] = assignee

            # Placeholder: Call platform's 'create task' API endpoint
            # import requests
            # headers = {
            #     "Authorization": f"Bearer {api_key}",
            #     "Content-Type": "application/json"
            # }
            # response = requests.post(
            #     f"{api_endpoint}/tasks",
            #     headers=headers,
            #     json=task_payload
            # )

            # Placeholder: Parse response to get task_id
            # if response.status_code == 201:
            #     response_json = response.json()
            #     task_id = response_json["id"]
            #     self.log("info", f"Task created successfully with ID {task_id}")
            # else:
            #     raise Exception(f"Failed to create task: {response.text}")

            # For demonstration, simulate successful task creation
            task_id = "task_12345"
            self.log("info", f"Task created successfully with ID {task_id}")

            # Placeholder: Monitor task status (polling)
            if task_id:
                self.log("info", f"Monitoring task status (timeout: {timeout_seconds}s)")

                # Placeholder: Start polling loop
                # start_time = time.time()
                # poll_interval = 30  # seconds
                # while True:
                #     # Check if elapsed time exceeds timeout
                #     elapsed_time = time.time() - start_time
                #     if elapsed_time > timeout_seconds:
                #         timed_out = True
                #         action_taken = "TIMED_OUT"
                #         self.log("info", f"Task monitoring timed out after {elapsed_time:.1f} seconds")
                #         break
                #
                #     # Placeholder: Call platform's 'get task status' API
                #     # status_response = requests.get(
                #     #     f"{api_endpoint}/tasks/{task_id}",
                #     #     headers=headers
                #     # )
                #     #
                #     # if status_response.status_code == 200:
                #     #     task_status = status_response.json()
                #     #     status = task_status["status"]
                #     #
                #     #     if status == "COMPLETED":
                #     #         action_taken = "COMPLETED"
                #     #         response_data = task_status["response"]
                #     #         self.log("info", f"Task completed successfully")
                #     #         break
                #     #     elif status in ["FAILED", "CANCELLED", "REJECTED"]:
                #     #         action_taken = status
                #     #         error_msg = task_status.get("error_details", "Task was not completed successfully")
                #     #         self.log("warning", f"Task {status.lower()}: {error_msg}")
                #     #         break
                #     #     else:  # PENDING, IN_PROGRESS, etc.
                #     #         self.log("info", f"Task status: {status}")
                #     # else:
                #     #     self.log("warning", f"Failed to get task status: {status_response.text}")
                #
                #     # Wait before polling again
                #     # time.sleep(poll_interval)

                # For demonstration, simulate task completion
                # In a real implementation, this would be a polling loop
                action_taken = "COMPLETED"
                response_data = {
                    "approved": True,
                    "comments": "This looks good to me!",
                    "timestamp": "2023-07-15T14:30:45Z",
                }
                self.log("info", "Task completed successfully")

        except Exception as e:
            error_msg = f"Failed to manage human task: {str(e)}"
            self.log("error", error_msg)
            action_taken = "FAILED"

        # Return all outputs
        return {
            "task_id": task_id,
            "response_data": response_data,
            "action_taken": action_taken,
            "timed_out": timed_out,
            "error": error_msg,
        }
