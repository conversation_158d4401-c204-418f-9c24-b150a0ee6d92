from typing import Dict, Any, List, ClassVar

from app.components.hitl.base_hitl_component import (
    BaseHITLComponent,
)
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    StringInput,
    MultilineInput,
    BoolInput,
)
from app.models.workflow_builder.components import Output
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input


class WhatsAppComponent(BaseHITLComponent):
    """
    Sends WhatsApp messages via an API provider (e.g., Twilio).

    This component allows workflows to send messages to WhatsApp numbers,
    optionally waiting for a reply. It uses a third-party API provider
    (such as Twilio) to send WhatsApp messages.
    """

    name: ClassVar[str] = "WhatsAppComponent"
    display_name: ClassVar[str] = "Send WhatsApp Message"
    description: ClassVar[str] = (
        "Sends a message to a WhatsApp number via an API provider and optionally waits for a reply."
    )

    icon: ClassVar[str] = (
        "MessageSquare"  # Using a generic message icon as WhatsApp might not be available
    )

    inputs: ClassVar[List[InputBase]] = [
        # Inherit input_data and timeout_seconds from base class
        # Phone Number - single input that can be both connected and directly edited
        create_dual_purpose_input(
            name="phone_number",
            display_name="Recipient Phone Number",
            input_type="string",
            required=True,
            info="The recipient's phone number in E.164 format (e.g., +**********). Can be connected from another node or entered directly.",
            input_types=["string"],
        ),
        # Message - single input that can be both connected and directly edited
        create_dual_purpose_input(
            name="message",
            display_name="Message",
            input_type="multiline",
            required=True,
            info="The message content to send to WhatsApp. Can be connected from another node or entered directly.",
            input_types=["string"],
        ),
        # Wait for reply
        BoolInput(
            name="wait_for_reply",
            display_name="Wait for Reply",
            required=False,
            is_handle=False,
            value=False,
            info="If enabled, the component will wait for a reply before continuing.",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        # Inherit response_data, timed_out, and error from base class
        Output(
            name="message_id",
            display_name="Message ID",
            output_type="string",
            # "The unique identifier of the sent WhatsApp message (provider SID/ID)."
        ),
        Output(
            name="sent_status",
            display_name="Sent Status",
            output_type="bool",
            # "True if the message was sent successfully, False otherwise."
        ),
        # Override action_taken from base class for clarity
        Output(
            name="action_taken",
            display_name="Action Taken",
            output_type="string",
            # "The action taken by the component, e.g., 'SENT', 'QUEUED', or 'FAILED'."
        ),
    ]

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Sends a WhatsApp message and optionally waits for a reply.

        Args:
            **kwargs: Contains the input values:
                - phone_number_handle: Connected recipient phone number
                - phone_number: Direct input recipient phone number
                - message_handle: Connected message content
                - message: Direct input message content
                - wait_for_reply: Whether to wait for a reply
                - timeout_seconds: Maximum time to wait for a reply
                - input_data: Additional data for context

        Returns:
            A dictionary with:
                - message_id: The ID of the sent WhatsApp message (provider SID/ID)
                - sent_status: Whether the message was sent successfully
                - response_data: The reply message content (if wait_for_reply is True)
                - timed_out: Whether waiting for a reply timed out
                - error: Any error message
                - action_taken: The action taken ('SENT', 'QUEUED', 'FAILED', etc.)
        """
        self.log("info", "Executing WhatsAppComponent...")

        # Initialize output variables
        message_id = None
        sent_status = False
        response_data = None
        timed_out = False
        error_msg = None
        action_taken = "PENDING"

        # Get inputs
        phone_number = kwargs.get("phone_number", "")
        message_text = kwargs.get("message", "")
        wait_for_reply = kwargs.get("wait_for_reply", False)
        timeout_seconds = kwargs.get("timeout_seconds", 3600)

        # Validate inputs
        if not phone_number:
            error_msg = "Recipient phone number is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        if not message_text:
            error_msg = "Message content is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        # Validate phone number format (basic E.164 check)
        if not phone_number.startswith("+"):
            error_msg = "Phone number must be in E.164 format (e.g., +**********)."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        try:
            # Log attempt
            self.log("info", f"Attempting to send WhatsApp message to {phone_number} via Twilio")

            # Placeholder: Get Twilio credentials
            # account_sid = self.get_config("TWILIO_ACCOUNT_SID")
            # auth_token = self.get_config("TWILIO_AUTH_TOKEN")
            # from_number = self.get_config("TWILIO_WHATSAPP_NUMBER")

            # Placeholder: Format WhatsApp numbers
            # to_whatsapp = f"whatsapp:{phone_number}"
            # from_whatsapp = f"whatsapp:{from_number}"

            # Placeholder: Send message via Twilio
            # from twilio.rest import Client
            # client = Client(account_sid, auth_token)
            # message = client.messages.create(
            #     body=message_text,
            #     from_=from_whatsapp,
            #     to=to_whatsapp
            # )

            # message_id = message.sid
            # sent_status = True
            # action_taken = message.status.upper()  # e.g., 'QUEUED', 'SENT'
            # self.log("info", f"WhatsApp message sent successfully with SID {message_id} (status: {action_taken})")

            # For demonstration, simulate successful sending
            message_id = "SM123456789abcdef"
            sent_status = True
            action_taken = "QUEUED"  # Twilio often returns QUEUED initially
            self.log("info", "WhatsApp message queued successfully")

            # Placeholder: If wait_for_reply is True, implement waiting logic
            if wait_for_reply:
                self.log("info", f"Waiting for reply (timeout: {timeout_seconds}s)")

                # Placeholder: Complex monitoring logic would be needed here
                # This could involve:
                # 1. Setting up a webhook to receive incoming messages
                # 2. Polling Twilio's API for new messages
                # 3. Filtering messages by sender phone number

                # For demonstration, simulate no reply received
                response_data = None
                timed_out = True
                action_taken = "TIMED_OUT"
                self.log("info", "Waiting for reply timed out")

        except Exception as e:
            error_msg = f"Failed to send WhatsApp message: {str(e)}"
            self.log("error", error_msg)
            sent_status = False
            action_taken = "FAILED"

        # Return all outputs
        return {
            "message_id": message_id,
            "sent_status": sent_status,
            "response_data": response_data,
            "timed_out": timed_out,
            "error": error_msg,
            "action_taken": action_taken,
        }
