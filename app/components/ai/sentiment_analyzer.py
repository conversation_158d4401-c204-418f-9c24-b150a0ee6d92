from typing import Dict, Any, List, ClassVar
import importlib
import uuid
import warnings

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import InputBase, HandleInput, StringInput
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import <PERSON>deR<PERSON>ult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input
from app.constants.semantic_types import AI_ANALYSIS, AI_METADATA, ERROR_INFO


class SentimentAnalyzer(BaseAgentComponent):
    """
    Analyzes input text to determine its sentiment.

    This component evaluates the sentiment of the provided text and
    categorizes it as Positive, Negative, or Neutral.
    """

    name: ClassVar[str] = "SentimentAnalyzer"
    display_name: ClassVar[str] = "Sentiment Analyzer"
    description: ClassVar[str] = (
        "Analyzes text to determine its sentiment (Positive, Negative, Neutral)."
    )

    icon: ClassVar[str] = "BarChart"

    # Define component-specific inputs using dual-purpose pattern
    component_inputs: ClassVar[List[InputBase]] = [
        # Text to analyze - unified dual-purpose input
        create_dual_purpose_input(
            name="text",
            display_name="Text to Analyze",
            input_type="string",
            required=True,
            info="The text whose sentiment needs to be evaluated. Can be connected from another node or entered directly.",
            input_types=["string", "Any"],
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="sentiment", display_name="Sentiment", output_type="string", semantic_type=AI_ANALYSIS),
        Output(name="confidence", display_name="Confidence", output_type="float", semantic_type=AI_METADATA),
        Output(name="error", display_name="Error", output_type="str", semantic_type=ERROR_INFO),
    ]

    def _check_openai_installed(self) -> bool:
        """
        Checks if the openai package is installed.

        Returns:
            True if openai is installed, False otherwise.
        """
        try:
            importlib.import_module("openai")
            return True
        except ImportError:
            return False

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute sentiment analysis via node-executor-service.

        This is the modern execution method that uses the unified AI executor
        for consistent behavior and improved performance.

        Args:
            context: The workflow execution context

        Returns:
            NodeResult with sentiment analysis results or error information
        """
        context.log(f"Executing {self.name}...")

        try:
            # Extract inputs from context
            inputs = self._extract_component_inputs(context)

            # Validate required inputs
            validation_result = self._validate_inputs(inputs)
            if not validation_result.is_valid:
                return NodeResult.error(validation_result.error_message)

            # Build request payload for node-executor-service
            payload = {
                "component_type": "sentiment_analyzer",
                "model_provider": inputs.get("model_provider", "OpenAI"),
                "api_key": inputs.get("api_key"),
                "model_name": inputs.get("model_name", "gpt-4o"),
                "temperature": 0.3,  # Lower temperature for more deterministic analysis
                "base_url": inputs.get("base_url"),
                "component_inputs": self._build_component_inputs(inputs),
                "request_id": context.request_id or str(uuid.uuid4())
            }

            # Execute via node-executor-service
            result = await self._execute_via_node_executor(payload, context)

            # Process and return results
            if result.get("status") == "success":
                outputs = self._map_outputs(result.get("result", {}))
                return NodeResult.success(outputs)
            else:
                error_msg = result.get("error", "Sentiment analysis failed")
                context.log(f"Error: {error_msg}")
                return NodeResult.error(error_msg)

        except Exception as e:
            error_msg = f"Error executing {self.name}: {str(e)}"
            context.log(error_msg)
            return NodeResult.error(error_msg)

    def _extract_component_inputs(self, context: WorkflowContext) -> Dict[str, Any]:
        """Extract component inputs from workflow context."""
        return {
            # Model configuration
            "model_provider": self.get_input_value("model_provider", context, "OpenAI"),
            "api_key": self._extract_credential_value(self.get_input_value("api_key", context)),
            "model_name": self.get_input_value("model_name", context, "gpt-4o"),
            "base_url": self.get_input_value("base_url", context),

            # Input data - unified dual-purpose input
            "text": self.get_input_value("text", context, ""),
        }

    def _validate_inputs(self, inputs: Dict[str, Any]) -> "ValidationResult":
        """Validate component inputs."""
        from app.core_.base_component import ValidationResult

        # Check API key
        if not inputs.get("api_key"):
            return ValidationResult(
                is_valid=False,
                error_message="API key is required for sentiment analysis"
            )

        # Check text input
        text = inputs.get("text", "")
        if not text:
            return ValidationResult(
                is_valid=False,
                error_message="Text to analyze is required. Please connect text or provide it directly."
            )

        return ValidationResult(is_valid=True)

    def _build_component_inputs(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Build component-specific inputs for executor."""
        return {
            "text": inputs.get("text", "")
        }

    def _map_outputs(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Map executor results to component outputs."""
        return {
            "sentiment": result.get("sentiment", "Unknown"),
            "confidence": result.get("confidence", 0.0),
            "error": result.get("error")
        }

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the SentimentAnalyzer.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Analyzes the sentiment of the provided text.

        Args:
            **kwargs: Contains the input values:
                - text: The text to analyze
                - api_key: API key for the LLM service
                - model_name: Model to use
                - temperature: Temperature for generation

        Returns:
            A dictionary with:
                - sentiment: The detected sentiment (Positive, Negative, or Neutral)
                - confidence: A confidence score between 0 and 1
                - error: An error message if the operation failed
        """
        warnings.warn(
            f"The build method for {self.name} is deprecated and will be removed in a future version. "
            f"Please use the execute method instead for improved performance and consistency.",
            DeprecationWarning,
            stacklevel=2
        )
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        print(f"Executing {self.name}...")

        # Check if openai is installed
        if not self._check_openai_installed():
            return {
                "error": "The openai package is required but not installed. Please install it with 'pip install openai'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        model_name = kwargs.get("model_name", "gpt-3.5-turbo")
        temperature = kwargs.get(
            "temperature", 0.3
        )  # Lower temperature for more deterministic analysis

        text = kwargs.get("text", "")

        # Validate inputs
        if not text:
            return {
                "error": "Text to analyze is missing. Please connect text or provide it directly."
            }
        if not api_key:
            return {"error": "API key is required."}

        try:
            # Import openai
            import openai

            # Set API key and base URL if provided
            openai.api_key = api_key

            # Set base URL if provided and using custom provider
            if model_provider == "Custom" and base_url:
                openai.api_base = base_url
            elif model_provider == "Azure OpenAI":
                # For Azure, we need to set the API type and version
                openai.api_type = "azure"
                openai.api_version = "2023-05-15"
                if base_url:
                    openai.api_base = base_url

            # Create system prompt for sentiment analysis
            system_prompt = """You are a sentiment analysis assistant. Analyze the sentiment of the provided text and categorize it as one of the following:
1. Positive
2. Negative
3. Neutral

Also provide a confidence score between 0 and 1, where 1 is the highest confidence.
Format your response as a JSON object with two fields: "sentiment" and "confidence".
Example: {"sentiment": "Positive", "confidence": 0.85}"""

            # Create user prompt
            user_prompt = f"Text to analyze: {text}"

            # Make API call
            response = openai.ChatCompletion.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
            )

            # Extract content from response
            result_text = response.choices[0].message.content.strip()

            # Parse the JSON response
            import json

            try:
                result = json.loads(result_text)
                sentiment = result.get("sentiment", "Unknown")
                confidence = float(result.get("confidence", 0.0))

                print(
                    f"  Sentiment analysis completed successfully: {sentiment} (confidence: {confidence})"
                )
                return {"sentiment": sentiment, "confidence": confidence}
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract sentiment directly from text
                if "positive" in result_text.lower():
                    sentiment = "Positive"
                elif "negative" in result_text.lower():
                    sentiment = "Negative"
                else:
                    sentiment = "Neutral"

                print(f"  Sentiment analysis completed with fallback parsing: {sentiment}")
                return {
                    "sentiment": sentiment,
                    "confidence": 0.5,  # Default confidence when parsing fails
                }

        except Exception as e:
            error_msg = f"Error analyzing sentiment: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
