from typing import Dict, Any, List, ClassVar
import importlib
import uuid
import warnings

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import InputBase, HandleInput, StringInput
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input
from app.constants.semantic_types import AI_RESPONSE, ERROR_INFO


class QuestionAnswerModule(BaseAgentComponent):
    """
    Answers questions based on a provided context document.

    This component takes a question and a context document, then generates
    an answer based only on the information in the context.
    """

    name: ClassVar[str] = "QuestionAnswerModule"
    display_name: ClassVar[str] = "Question Answering"
    description: ClassVar[str] = "Answers questions based on a provided context document."

    icon: ClassVar[str] = "HelpCircle"

    # Define component-specific inputs using dual-purpose pattern
    component_inputs: ClassVar[List[InputBase]] = [
        # Question - unified dual-purpose input
        create_dual_purpose_input(
            name="question",
            display_name="Question",
            input_type="string",
            required=True,
            info="The question to be answered. Can be connected from another node or entered directly.",
            input_types=["string", "Any"],
        ),
        # Context - unified dual-purpose input
        create_dual_purpose_input(
            name="context",
            display_name="Context Document",
            input_type="string",
            required=True,
            info="The text containing the information needed to answer the question. Can be connected from another node or entered directly.",
            input_types=["string", "Any"],
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="answer", display_name="Answer", output_type="string", semantic_type=AI_RESPONSE),
        Output(name="error", display_name="Error", output_type="str", semantic_type=ERROR_INFO),
    ]

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """Execute question answering via node-executor-service."""
        context.log(f"Executing {self.name}...")

        try:
            # Extract inputs from context
            inputs = self._extract_component_inputs(context)

            # Validate required inputs
            validation_result = self._validate_inputs(inputs)
            if not validation_result.is_valid:
                return NodeResult.error(validation_result.error_message)

            # Build request payload for node-executor-service
            payload = {
                "component_type": "question_answer_module",
                "model_provider": inputs.get("model_provider", "OpenAI"),
                "api_key": inputs.get("api_key"),
                "model_name": inputs.get("model_name", "gpt-4o"),
                "temperature": 0.5,  # Moderate temperature for balanced responses
                "base_url": inputs.get("base_url"),
                "component_inputs": self._build_component_inputs(inputs),
                "request_id": context.request_id or str(uuid.uuid4())
            }

            # Execute via node-executor-service
            result = await self._execute_via_node_executor(payload, context)

            # Process and return results
            if result.get("status") == "success":
                outputs = self._map_outputs(result.get("result", {}))
                return NodeResult.success(outputs)
            else:
                error_msg = result.get("error", "Question answering failed")
                context.log(f"Error: {error_msg}")
                return NodeResult.error(error_msg)

        except Exception as e:
            error_msg = f"Error executing {self.name}: {str(e)}"
            context.log(error_msg)
            return NodeResult.error(error_msg)

    def _extract_component_inputs(self, context: WorkflowContext) -> Dict[str, Any]:
        """Extract component inputs from workflow context."""
        return {
            "model_provider": self.get_input_value("model_provider", context, "OpenAI"),
            "api_key": self._extract_credential_value(self.get_input_value("api_key", context)),
            "model_name": self.get_input_value("model_name", context, "gpt-4o"),
            "base_url": self.get_input_value("base_url", context),
            "question": self.get_input_value("question", context, ""),
            "context": self.get_input_value("context", context, ""),
        }

    def _validate_inputs(self, inputs: Dict[str, Any]) -> "ValidationResult":
        """Validate component inputs."""
        from app.core_.base_component import ValidationResult

        if not inputs.get("api_key"):
            return ValidationResult(is_valid=False, error_message="API key is required for question answering")

        question = inputs.get("question", "")
        if not question:
            return ValidationResult(is_valid=False, error_message="Question is required. Please connect a question or provide it directly.")

        context = inputs.get("context", "")
        if not context:
            return ValidationResult(is_valid=False, error_message="Context document is required. Please connect a context document or provide it directly.")

        return ValidationResult(is_valid=True)

    def _build_component_inputs(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Build component-specific inputs for executor."""
        return {
            "question": inputs.get("question", ""),
            "context": inputs.get("context", "")
        }

    def _map_outputs(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Map executor results to component outputs."""
        return {
            "answer": result.get("answer", ""),
            "error": result.get("error")
        }

    def _check_openai_installed(self) -> bool:
        """
        Checks if the openai package is installed.

        Returns:
            True if openai is installed, False otherwise.
        """
        try:
            importlib.import_module("openai")
            return True
        except ImportError:
            return False

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the QuestionAnswerModule.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Answers a question based on the provided context.

        Args:
            **kwargs: Contains the input values:
                - question: The question to be answered
                - context: The context document containing the information
                - api_key: API key for the LLM service
                - model_name: Model to use
                - temperature: Temperature for generation

        Returns:
            A dictionary with:
                - answer: The generated answer
                - error: An error message if the operation failed
        """
        warnings.warn(
            f"The build method for {self.name} is deprecated and will be removed in a future version. "
            f"Please use the execute method instead for improved performance and consistency.",
            DeprecationWarning,
            stacklevel=2
        )
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        print(f"Executing {self.name}...")

        # Check if openai is installed
        if not self._check_openai_installed():
            return {
                "error": "The openai package is required but not installed. Please install it with 'pip install openai'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        model_name = kwargs.get("model_name", "gpt-3.5-turbo")
        temperature = kwargs.get("temperature", 0.5)

        question = kwargs.get("question", "")
        context = kwargs.get("context", "")

        # Validate inputs
        if not question:
            return {
                "error": "Question is missing. Please connect a question or provide it directly."
            }
        if not context:
            return {
                "error": "Context document is missing. Please connect a context document or provide it directly."
            }
        if not api_key:
            return {"error": "API key is required."}

        try:
            # Import openai
            import openai

            # Set API key and base URL if provided
            openai.api_key = api_key

            # Set base URL if provided and using custom provider
            if model_provider == "Custom" and base_url:
                openai.api_base = base_url
            elif model_provider == "Azure OpenAI":
                # For Azure, we need to set the API type and version
                openai.api_type = "azure"
                openai.api_version = "2023-05-15"
                if base_url:
                    openai.api_base = base_url

            # Create system prompt for question answering
            system_prompt = "You are a question answering assistant. Answer the question based ONLY on the provided context. If the context doesn't contain the information needed to answer the question, say 'I don't have enough information to answer this question.'"

            # Create user prompt combining the question and context
            user_prompt = f"Context: {context}\n\nQuestion: {question}"

            # Make API call
            response = openai.ChatCompletion.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
            )

            # Extract content from response
            answer = response.choices[0].message.content.strip()

            print(f"  Question answered successfully.")
            return {"answer": answer}

        except Exception as e:
            error_msg = f"Error answering question: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
