from typing import Dict, Any, List, ClassVar
import importlib
import uuid
import warnings

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import InputBase, HandleInput, DictInput
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import <PERSON>deR<PERSON>ult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input
from app.constants.semantic_types import AI_RESPONSE, ERROR_INFO


class BasicLLMChain(BaseAgentComponent):
    """
    Runs a simple LLMChain with a prompt template.

    This component creates and runs a LangChain LLMChain that combines
    a language model with a prompt template.

    DEPRECATED: This component is deprecated and will be removed in a future version.
    Please use specific AI components (SentimentAnalyzer, Summarizer, Classifier, etc.) instead for better performance and reliability.
    """

    name: ClassVar[str] = "BasicLLMChain"
    display_name: ClassVar[str] = "LLM Chain (Basic)"
    description: ClassVar[str] = "Runs a simple LLMChain with a prompt template."

    icon: ClassVar[str] = "Chain"
    is_abstract: ClassVar[bool] = False  # Explicitly set to False to ensure it's not abstract

    # Use the component logger to log class loading

    print("DEBUG: BasicLLMChain class loaded")

    # Define component-specific inputs (ALL DEPRECATED)
    component_inputs: ClassVar[List[InputBase]] = [
        # Input variables - unified dual-purpose input (DEPRECATED)
        create_dual_purpose_input(
            name="input_variables",
            display_name="Input Variables (DEPRECATED)",
            input_type="dict",
            required=True,
            info="DEPRECATED: Dictionary of variables to fill the prompt template. Please use specific AI components instead.",
            input_types=["dict", "Any"],
            default_value={},
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="output_text", display_name="Generated Text", output_type="string", semantic_type=AI_RESPONSE),
        Output(name="full_response", display_name="Full Chain Response", output_type="dict", semantic_type=AI_RESPONSE),
        Output(name="error", display_name="Error", output_type="str", semantic_type=ERROR_INFO),
    ]

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute BasicLLMChain (DEPRECATED).

        This method is deprecated along with the entire BasicLLMChain component.
        Please use OpenAIModule with custom prompts instead.

        Args:
            context: The workflow execution context

        Returns:
            NodeResult with deprecation warning and error
        """
        warnings.warn(
            f"The {self.name} component is deprecated and will be removed in a future version. "
            f"Please use specific AI components (SentimentAnalyzer, Summarizer, Classifier, etc.) instead for better performance and reliability.",
            DeprecationWarning,
            stacklevel=2
        )

        context.log(f"WARNING: {self.name} is deprecated. Please use specific AI components instead.")

        return NodeResult.error(
            f"BasicLLMChain is deprecated. Please use specific AI components (SentimentAnalyzer, Summarizer, Classifier, InformationExtractor, QuestionAnswerModule) instead. "
            f"These provide better performance, reliability, and modern API support."
        )

    def _check_langchain_installed(self) -> bool:
        """
        Checks if the langchain package is installed.

        Returns:
            True if langchain is installed, False otherwise.
        """
        try:
            importlib.import_module("langchain")
            return True
        except ImportError:
            return False

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the BasicLLMChain.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Executes the LLM chain with the provided inputs.

        Args:
            **kwargs: Contains the input values:
                - llm: Language model object
                - prompt_template: Prompt template object
                - input_variables: Dictionary of variables for the prompt

        Returns:
            A dictionary with:
                - output_text: The generated text
                - full_response: The full response from the chain
                - error: An error message if the operation failed
        """
        warnings.warn(
            f"The {self.name} component and its build method are deprecated and will be removed in a future version. "
            f"Please use specific AI components (SentimentAnalyzer, Summarizer, Classifier, etc.) instead for better performance and reliability.",
            DeprecationWarning,
            stacklevel=2
        )
        print(
            f"WARNING: {self.name} is deprecated. Please use specific AI components instead."
        )
        print(f"Executing {self.name}...")

        # Check if langchain is installed
        if not self._check_langchain_installed():
            return {
                "error": "The langchain package is required but not installed. Please install it with 'pip install langchain'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        model_name = kwargs.get("model_name", "")

        # DEPRECATED: BasicLLMChain functionality removed
        return {"error": "BasicLLMChain is deprecated. Please use specific AI components (SentimentAnalyzer, Summarizer, Classifier, InformationExtractor, QuestionAnswerModule) instead."}
