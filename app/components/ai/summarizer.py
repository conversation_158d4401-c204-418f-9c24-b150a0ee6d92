from typing import Dict, Any, List, ClassVar
import importlib
import uuid
import warnings

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import InputBase, HandleInput, StringInput, IntInput
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeR<PERSON>ult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input
from app.constants.semantic_types import AI_RESPONSE, ERROR_INFO


class Summarizer(BaseAgentComponent):
    """
    Creates a concise summary of a longer piece of text.

    This component takes a longer text and generates a shorter summary
    that captures the key points.
    """

    name: ClassVar[str] = "Summarizer"
    display_name: ClassVar[str] = "Text Summarizer"
    description: ClassVar[str] = "Creates a concise summary of a longer piece of text."

    icon: ClassVar[str] = "FileText"

    # Define component-specific inputs using dual-purpose pattern
    component_inputs: ClassVar[List[InputBase]] = [
        # Text to summarize - unified dual-purpose input
        create_dual_purpose_input(
            name="text",
            display_name="Text to Summarize",
            input_type="string",
            required=True,
            info="The text that needs to be condensed. Can be connected from another node or entered directly.",
            input_types=["string", "Any"],
        ),
        # Max length - direct input in inspector
        IntInput(
            name="max_length",
            display_name="Max Summary Length",
            required=False,
            is_handle=False,
            value=200,
            info="Maximum length of the summary in words. Use 0 for automatic length.",
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="summary", display_name="Summary", output_type="string", semantic_type=AI_RESPONSE),
        Output(name="error", display_name="Error", output_type="str", semantic_type=ERROR_INFO),
    ]

    def _check_openai_installed(self) -> bool:
        """
        Checks if the openai package is installed.

        Returns:
            True if openai is installed, False otherwise.
        """
        try:
            importlib.import_module("openai")
            return True
        except ImportError:
            return False

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute text summarization via node-executor-service.

        This is the modern execution method that uses the unified AI executor
        for consistent behavior and improved performance.

        Args:
            context: The workflow execution context

        Returns:
            NodeResult with summarization results or error information
        """
        context.log(f"Executing {self.name}...")

        try:
            # Extract inputs from context
            inputs = self._extract_component_inputs(context)

            # Validate required inputs
            validation_result = self._validate_inputs(inputs)
            if not validation_result.is_valid:
                return NodeResult.error(validation_result.error_message)

            # Build request payload for node-executor-service
            payload = {
                "component_type": "summarizer",
                "model_provider": inputs.get("model_provider", "OpenAI"),
                "api_key": inputs.get("api_key"),
                "model_name": inputs.get("model_name", "gpt-4o"),
                "temperature": 0.5,  # Moderate temperature for balanced creativity
                "base_url": inputs.get("base_url"),
                "component_inputs": self._build_component_inputs(inputs),
                "request_id": context.request_id or str(uuid.uuid4())
            }

            # Execute via node-executor-service
            result = await self._execute_via_node_executor(payload, context)

            # Process and return results
            if result.get("status") == "success":
                outputs = self._map_outputs(result.get("result", {}))
                return NodeResult.success(outputs)
            else:
                error_msg = result.get("error", "Text summarization failed")
                context.log(f"Error: {error_msg}")
                return NodeResult.error(error_msg)

        except Exception as e:
            error_msg = f"Error executing {self.name}: {str(e)}"
            context.log(error_msg)
            return NodeResult.error(error_msg)

    def _extract_component_inputs(self, context: WorkflowContext) -> Dict[str, Any]:
        """Extract component inputs from workflow context."""
        return {
            # Model configuration
            "model_provider": self.get_input_value("model_provider", context, "OpenAI"),
            "api_key": self._extract_credential_value(self.get_input_value("api_key", context)),
            "model_name": self.get_input_value("model_name", context, "gpt-4o"),
            "base_url": self.get_input_value("base_url", context),

            # Input data - unified dual-purpose input
            "text": self.get_input_value("text", context, ""),
            "max_length": self.get_input_value("max_length", context, 200),
        }

    def _validate_inputs(self, inputs: Dict[str, Any]) -> "ValidationResult":
        """Validate component inputs."""
        from app.core_.base_component import ValidationResult

        # Check API key
        if not inputs.get("api_key"):
            return ValidationResult(
                is_valid=False,
                error_message="API key is required for text summarization"
            )

        # Check text input
        text = inputs.get("text", "")
        if not text:
            return ValidationResult(
                is_valid=False,
                error_message="Text to summarize is required. Please connect text or provide it directly."
            )

        return ValidationResult(is_valid=True)

    def _build_component_inputs(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Build component-specific inputs for executor."""
        text = inputs.get("text", "")
        max_length = inputs.get("max_length", 200)

        return {
            "text": text,
            "max_length": max_length if max_length > 0 else None
        }

    def _map_outputs(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Map executor results to component outputs."""
        return {
            "summary": result.get("summary", ""),
            "error": result.get("error")
        }

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the Summarizer.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Summarizes the provided text.

        Args:
            **kwargs: Contains the input values:
                - text: The text to summarize
                - max_length: Maximum length of the summary in words
                - api_key: API key for the LLM service
                - model_name: Model to use
                - temperature: Temperature for generation

        Returns:
            A dictionary with:
                - summary: The generated summary
                - error: An error message if the operation failed
        """
        warnings.warn(
            f"The build method for {self.name} is deprecated and will be removed in a future version. "
            f"Please use the execute method instead for improved performance and consistency.",
            DeprecationWarning,
            stacklevel=2
        )
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        print(f"Executing {self.name}...")

        # Check if openai is installed
        if not self._check_openai_installed():
            return {
                "error": "The openai package is required but not installed. Please install it with 'pip install openai'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        model_name = kwargs.get("model_name", "gpt-3.5-turbo")
        temperature = kwargs.get("temperature", 0.5)

        text = kwargs.get("text", "")
        max_length = kwargs.get("max_length", 200)

        # Validate inputs
        if not text:
            return {
                "error": "Text to summarize is missing. Please connect text or provide it directly."
            }
        if not api_key:
            return {"error": "API key is required."}

        try:
            # Import openai
            import openai

            # Set API key and base URL if provided
            openai.api_key = api_key

            # Set base URL if provided and using custom provider
            if model_provider == "Custom" and base_url:
                openai.api_base = base_url
            elif model_provider == "Azure OpenAI":
                # For Azure, we need to set the API type and version
                openai.api_type = "azure"
                openai.api_version = "2023-05-15"
                if base_url:
                    openai.api_base = base_url

            # Create system prompt for summarization
            system_prompt = "You are a text summarization assistant. Create a concise summary of the provided text that captures the key points."

            # Add length constraint if specified
            if max_length > 0:
                system_prompt += f" The summary should be no longer than {max_length} words."

            # Create user prompt
            user_prompt = f"Text to summarize: {text}"

            # Make API call
            response = openai.ChatCompletion.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
            )

            # Extract content from response
            summary = response.choices[0].message.content.strip()

            print(f"  Summarization completed successfully.")
            return {"summary": summary}

        except Exception as e:
            error_msg = f"Error summarizing text: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
