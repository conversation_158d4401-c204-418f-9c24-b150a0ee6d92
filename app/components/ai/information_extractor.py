from typing import Dict, Any, List, ClassVar
import importlib
import uuid
import warnings

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import InputBase, HandleInput, StringInput
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import <PERSON>deR<PERSON>ult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input
from app.constants.semantic_types import AI_EXTRACTION, ERROR_INFO


class InformationExtractor(BaseAgentComponent):
    """
    Extracts specific pieces of information from a given text based on a query or instruction.

    This component uses an LLM to extract structured information (like names, dates, structured data)
    from a given text based on a query or instruction.
    """

    name: ClassVar[str] = "InformationExtractor"
    display_name: ClassVar[str] = "Information Extractor"
    description: ClassVar[str] = "Extracts specific information from text based on a query."

    icon: ClassVar[str] = "FileSearch"

    # Define component-specific inputs using dual-purpose pattern
    component_inputs: ClassVar[List[InputBase]] = [
        # Source text - unified dual-purpose input
        create_dual_purpose_input(
            name="text",
            display_name="Source Text",
            input_type="string",
            required=True,
            info="The text from which to extract information. Can be connected from another node or entered directly.",
            input_types=["string", "Any"],
        ),
        # Query - unified dual-purpose input
        create_dual_purpose_input(
            name="query",
            display_name="Extraction Query",
            input_type="string",
            required=True,
            info="Specifies what information to extract (e.g., 'Extract email addresses', 'Find the customer name'). Can be connected from another node or entered directly.",
            input_types=["string", "Any"],
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="extracted_info", display_name="Extracted Information", output_type="string", semantic_type=AI_EXTRACTION),
        Output(name="error", display_name="Error", output_type="str", semantic_type=ERROR_INFO),
    ]

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """Execute information extraction via node-executor-service."""
        context.log(f"Executing {self.name}...")

        try:
            # Extract inputs from context
            inputs = self._extract_component_inputs(context)

            # Validate required inputs
            validation_result = self._validate_inputs(inputs)
            if not validation_result.is_valid:
                return NodeResult.error(validation_result.error_message)

            # Build request payload for node-executor-service
            payload = {
                "component_type": "information_extractor",
                "model_provider": inputs.get("model_provider", "OpenAI"),
                "api_key": inputs.get("api_key"),
                "model_name": inputs.get("model_name", "gpt-4o"),
                "temperature": 0.3,  # Lower temperature for more deterministic extraction
                "base_url": inputs.get("base_url"),
                "component_inputs": self._build_component_inputs(inputs),
                "request_id": context.request_id or str(uuid.uuid4())
            }

            # Execute via node-executor-service
            result = await self._execute_via_node_executor(payload, context)

            # Process and return results
            if result.get("status") == "success":
                outputs = self._map_outputs(result.get("result", {}))
                return NodeResult.success(outputs)
            else:
                error_msg = result.get("error", "Information extraction failed")
                context.log(f"Error: {error_msg}")
                return NodeResult.error(error_msg)

        except Exception as e:
            error_msg = f"Error executing {self.name}: {str(e)}"
            context.log(error_msg)
            return NodeResult.error(error_msg)

    def _extract_component_inputs(self, context: WorkflowContext) -> Dict[str, Any]:
        """Extract component inputs from workflow context."""
        return {
            "model_provider": self.get_input_value("model_provider", context, "OpenAI"),
            "api_key": self._extract_credential_value(self.get_input_value("api_key", context)),
            "model_name": self.get_input_value("model_name", context, "gpt-4o"),
            "base_url": self.get_input_value("base_url", context),
            "text": self.get_input_value("text", context, ""),
            "query": self.get_input_value("query", context, ""),
        }

    def _validate_inputs(self, inputs: Dict[str, Any]) -> "ValidationResult":
        """Validate component inputs."""
        from app.core_.base_component import ValidationResult

        if not inputs.get("api_key"):
            return ValidationResult(is_valid=False, error_message="API key is required for information extraction")

        text = inputs.get("text", "")
        if not text:
            return ValidationResult(is_valid=False, error_message="Source text is required. Please connect text or provide it directly.")

        query = inputs.get("query", "")
        if not query:
            return ValidationResult(is_valid=False, error_message="Extraction query is required. Please connect a query or provide it directly.")

        return ValidationResult(is_valid=True)

    def _build_component_inputs(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Build component-specific inputs for executor."""
        return {
            "text": inputs.get("text", ""),
            "query": inputs.get("query", "")
        }

    def _map_outputs(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Map executor results to component outputs."""
        return {
            "extracted_info": result.get("extracted_info", ""),
            "error": result.get("error")
        }

    def _check_openai_installed(self) -> bool:
        """
        Checks if the openai package is installed.

        Returns:
            True if openai is installed, False otherwise.
        """
        try:
            importlib.import_module("openai")
            return True
        except ImportError:
            return False

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the InformationExtractor.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Extracts information from text based on a query.

        Args:
            **kwargs: Contains the input values:
                - text: The source text from which to extract information
                - query: The query specifying what information to extract
                - api_key: API key for the LLM service
                - model_name: Model to use
                - temperature: Temperature for generation

        Returns:
            A dictionary with:
                - extracted_info: The extracted information
                - error: An error message if the operation failed
        """
        warnings.warn(
            f"The build method for {self.name} is deprecated and will be removed in a future version. "
            f"Please use the execute method instead for improved performance and consistency.",
            DeprecationWarning,
            stacklevel=2
        )
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        print(f"Executing {self.name}...")

        # Check if openai is installed
        if not self._check_openai_installed():
            return {
                "error": "The openai package is required but not installed. Please install it with 'pip install openai'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        model_name = kwargs.get("model_name", "gpt-3.5-turbo")
        temperature = kwargs.get(
            "temperature", 0.3
        )  # Lower temperature for more deterministic extraction

        text = kwargs.get("text", "")
        query = kwargs.get("query", "")

        # Validate inputs
        if not text:
            return {"error": "Source text is missing. Please connect text or provide it directly."}
        if not query:
            return {
                "error": "Extraction query is missing. Please connect a query or provide it directly."
            }
        if not api_key:
            return {"error": "API key is required."}

        try:
            # Import openai
            import openai

            # Set API key and base URL if provided
            openai.api_key = api_key

            # Set base URL if provided and using custom provider
            if model_provider == "Custom" and base_url:
                openai.api_base = base_url
            elif model_provider == "Azure OpenAI":
                # For Azure, we need to set the API type and version
                openai.api_type = "azure"
                openai.api_version = "2023-05-15"
                if base_url:
                    openai.api_base = base_url

            # Create system prompt for extraction
            system_prompt = "You are an information extraction assistant. Extract the requested information from the provided text. Only return the extracted information, nothing else."

            # Create user prompt combining the query and text
            user_prompt = f"Query: {query}\n\nText: {text}"

            # Make API call
            response = openai.ChatCompletion.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
            )

            # Extract content from response
            extracted_info = response.choices[0].message.content.strip()

            print(f"  Information extracted successfully.")
            return {"extracted_info": extracted_info}

        except Exception as e:
            error_msg = f"Error extracting information: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
