"""Components for the workflow builder.

This package contains all the components that can be used in workflows.
"""

# Import subpackages with error handling
try:
    from app.components import core
except ImportError as e:
    print(f"Warning: Could not import core components: {e}")

try:
    from app.components import io
except ImportError as e:
    print(f"Warning: Could not import io components: {e}")

try:
    from app.components import processing
except ImportError as e:
    print(f"Warning: Could not import processing components: {e}")

try:
    from app.components import ai
except ImportError as e:
    print(f"Warning: Could not import ai components: {e}")

try:
    from app.components import data_interaction
except ImportError as e:
    print(f"Warning: Could not import data_interaction components: {e}")

# try:
#     from app.components import tools
# except ImportError as e:
#     print(f"Warning: Could not import tools components: {e}")

try:
    from app.components import hitl
except ImportError as e:
    print(f"Warning: Could not import hitl components: {e}")

try:
    from app.components import helper
except ImportError as e:
    print(f"Warning: Could not import helper components: {e}")
