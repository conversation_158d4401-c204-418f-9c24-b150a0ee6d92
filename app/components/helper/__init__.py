"""
Helper components for workflow builder.

This package contains utility components that provide helper functionality
for workflows, such as ID generation, document extraction, etc.

NOTE: IDGeneratorComponent is enabled (is_abstract = False) for Redis key generation.
Other helper components may be disabled by setting is_abstract = True.
"""

from app.components.helper.id_generator import IDGeneratorComponent
from app.components.helper.doc_extractor import DocExtractorComponent

__all__ = [
    "IDGeneratorComponent",  # Enabled for Redis key generation (is_abstract = False)
    "DocExtractorComponent",  # Currently disabled (is_abstract = True)
]
