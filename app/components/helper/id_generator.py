from typing import Dict, Any, List, ClassVar
import uuid
import time
import random
import string
import asyncio
import logging

from app.utils.type_conversion import safe_int_convert

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    DropdownInput,
    IntInput,
    HandleInput,
    InputVisibilityRule,
)
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

# Set up logging
logger = logging.getLogger(__name__)


class IDGeneratorComponent(BaseNode):
    """
    Generates various types of unique identifiers.

    This component can generate different types of unique identifiers including:
    - UUIDv4: Standard universally unique identifier
    - Timestamp ID: Time-based identifier
    - Short ID: Configurable length alphanumeric identifier
    
    NOTE: This component is currently disabled from the frontend.
    To re-enable, set is_abstract = False.
    """

    name: ClassVar[str] = "IDGeneratorComponent"
    display_name: ClassVar[str] = "ID Generator"
    description: ClassVar[str] = (
        "Generates various types of unique identifiers (UUID, timestamp, short ID)."
    )

    category: ClassVar[str] = "Helpers"
    icon: ClassVar[str] = "Fingerprint"
    is_abstract: ClassVar[bool] = False  # Enabled for Redis key generation

    inputs: ClassVar[List[InputBase]] = [
        # Optional trigger input for flow control (not required)
        HandleInput(
            name="trigger",
            display_name="Trigger",
            input_types=["Any"],
            required=False,
            info="Optional input to control when ID generation occurs. Leave unconnected for immediate execution.",
        ),
        DropdownInput(
            name="id_type",
            display_name="ID Type",
            options=["UUIDv4", "Timestamp ID", "Short ID"],
            value="UUIDv4",
            info="The type of unique identifier to generate.",
        ),
        IntInput(
            name="short_id_length",
            display_name="Short ID Length",
            value=8,
            info="The length of the short ID (only used when ID Type is 'Short ID').",
            visibility_rules=[InputVisibilityRule(field_name="id_type", field_value="Short ID")],
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="unique_id", display_name="Unique ID", output_type="string"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get the value of an input from the context.

        This method handles dual-purpose inputs, retrieving the value from the context.

        Args:
            input_name: The name of the input.
            context: The workflow execution context.
            default: The default value to return if the input is not found.

        Returns:
            The value of the input, or the default value if not found.
        """
        # Get the current node ID from the context
        node_id = context.current_node_id
        if not node_id:
            logger.warning("No current node ID in context")
            return default

        # Check if there's a value in the node outputs
        node_outputs = context.node_outputs.get(node_id, {})
        if input_name in node_outputs:
            return node_outputs[input_name]

        # If not found, return the default
        return default

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute the IDGeneratorComponent.

        This method generates various types of unique identifiers based on the specified type.
        Perfect for generating Redis keys or any other unique identifier needs.

        Args:
            context: The workflow execution context containing input values.

        Returns:
            A NodeResult with the execution results.
        """
        # Start timing for performance measurement
        start_time = time.time()

        # Log execution start
        context.log(f"Executing {self.name}...")

        try:
            # Get inputs from context
            # Note: trigger input is optional and doesn't affect the logic, just flow control
            trigger = self.get_input_value("trigger", context)
            id_type = self.get_input_value("id_type", context, "UUIDv4")
            short_id_length = self.get_input_value("short_id_length", context, 8)

            # Convert short_id_length to int if needed
            short_id_length = safe_int_convert(short_id_length, 8)

            # Log input values for debugging
            logger.debug(f"Trigger received: {trigger is not None}")
            logger.debug(f"ID type: {id_type}")
            logger.debug(f"Short ID length: {short_id_length}")

            # Validate id_type
            valid_id_types = ["UUIDv4", "Timestamp ID", "Short ID"]
            if id_type not in valid_id_types:
                error_msg = f"Invalid ID type: {id_type}. Must be one of {valid_id_types}"
                context.log(f"  {error_msg}")
                return NodeResult.error(error_msg, time.time() - start_time)

            # Validate short_id_length if id_type is Short ID
            if id_type == "Short ID":
                if short_id_length <= 0 or short_id_length > 50:
                    error_msg = f"Short ID length must be between 1 and 50, got: {short_id_length}"
                    context.log(f"  {error_msg}")
                    return NodeResult.error(error_msg, time.time() - start_time)

            # Generate ID based on type
            unique_id = None
            if id_type == "UUIDv4":
                unique_id = str(uuid.uuid4())
                context.log(f"  Generated UUIDv4: {unique_id}")
                logger.info(f"Generated UUIDv4: {unique_id}")

            elif id_type == "Timestamp ID":
                # Generate millisecond timestamp
                unique_id = str(int(time.time() * 1000))
                context.log(f"  Generated Timestamp ID: {unique_id}")
                logger.info(f"Generated Timestamp ID: {unique_id}")

            elif id_type == "Short ID":
                # Generate random alphanumeric string
                chars = string.ascii_letters + string.digits
                unique_id = ''.join(random.choices(chars, k=short_id_length))
                context.log(f"  Generated Short ID (length {short_id_length}): {unique_id}")
                logger.info(f"Generated Short ID (length {short_id_length}): {unique_id}")

            # Log success
            execution_time = time.time() - start_time
            context.log(f"ID generation completed successfully. Time: {execution_time:.3f}s")
            logger.info(f"ID generation completed successfully. ID: {unique_id}, Time: {execution_time:.3f}s")

            # Return success result
            return NodeResult.success(
                outputs={"unique_id": unique_id},
                execution_time=execution_time
            )

        except Exception as e:
            # Log error
            error_msg = f"Error generating ID: {str(e)}"
            context.log(f"  {error_msg}")
            logger.error(f"{error_msg}", exc_info=True)

            # Return error result
            return NodeResult.error(
                error_message=error_msg,
                execution_time=time.time() - start_time
            )

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the IDGeneratorComponent.
        
        This method is deprecated and will be replaced by the execute method.
        It is kept for backward compatibility.
        
        Args:
            id_type: The type of ID to generate ("UUIDv4", "Timestamp ID", or "Short ID")
            short_id_length: The length of the short ID (only used when id_type is "Short ID")

        Returns:
            A dictionary with the generated unique ID and any error message.
        """
        logger.warning(f"Using legacy build method for {self.name}. Please update to use execute method.")
        print(f"Executing {self.name} using legacy build method...")
        # Get input values
        id_type = kwargs.get("id_type", "UUIDv4")
        short_id_length = safe_int_convert(kwargs.get("short_id_length", 8), 8)

        # Initialize result
        result = {"unique_id": None, "error": None}

        try:
            # Generate ID based on type
            if id_type == "UUIDv4":
                result["unique_id"] = str(uuid.uuid4())

            elif id_type == "Timestamp ID":
                # Generate millisecond timestamp
                result["unique_id"] = str(int(time.time() * 1000))

            elif id_type == "Short ID":
                # Validate short_id_length
                if short_id_length <= 0 or short_id_length > 50:
                    short_id_length = 8

                # Generate random alphanumeric string
                chars = string.ascii_letters + string.digits
                result["unique_id"] = "".join(random.choices(chars, k=short_id_length))

            else:
                raise ValueError(f"Unknown ID type: {id_type}")

        except Exception as e:
            result["error"] = str(e)

        return result
