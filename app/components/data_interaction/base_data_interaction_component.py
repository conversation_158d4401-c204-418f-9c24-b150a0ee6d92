from typing import Dict, Any, List, ClassVar
from abc import ABC, abstractmethod

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import InputBase, HandleInput, StringInput, DictInput
from app.models.workflow_builder.components import Output


class BaseDataInteractionComponent(BaseNode, ABC):
    """
    Abstract base class for all Data Interaction components.

    This class defines the common interface and functionality for components that
    interact with external data sources and APIs. Concrete Data Interaction components
    should inherit from this class and implement the build method.
    """

    # These should be overridden by subclasses
    name: ClassVar[str] = "BaseDataInteractionComponent"
    display_name: ClassVar[str] = "Base Data Interaction (Abstract)"
    description: ClassVar[str] = (
        "Abstract base class defining the common interface for data interaction components."
    )

    category: ClassVar[str] = "Data Interaction"
    icon: ClassVar[str] = "Database"

    # Flag to indicate this is an abstract base class that shouldn't be displayed in the UI
    is_abstract: ClassVar[bool] = True

    inputs: ClassVar[List[InputBase]] = [
        # Input data - connection handle
        HandleInput(
            name="input_data_handle",
            display_name="Input Data",
            is_handle=True,
            input_types=["Any"],
            info="Data to be sent to the external system.",
        ),
        # Input data - direct input in inspector
        DictInput(
            name="input_data",
            display_name="Input Data (Direct)",
            required=False,
            is_handle=False,
            value={},
            info="Data to be sent to the external system. Used if no connection is provided.",
        ),
        # URL/Endpoint - connection handle
        HandleInput(
            name="endpoint_handle",
            display_name="Endpoint/URL",
            is_handle=True,
            input_types=["string"],
            info="Connect the endpoint or URL for the external system.",
        ),
        # URL/Endpoint - direct input in inspector
        StringInput(
            name="endpoint",
            display_name="Endpoint/URL (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The endpoint or URL for the external system. Used if no connection is provided.",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="response_data", display_name="Response Data", output_type="Any"),
        Output(name="status_code", display_name="Status Code", output_type="int"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    @abstractmethod
    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Abstract method that must be implemented by concrete Data Interaction components.

        This method contains the main execution logic for the component, including
        sending data to external systems, receiving responses, and processing those responses.

        Args:
            **kwargs: Input values for the component.

        Returns:
            A dictionary with the component's outputs.
        """
        raise NotImplementedError("Subclasses must implement build method")
