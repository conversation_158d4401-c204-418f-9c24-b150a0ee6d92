from typing import Dict, Any, List, <PERSON>Var
import json
import os

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    StringInput,
    BoolInput,
    DropdownInput,
    HandleInput,
)
from app.models.workflow_builder.components import Output


class SaveToFileComponent(BaseNode):
    """
    Writes data to a file (text or JSON).

    This component takes input data and writes it to a file at the specified path.
    It can handle text strings, dictionaries, and lists, with automatic JSON serialization
    for complex data types.
    
    NOTE: This component is currently disabled from the frontend.
    To re-enable, set is_abstract = False.
    """

    name: ClassVar[str] = "SaveToFileComponent"
    display_name: ClassVar[str] = "Save To File"
    description: ClassVar[str] = "Writes data to a file (text or JSON)."
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Save"
    is_abstract: ClassVar[bool] = True  # Disabled from frontend - set to False to re-enable

    inputs: ClassVar[List[InputBase]] = [
        # Input data - connection handle
        HandleInput(
            name="input_data_handle",
            display_name="Data to Save",
            required=True,
            is_handle=True,
            input_types=["string", "dict", "list", "Any"],
            info="Connect data from another node to save to a file.",
        ),
        # File path - connection handle
        HandleInput(
            name="file_path_handle",
            display_name="File Path",
            required=True,
            is_handle=True,
            input_types=["string"],
            info="Connect a string containing the file path.",
        ),
        # File path - direct input in inspector
        StringInput(
            name="file_path",
            display_name="File Path (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The path where the file will be saved. Used if no connection is provided.",
        ),
        BoolInput(
            name="overwrite",
            display_name="Overwrite",
            value=True,
            info="If enabled, overwrites existing files. If disabled, returns an error if the file exists.",
        ),
        DropdownInput(
            name="encoding",
            display_name="Encoding",
            value="utf-8",
            options=["utf-8", "latin-1", "ascii"],
            info="The character encoding to use when writing the file.",
        ),
        BoolInput(
            name="create_dirs",
            display_name="Create Directories",
            value=True,
            info="If enabled, creates any missing directories in the file path.",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="file_path_out", display_name="File Path Saved", output_type="string"),
        Output(name="success", display_name="Success", output_type="bool"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Executes the file save operation.

        Args:
            **kwargs: Contains the input values:
                - input_data: The data to save
                - file_path: The path where to save the file
                - overwrite: Whether to overwrite existing files
                - encoding: The character encoding to use
                - create_dirs: Whether to create missing directories

        Returns:
            A dictionary with:
                - file_path_out: The path where the file was saved
                - success: True if the operation succeeded, False otherwise
                - error: An error message if the operation failed
        """
        print(f"Executing {self.name}...")

        # Get inputs - prioritize handle inputs over direct inputs
        input_data = kwargs.get("input_data_handle")
        file_path_handle = kwargs.get("file_path_handle")
        file_path_direct = kwargs.get("file_path")
        overwrite = kwargs.get("overwrite", True)
        encoding = kwargs.get("encoding", "utf-8")
        create_dirs = kwargs.get("create_dirs", True)

        # Process file path - prioritize handle input over direct input
        file_path = file_path_handle if file_path_handle is not None else file_path_direct

        # Validate inputs
        if input_data is None:
            return {
                "success": False,
                "error": "Input data is missing. Please connect data to save.",
            }

        if not file_path:
            return {"success": False, "error": "File path is missing. Please provide a file path."}

        # Check if file exists and handle overwrite setting
        if os.path.exists(file_path) and not overwrite:
            return {
                "success": False,
                "error": f"File already exists at {file_path} and overwrite is disabled.",
            }

        try:
            # Create directories if needed
            if create_dirs:
                directory = os.path.dirname(file_path)
                if directory and not os.path.exists(directory):
                    os.makedirs(directory)
                    print(f"  Created directory: {directory}")

            # Determine how to write the data based on its type
            if isinstance(input_data, (dict, list)):
                # Convert dict/list to JSON
                with open(file_path, "w", encoding=encoding) as f:
                    json.dump(input_data, f, indent=2)
                print(f"  Saved JSON data to {file_path}")
            else:
                # Write as plain text
                with open(file_path, "w", encoding=encoding) as f:
                    f.write(str(input_data))
                print(f"  Saved text data to {file_path}")

            return {"file_path_out": file_path, "success": True}

        except Exception as e:
            error_msg = f"Error saving file: {str(e)}"
            print(f"  {error_msg}")
            return {"success": False, "error": error_msg}
