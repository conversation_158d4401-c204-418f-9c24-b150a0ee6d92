from typing import List, ClassVar
import re

from app.utils.type_conversion import safe_int_convert

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import InputBase, StringInput, IntInput, BoolInput
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import <PERSON>deR<PERSON>ult


from app.utils.workflow_builder.input_helpers import create_dual_purpose_input


class SplitTextComponent(BaseNode):
    """
    Splits text into a list using a delimiter.

    This component takes an input text string and splits it into a list of strings
    using the specified delimiter.
    """

    name: ClassVar[str] = "SplitTextComponent"
    display_name: ClassVar[str] = "Split Text"
    description: ClassVar[str] = "Splits text into a list using a delimiter."
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Scissors"

    inputs: ClassVar[List[InputBase]] = [
        # Input text - single input that can be both connected and directly edited
        create_dual_purpose_input(
            name="input_text",
            display_name="Input Text",
            input_type="string",
            required=True,
            info="The text to split. Can be connected from another node or entered directly.",
            input_types=["string", "Any"],
        ),
        StringInput(
            name="delimiter",
            display_name="Delimiter",
            value=",",
            info="The character or string to split the text by.",
        ),
        IntInput(
            name="max_splits",
            display_name="Max Splits",
            value=-1,
            advanced=True,
            info="Maximum number of splits to perform. -1 means no limit.",
        ),
        BoolInput(
            name="include_delimiter",
            display_name="Include Delimiter",
            value=False,
            advanced=True,
            info="If enabled, the delimiter will be included at the end of each split part (except the last one).",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="output_list", display_name="Split List", output_type="list"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    async def execute(self, context: WorkflowContext, **kwargs) -> NodeResult:
        """
        Executes the split operation on the input text.

        Args:
            context: The workflow execution context
            **kwargs: Contains the input values:
                - input_text: The text to split
                - delimiter: The delimiter to split by
                - max_splits: Maximum number of splits to perform
                - include_delimiter: Whether to include the delimiter in the output

        Returns:
            A NodeResult with either:
                - output_list: The list of split strings
                - error: An error message if the operation failed
        """
        print(f"Executing {self.name}...")

        # Get inputs
        input_text = kwargs.get("input_text", "")
        delimiter = kwargs.get("delimiter", ",")
        max_splits = safe_int_convert(kwargs.get("max_splits", -1), -1)
        include_delimiter = kwargs.get("include_delimiter", False)

        # Validate input
        if input_text is None:
            return NodeResult(
                node_id=context.current_node_id,
                outputs={"error": "Input text is missing. Please connect text or provide it directly."},
                status="error"
            )

        # Convert to string if not already
        if not isinstance(input_text, str):
            try:
                input_text = str(input_text)
                print(f"  Converted input to string: {input_text}")
            except Exception as e:
                return NodeResult(
                    node_id=context.current_node_id,
                    outputs={"error": f"Failed to convert input to string: {str(e)}"},
                    status="error"
                )

        try:
            if include_delimiter:
                # Use regex to split and keep the delimiter
                if max_splits > 0:
                    # Split with limit
                    parts = []
                    remaining = input_text
                    for _ in range(max_splits):
                        if delimiter in remaining:
                            idx = remaining.find(delimiter)
                            parts.append(remaining[: idx + len(delimiter)])
                            remaining = remaining[idx + len(delimiter) :]
                        else:
                            break
                    if remaining:
                        parts.append(remaining)
                    result = parts
                else:
                    # Split without limit
                    pattern = f"(.*?{re.escape(delimiter)}|.+$)"
                    result = re.findall(pattern, input_text)
                    # Remove empty strings
                    result = [part for part in result if part]
            else:
                # Use standard split method
                result = input_text.split(delimiter, max_splits)

            print(f"  Text split successfully into {len(result)} parts.")
            return NodeResult(
                node_id=context.current_node_id,
                outputs={"output_list": result},
                status="success"
            )

        except Exception as e:
            error_msg = f"Error splitting text: {str(e)}"
            print(f"  {error_msg}")
            return NodeResult(
                node_id=context.current_node_id,
                outputs={"error": error_msg},
                status="error"
            )
