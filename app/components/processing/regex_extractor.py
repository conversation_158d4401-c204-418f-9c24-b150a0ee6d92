from typing import List, ClassVar
import re

from app.components.core.base_node import <PERSON>Node
from app.models.workflow_builder.components import InputBase, StringInput, BoolInput, DropdownInput, MultilineInput
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeR<PERSON>ult

from app.utils.workflow_builder.input_helpers import create_dual_purpose_input


class RegexExtractorComponent(BaseNode):
    """
    Extracts data from text using regular expressions.

    This component takes an input text and applies a regular expression pattern
    to extract specific data, supporting various extraction modes and output formats.
    """

    name: ClassVar[str] = "RegexExtractorComponent"
    display_name: ClassVar[str] = "Regex Extractor"
    description: ClassVar[str] = "Extract data from text using regular expressions"
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Search"

    inputs: ClassVar[List[InputBase]] = [
        # Input text - dual purpose (connectable + editable)
        create_dual_purpose_input(
            name="input_text",
            display_name="Input Text",
            input_type="multiline",
            required=True,
            info="The source text content to be searched. Can be connected from another node or entered directly.",
            input_types=["string", "Any"]
        ),
        
        # Regex pattern - dual purpose (connectable + editable)
        create_dual_purpose_input(
            name="regex_pattern",
            display_name="Regex Pattern",
            input_type="multiline",
            required=True,
            info="Regular expression pattern to match. Can be connected from another node or entered directly.",
            input_types=["string"]
        ),
        
        # Extraction mode
        DropdownInput(
            name="extraction_mode",
            display_name="Extraction Mode",
            options=["first_match", "all_matches"],
            value="first_match",
            info="Find first match only or all matches"
        ),
        
        # Output format
        DropdownInput(
            name="output_format",
            display_name="Output Format",
            options=["full_match", "first_capture_group", "named_groups"],
            value="full_match",
            info="Format of extracted data"
        ),
        
        # Regex flags
        BoolInput(
            name="case_insensitive",
            display_name="Case Insensitive (i)",
            value=False,
            advanced=True,
            info="Ignore case when matching"
        ),
        BoolInput(
            name="multiline",
            display_name="Multiline (m)",
            value=False,
            advanced=True,
            info="^ and $ match start/end of lines"
        ),
        BoolInput(
            name="dot_all",
            display_name="Dot All (s)",
            value=False,
            advanced=True,
            info=". matches newlines"
        ),
        
        # Error handling
        DropdownInput(
            name="on_no_match",
            display_name="On No Match",
            options=["continue_empty", "fail_workflow"],
            value="continue_empty",
            info="Behavior when no match is found"
        )
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="matches", display_name="Matches", output_type="Any"),
        Output(name="first_match", display_name="First Match", output_type="Any"),
        Output(name="match_count", display_name="Match Count", output_type="int"),
        Output(name="is_match_found", display_name="Is Match Found", output_type="bool"),
        Output(name="error", display_name="Error", output_type="str")
    ]

    async def execute(self, context: WorkflowContext, **kwargs) -> NodeResult:
        """
        Executes the regex extraction operation.

        Args:
            context: The workflow execution context
            **kwargs: Contains the input values:
                - input_text: The text to search
                - regex_pattern: The regex pattern to apply
                - extraction_mode: 'first_match' or 'all_matches'
                - output_format: 'full_match', 'first_capture_group', or 'named_groups'
                - case_insensitive: Boolean flag for case insensitive matching
                - multiline: Boolean flag for multiline mode
                - dot_all: Boolean flag for dot all mode
                - on_no_match: 'continue_empty' or 'fail_workflow'

        Returns:
            A NodeResult with either:
                - matches: The extracted matches
                - first_match: The first match (for convenience)
                - match_count: Number of matches found
                - is_match_found: Boolean indicating if any matches were found
                - error: An error message if the operation failed
        """
        print(f"Executing {self.name}...")

        # Get inputs
        input_text = kwargs.get("input_text", "")
        regex_pattern = kwargs.get("regex_pattern", "")
        extraction_mode = kwargs.get("extraction_mode", "first_match")
        output_format = kwargs.get("output_format", "full_match")
        case_insensitive = kwargs.get("case_insensitive", False)
        multiline = kwargs.get("multiline", False)
        dot_all = kwargs.get("dot_all", False)
        on_no_match = kwargs.get("on_no_match", "continue_empty")

        # Validate required inputs
        if not input_text:
            return NodeResult(
                node_id=context.current_node_id,
                outputs={"error": "Input text is required. Please connect text or provide it directly."},
                status="error"
            )

        if not regex_pattern:
            return NodeResult(
                node_id=context.current_node_id,
                outputs={"error": "Regex pattern is required. Please connect a pattern or provide it directly."},
                status="error"
            )

        # Convert input to string if not already
        if not isinstance(input_text, str):
            try:
                input_text = str(input_text)
                print(f"  Converted input to string")
            except Exception as e:
                return NodeResult(
                    node_id=context.current_node_id,
                    outputs={"error": f"Failed to convert input to string: {str(e)}"},
                    status="error"
                )

        # Validate regex pattern syntax
        try:
            # Build regex flags
            flags = 0
            if case_insensitive:
                flags |= re.IGNORECASE
            if multiline:
                flags |= re.MULTILINE
            if dot_all:
                flags |= re.DOTALL
            
            # Test compile the pattern
            re.compile(regex_pattern, flags)
        except re.error as e:
            return NodeResult(
                node_id=context.current_node_id,
                outputs={"error": f"Invalid regex pattern: {str(e)}"},
                status="error"
            )

        try:
            # Build regex flags again for execution
            flags = 0
            if case_insensitive:
                flags |= re.IGNORECASE
            if multiline:
                flags |= re.MULTILINE
            if dot_all:
                flags |= re.DOTALL

            compiled_pattern = re.compile(regex_pattern, flags)
            
            # Execute regex matching
            if extraction_mode == "first_match":
                match = compiled_pattern.search(input_text)
                matches = [match] if match else []
            else:  # all_matches
                matches = list(compiled_pattern.finditer(input_text))

            # Handle no matches
            if not matches:
                if on_no_match == "fail_workflow":
                    return NodeResult(
                        node_id=context.current_node_id,
                        outputs={"error": "No matches found for the given pattern"},
                        status="error"
                    )
                else:  # continue_empty
                    print(f"  No matches found, continuing with empty results")
                    return NodeResult(
                        node_id=context.current_node_id,
                        outputs={
                            "matches": [],
                            "first_match": None,
                            "match_count": 0,
                            "is_match_found": False
                        },
                        status="success"
                    )

            # Format results based on output_format
            formatted_matches = self._format_matches(matches, output_format)
            
            print(f"  Regex extraction successful. Found {len(matches)} matches.")
            return NodeResult(
                node_id=context.current_node_id,
                outputs={
                    "matches": formatted_matches,
                    "first_match": formatted_matches[0] if formatted_matches else None,
                    "match_count": len(matches),
                    "is_match_found": len(matches) > 0
                },
                status="success"
            )

        except Exception as e:
            error_msg = f"Error during regex extraction: {str(e)}"
            print(f"  {error_msg}")
            return NodeResult(
                node_id=context.current_node_id,
                outputs={"error": error_msg},
                status="error"
            )

    def _format_matches(self, matches, output_format):
        """
        Format match results based on output format.
        
        Args:
            matches: List of regex match objects
            output_format: Format type ('full_match', 'first_capture_group', 'named_groups')
            
        Returns:
            Formatted list of matches
        """
        try:
            if output_format == "full_match":
                return [match.group(0) for match in matches]
            elif output_format == "first_capture_group":
                return [match.group(1) if match.groups() else match.group(0) for match in matches]
            elif output_format == "named_groups":
                return [match.groupdict() if match.groupdict() else {"match": match.group(0)} for match in matches]
            else:
                # Fallback to full match
                return [match.group(0) for match in matches]
        except Exception as e:
            print(f"  Error formatting matches: {str(e)}")
            # Return raw match strings as fallback
            return [match.group(0) for match in matches]