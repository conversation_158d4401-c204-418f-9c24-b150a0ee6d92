from typing import Dict, Any, List, ClassVar
import time
import logging

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import InputBase, Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import Node<PERSON><PERSON>ult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

# Set up logging
logger = logging.getLogger(__name__)


class MessageToDataComponent(BaseNode):
    """
    Extracts fields from a Message object.

    This component takes a Message object and extracts specified fields
    into a dictionary.
    """

    name: ClassVar[str] = "MessageToDataComponent"
    display_name: ClassVar[str] = "Message To Data"
    description: ClassVar[str] = "Extracts fields from a Message object."
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Package"

    inputs: ClassVar[List[InputBase]] = [
        # Input message - unified dual-purpose input
        create_dual_purpose_input(
            name="input_message",
            display_name="Input Message",
            input_type="dict",
            required=True,
            value={},
            info="The Message object to extract fields from. Can be connected from another node or entered directly.",
            input_types=["Message", "dict", "Any"],
        ),
        # Fields to extract - unified dual-purpose input
        create_dual_purpose_input(
            name="fields_to_extract",
            display_name="Fields to Extract",
            input_type="list",
            required=False,
            value=[],
            info="List of field names to extract from the message. Leave empty to extract all fields. Can be connected from another node or entered directly.",
            input_types=["list", "Any"],
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="output", display_name="Extracted Data", output_type="dict"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get the value of an input from the context.

        This method handles dual-purpose inputs, retrieving the value from the context.

        Args:
            input_name: The name of the input.
            context: The workflow execution context.
            default: The default value to return if the input is not found.

        Returns:
            The value of the input, or the default value if not found.
        """
        # Get the current node ID from the context
        node_id = context.current_node_id
        if not node_id:
            logger.warning("No current node ID in context")
            return default

        # Check if there's a value in the node outputs
        node_outputs = context.node_outputs.get(node_id, {})
        if input_name in node_outputs:
            return node_outputs[input_name]

        # If not found, return the default
        return default

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute the MessageToDataComponent.

        This method extracts specified fields from a Message object into a dictionary.

        Args:
            context: The workflow execution context containing input values.

        Returns:
            A NodeResult with the execution results.
        """
        # Start timing for performance measurement
        start_time = time.time()

        # Log execution start
        context.log(f"Executing {self.name}...")

        try:
            # Get inputs using helper method
            input_message = self.get_input_value("input_message", context, None)
            fields_to_extract = self.get_input_value("fields_to_extract", context, [])

            # Validate input message
            if input_message is None:
                error_msg = "Input message is missing. Please connect or provide a Message object."
                context.log(error_msg)
                return NodeResult.error(error_msg, time.time() - start_time)

            # Extract fields from the message
            result = {}

            # If the input is a dictionary, we can extract fields directly
            if isinstance(input_message, dict):
                # If no fields specified, extract all
                if not fields_to_extract:
                    result = input_message.copy()
                else:
                    # Extract only specified fields
                    for field in fields_to_extract:
                        if field in input_message:
                            result[field] = input_message[field]

            # If the input is an object with attributes, extract them
            else:
                # If no fields specified, try to get all attributes
                if not fields_to_extract:
                    # Try to convert object to dictionary using __dict__
                    try:
                        result = input_message.__dict__.copy()
                    except (AttributeError, TypeError):
                        error_msg = "Cannot extract all fields from the input message. Please specify fields to extract."
                        context.log(error_msg)
                        return NodeResult.error(error_msg, time.time() - start_time)
                else:
                    # Extract only specified fields
                    for field in fields_to_extract:
                        try:
                            result[field] = getattr(input_message, field)
                        except (AttributeError, TypeError):
                            # Skip fields that don't exist
                            pass

            # Log success
            execution_time = time.time() - start_time
            context.log(f"Fields extracted successfully. Keys: {list(result.keys())}. Time: {execution_time:.2f}s")

            return NodeResult.success(
                outputs=result,
                execution_time=execution_time
            )

        except Exception as e:
            error_msg = f"Error extracting fields: {str(e)}"
            context.log(error_msg)
            execution_time = time.time() - start_time
            return NodeResult.error(error_msg, execution_time)

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the MessageToDataComponent.

        DEPRECATED: This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Args:
            **kwargs: Contains the input values

        Returns:
            A dictionary with the component's outputs
        """
        logger.warning(
            f"Using legacy build method for {self.name}. Please update to use execute method."
        )

        # Get inputs - now using unified dual-purpose inputs
        input_message = kwargs.get("input_message", {})
        fields_to_extract = kwargs.get("fields_to_extract", [])

        # Validate input
        if input_message is None:
            return {"error": "Input message is missing. Please connect or provide a Message object."}

        try:
            # Extract fields from the message
            result = {}

            # If the input is a dictionary, we can extract fields directly
            if isinstance(input_message, dict):
                # If no fields specified, extract all
                if not fields_to_extract:
                    result = input_message.copy()
                else:
                    # Extract only specified fields
                    for field in fields_to_extract:
                        if field in input_message:
                            result[field] = input_message[field]

            # If the input is an object with attributes, extract them
            else:
                # If no fields specified, try to get all attributes
                if not fields_to_extract:
                    # Try to convert object to dictionary using __dict__
                    try:
                        result = input_message.__dict__.copy()
                    except (AttributeError, TypeError):
                        return {
                            "error": "Cannot extract all fields from the input message. Please specify fields to extract."
                        }
                else:
                    # Extract only specified fields
                    for field in fields_to_extract:
                        try:
                            result[field] = getattr(input_message, field)
                        except (AttributeError, TypeError):
                            # Skip fields that don't exist
                            pass

            logger.info(f"Fields extracted successfully. Keys: {list(result.keys())}")
            return result

        except Exception as e:
            error_msg = f"Error extracting fields: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
