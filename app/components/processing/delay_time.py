from typing import ClassVar, List

from app.components.core.base_node import BaseNode
# InputBase is needed for the generic input handle
from app.models.workflow_builder.components import InputBase, Output
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

class DelayComponent(BaseNode):
    """
    A user-facing definition for a node that pauses the workflow and passes
    data through after the delay.
    """

    # --- Static Component Definition for the UI ---
    name: ClassVar[str] = "DelayComponent"
    display_name: ClassVar[str] = "Wait / Delay"
    description: ClassVar[str] = "Pauses the workflow execution for a set number of seconds."
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Timer"

    # --- Input Definition ---
    inputs: ClassVar[List[InputBase]] = [
        # --- FIX #1: ADD THE GENERIC INPUT HANDLE ---
        # This defines the main input connection point on the node where users can
        # connect data from a previous node. This data will be passed through.
        create_dual_purpose_input(
            name="input_data",
            display_name="Input Data",
            input_type="string", 
            required=True,
            value="",
            info="The input data to be passed through.",
            input_types=["number", "string", "Any"],
        ),
        # --- END OF FIX #1 ---

        create_dual_purpose_input(
            name="delay_seconds",
            display_name="Delay (seconds)",
            input_type="string", 
            required=True,
            value="30",
            info="The number of seconds to pause the workflow.",
            input_types=["number", "string", "Any"],
        ),
    ]

    # --- Output Definitions ---
    # This definition is already correct.
    outputs: ClassVar[List[Output]] = [
        Output(name="output", display_name="Output", output_type="Any"),
        Output(name="Message", display_name="Message", output_type="string"),
        Output(name="error", display_name="Error", output_type="string"),
    ]

    async def execute(self, context):
        raise NotImplementedError(
            f"Execution for '{self.name}' is handled by the dedicated executor service."
        )