from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query, Header
from fastapi.security import OAuth2PasswordRequestForm
from app.services.admin_service import AdminServiceClient
from app.schemas.admin import UserInfo
from app.core.security import get_current_user
from app.schemas.admin import (
    AdminCreate,
    AdminUpdate,
    AdminResponse,
    LoginResponse,
    TokenResponse,
    AdminList,
    RoleCreate,
    RoleUpdate,
    RoleResponse,
    RoleList,
    GetAllOrganisationsResponse,
    GetOrganisationDetailsRequest,
    GetOrganisationDetailsResponse,
    GetUsersResponse,
    GetAgentsRequest,
    GetAgentsResponse,
    GetWorkflowsResponse,
    GetOrganisationActiveUsersRequest,
    GetOrganisationActiveUsersResponse,
    SourceModel,
    ListSourcesResponse,
    AgentGraphInDB
)
from google.protobuf.json_format import MessageToDict
from app.utils.parse_error import parse_error
import json

# Create separate routers for admin auth, admin management, and role management
admin_auth_router = APIRouter(prefix="/admin/auth", tags=["admin auth"])
admin_router = APIRouter(prefix="/admin", tags=["admin management"])
role_router = APIRouter(prefix="/roles", tags=["role management"])

admin_service = AdminServiceClient()

# Admin Authentication Routes

def convert_enum_to_string(enum_value: str, enum_type: str) -> str:
    """
    Convert proto enum values to their string representations.
    
    Args:
        enum_value (str): The enum value from proto
        enum_type (str): The type of enum (visibility, status, creator_role)
    
    Returns:
        str: The string representation of the enum
    """
    if enum_type == "visibility":
        visibility_map = {
            "DEFAULT": "private",  # Default maps to private
            "PRIVATE": "private",
            "PUBLIC": "public"
        }
        return visibility_map.get(enum_value.upper(), "private")
    
    elif enum_type == "status":
        status_map = {
            "INVALID": "inactive",  # Default maps to inactive
            "ACTIVE": "active",
            "INACTIVE": "inactive",
            "BENCH": "bench"
        }
        return status_map.get(enum_value.upper(), "active")
    
    elif enum_type == "creator_role":
        role_map = {
            "CREATOR_ROLE_UNSPECIFIED": "member",  # Default maps to member
            "MEMBER": "member",
            "CREATOR": "creator",
            "VIEWER": "viewer"
        }
        return role_map.get(enum_value.upper(), "member")
    
    return enum_value.lower()



@admin_auth_router.post(
    "/login",
    summary="Admin Login",
    description="""
    This endpoint allows an admin to log in using their email and password.

    - If the admin does not exist, a 404 error is returned.
    - If the credentials are incorrect, a 401 error is returned.
    - On success, an access token, refresh token, and admin info are returned.
    """,
    responses={
        200: {
            "description": "Login successful",
            "content": {
                "application/json": {
                    "example": {
                        "access_token": "your_access_token",
                        "refresh_token": "your_refresh_token",
                        "token_type": "bearer",
                        "roles": [
                            {
                                "roleId": "1",
                                "name": "Super Admin",
                                "description": "Has full access",
                                "permissions": ["manage_users", "manage_roles"],
                                "createdAt": "2024-01-01T00:00:00Z",
                                "updatedAt": "2024-01-01T00:00:00Z",
                            }
                        ],
                    }
                }
            },
        },
        401: {
            "description": "Invalid credentials",
            "content": {"application/json": {"example": {"detail": "Invalid credentials"}}},
        },
        404: {
            "description": "Admin not found",
            "content": {"application/json": {"example": {"detail": "Admin does not exist"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
    response_model=LoginResponse,
)
@admin_auth_router.post("/login", response_model=LoginResponse)
async def admin_login(form_data: OAuth2PasswordRequestForm = Depends()):
    try:
        response = await admin_service.login(email=form_data.username, password=form_data.password)
        if not response.success:
            raise HTTPException(status_code=401, detail=response.message)

        # Extract the roles information correctly
        roles_data = []
        for role in response.admin.roles:
            roles_data.append(
                {
                    "role_id": role.roleId,
                    "name": role.name,
                    "description": role.description,
                    "permissions": role.permissions,
                    "created_at": role.createdAt,
                    "updated_at": role.updatedAt,
                }
            )

        return {
            "access_token": response.accessToken,
            "refresh_token": response.refreshToken,
            "token_type": "bearer",
            "roles": roles_data,  # Ensure the `admin` object is returned
        }
    except Exception as e:
        print(str(e))
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@admin_auth_router.post(
    "/access-token",
    summary="Refresh Admin Access Token",
    description="""
    This endpoint allows refreshing an admin's access token using their refresh token.

    - If the refresh token is invalid or expired, a 401 error is returned.
    - If the admin does not exist, a 404 error is returned.
    - On success, a new access token and refresh token are returned.

    """,
    responses={
        200: {
            "description": "Token refresh successful",
            "content": {
                "application/json": {
                    "example": {
                        "access_token": "new_access_token",
                        "refresh_token": "new_refresh_token",
                        "token_type": "bearer",
                    }
                }
            },
        },
        401: {
            "description": "Authentication failed",
            "content": {"application/json": {"example": {"detail": "Refresh token expired"}}},
        },
        404: {
            "description": "Admin not found",
            "content": {"application/json": {"example": {"detail": "Admin not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
    response_model=TokenResponse,
)
@admin_auth_router.post("/access-token", response_model=TokenResponse)
async def admin_refresh_token(refresh_token: str):
    try:
        response = await admin_service.access_token(refresh_token)
        if not response.success:
            raise HTTPException(status_code=401, detail=response.message)
        return {
            "access_token": response.accessToken,
            "refresh_token": response.refreshToken,
            "token_type": "bearer",
        }
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# Admin Management Routes
@admin_router.post("", response_model=AdminResponse)
async def create_admin(admin_data: AdminCreate, current_user: dict = Depends(get_current_user)):
    response = await admin_service.create_admin(
        email=admin_data.email,
        password=admin_data.password,
        full_name=admin_data.full_name,
        role_ids=admin_data.role_ids,
    )
    return response


@admin_router.get("/{admin_id}", response_model=AdminResponse)
async def get_admin(admin_id: str, current_user: dict = Depends(get_current_user)):
    response = await admin_service.get_admin(admin_id)
    return response


@admin_router.put("/{admin_id}", response_model=AdminResponse)
async def update_admin(
    admin_id: str, admin_data: AdminUpdate, current_user: dict = Depends(get_current_user)
):
    response = await admin_service.update_admin(
        admin_id=admin_id,
        full_name=admin_data.full_name,
        email=admin_data.email,
        password=admin_data.password,
        role_ids=admin_data.role_ids,
    )
    return response


@admin_router.delete("/{admin_id}")
async def delete_admin(admin_id: str, current_user: dict = Depends(get_current_user)):
    response = await admin_service.delete_admin(admin_id)
    if not response.success:
        raise HTTPException(status_code=400, detail=response.message)
    return {"message": "Admin deleted successfully"}


@admin_router.get("", response_model=AdminList)
async def list_admins(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    current_user: dict = Depends(get_current_user),
):
    response = await admin_service.list_admins(page=page, page_size=page_size)
    return {
        "admins": response.admins,
        "total": response.total,
        "page": response.page,
        "total_pages": response.totalPages,
    }


@admin_router.post("/{admin_id}/roles", response_model=AdminResponse)
async def assign_roles(
    admin_id: str, role_ids: list[str], current_user: dict = Depends(get_current_user)
):
    response = await admin_service.assign_role(admin_id=admin_id, role_ids=role_ids)
    return response


@admin_router.get(
    "/organisations/all",
    summary="Get All Organisations",
    description="""
    This endpoint allows a super admin to retrieve all organisations registered on the platform.
    
    Returns comprehensive information about each organisation including:
    - Organisation details (name, website, industry, etc.)
    - Admin information for each organisation
    - Department structure and member counts
    - Statistics (total users, departments, etc.)
    
    This is a super admin function that provides complete visibility into all organisations.
    """,
    responses={
        200: {
            "description": "Successfully retrieved all organisations",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Successfully retrieved organisations",
                        "organisations": [
                            {
                                "id": "org_123",
                                "name": "Example Corp",
                                "website_url": "https://example.com",
                                "industry": "Technology",
                                "admin": {
                                    "admin_id": "admin_123",
                                    "email": "<EMAIL>",
                                    "full_name": "John Doe"
                                },
                                "departments": [
                                    {
                                        "id": "dept_123",
                                        "name": "Engineering",
                                        "member_count": 25
                                    }
                                ],
                                "statistics": {
                                    "total_users": 50,
                                    "total_departments": 3,
                                    "total_department_members": 45
                                }
                            }
                        ],
                        "total_count": 1,
                        "status": "success"
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Invalid or missing authentication",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Forbidden - Insufficient permissions",
            "content": {"application/json": {"example": {"detail": "Not enough permissions"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
    response_model=GetAllOrganisationsResponse,
)
async def get_all_organisations(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of organisations per page"),
    industry: Optional[str] = Query(None, description="Filter by Industry"),
    plan_type: Optional[str] = Query(None, description="Filter by Plan Type"),
    search: Optional[str] = Query(None, description="Search term to filter by name"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all organisations with limited data and pagination support.
    Returns only specific fields: id, name, description, logo, industry,
    created_at, status, total_departments, total_users, type,
    total_payments, total_rcu, rcu_used
    """
    try:
        response = await admin_service.get_all_organisations(
            page=page,
            page_size=page_size,
            industry = industry,
            plan_type = plan_type,
            search = search
        )
        
        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)
        
        try:
            data = json.loads(response.message)
            return GetAllOrganisationsResponse(
                success=True,
                message="Successfully retrieved all organisations",
                organisations=data.get('organisations', []),
                total_count=data.get('total_count', 0),
                page=data.get('page', page),
                page_size=data.get('page_size', page_size),
                status=data.get('status', 'success')
            )
        except json.JSONDecodeError:
            # Fallback if message is not JSON
            return GetAllOrganisationsResponse(
                success=response.success,
                message=response.message,
                organisations=[],
                total_count=0,
                page=page,
                page_size=page_size,
                status='error'
            )
            
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@admin_router.get(
    "/organisations/{organisation_id}",
    summary="Get Organisation Details",
    description="""
    This endpoint allows a super admin to retrieve complete details of a specific organisation by ID.
    
    Returns comprehensive information about the organisation including:
    - Complete organisation details (name, website, industry, address, etc.)
    - Admin information for the organisation
    - All departments with member counts
    - Detailed statistics (total users, departments, etc.)
    
    This is a super admin function that provides complete visibility into a specific organisation.
    """,
    responses={
        200: {
            "description": "Successfully retrieved organisation details",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Successfully retrieved organisation details",
                        "organisation": {
                            "id": "org_123",
                            "name": "Example Corp",
                            "website_url": "https://example.com",
                            "industry": "Technology",
                            "description": "A technology company",
                            "admin": {
                                "admin_id": "admin_123",
                                "email": "<EMAIL>",
                                "full_name": "John Doe"
                            },
                            "departments": [
                                {
                                    "id": "dept_123",
                                    "name": "Engineering",
                                    "member_count": 25
                                }
                            ],
                            "statistics": {
                                "total_users": 50,
                                "total_departments": 3,
                                "total_department_members": 45
                            },
                            'plan_type': "Free Plan",
                            'available_tokens': 20.0
                        },
                        "status": "success"
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid organisation ID",
            "content": {"application/json": {"example": {"detail": "Organisation ID is required"}}},
        },
        401: {
            "description": "Unauthorized - Invalid or missing authentication",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Forbidden - Insufficient permissions",
            "content": {"application/json": {"example": {"detail": "Not enough permissions"}}},
        },
        404: {
            "description": "Not Found - Organisation not found",
            "content": {"application/json": {"example": {"detail": "Organisation not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
    response_model=GetOrganisationDetailsResponse,
)
async def get_organisation_details(
    organisation_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Get complete organisation details by organisation ID.
    Returns all organisation data including admin, departments, and statistics.
    """
    try:
        if not organisation_id or not organisation_id.strip():
            raise HTTPException(status_code=400, detail="Organisation ID is required")
            
        response = await admin_service.get_organisation_details(organisation_id=organisation_id)
        
        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)
        
        try:
            data = json.loads(response.message)
            
            # Check if organisation was found
            if data.get('status') == 'not_found':
                raise HTTPException(status_code=404, detail=data.get('message', 'Organisation not found'))
            
            return GetOrganisationDetailsResponse(
                success=True,
                message="Successfully retrieved organisation details",
                organisation=data.get('organisation'),
                status=data.get('status', 'success')
            )
        except json.JSONDecodeError:
            # Fallback if message is not JSON
            return GetOrganisationDetailsResponse(
                success=response.success,
                message=response.message,
                organisation=None,
                status='error'
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions as they are
        raise
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@admin_router.get(
    "/organisations/{organisation_id}/sources",
    summary="List Organisation Sources",
    description="""
    This endpoint allows retrieving all sources for a specific organisation.
    Returns a list of sources and indicates if initial mapping has been done.
    """,
    responses={
        200: {
            "description": "Successfully retrieved organisation sources",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Successfully retrieved sources",
                        "sources": [
                            {
                               "id": "source_123",
                               "organisation_id": "Source 1",
                               "type": "folder",
                               "name": "/path/to/source",
                               "created_at": "2025-01-01T00:00:00Z",
                               "updated_at": "2025-01-01T00:00:00Z"
                            }
                        ],
                        "is_initial_mapping": True
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized - Invalid or missing authentication",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}}
        },
        403: {
            "description": "Forbidden - Insufficient permissions",
            "content": {"application/json": {"example": {"detail": "Not enough permissions"}}}
        },
        404: {
            "description": "Not Found - Organisation not found",
            "content": {"application/json": {"example": {"detail": "Organisation not found"}}}
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}}
        }
    },
    response_model=ListSourcesResponse
)
async def list_organisation_sources(
    organisation_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Get all sources for a specific organisation.
    Returns list of sources and initial mapping status.
    """
    try:
        response = await admin_service.list_sources(organisation_id=organisation_id)
        sources = []
        for source in response.sources:
            # Convert integer enum to string for response
            source_type_str = "GOOGLE_DRIVE" if source.type == 0 else "SLACK"
            source_dict = {
                "id": source.id,
                "organisation_id": source.organisation_id,
                "type": source_type_str,
                "name": source.name,
                "created_at": source.created_at,
                "updated_at": source.updated_at
            }
            sources.append(source_dict)
        
        result = {
            "success": response.success,
            "message": response.message,
            "sources": sources,
            "is_initial_mapping": response.isInitialMapping
        }

        # Unpack the dictionary when creating ListSourcesResponse
        return ListSourcesResponse(**result)
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@admin_router.get(
    "/organisations/{organisation_id}/active-users",
    summary="Get Organisation Active Users",
    description="""
    This endpoint allows retrieving active users belonging to a specific organization.
    Optionally filter by department and includes pagination support.
    
    Returns:
    - List of active users with their department details
    - Total count of users
    - Pagination information
    - Department details if filtered by department
    """,
    responses={
        200: {
            "description": "Successfully retrieved organisation active users",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Successfully retrieved users",
                        "users": [
                            {
                                "id": "user_123",
                                "name": "John Doe",
                                "email": "<EMAIL>",
                                "departments": [
                                    {
                                        "name": "Engineering",
                                        "role": "MEMBER",
                                        "permission": "READ"
                                    }
                                ]
                            }
                        ],
                        "total_count": 1,
                        "page": 1,
                        "page_size": 10,
                        "dept_name": "Engineering",
                        "dept_desc": "Engineering department"
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid parameters",
            "content": {"application/json": {"example": {"detail": "Invalid parameters"}}},
        },
        401: {
            "description": "Unauthorized - Invalid or missing authentication",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Forbidden - Insufficient permissions",
            "content": {"application/json": {"example": {"detail": "Not enough permissions"}}},
        },
        404: {
            "description": "Not Found - Organisation or department not found",
            "content": {"application/json": {"example": {"detail": "Organisation not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
    response_model=GetOrganisationActiveUsersResponse,
)
async def get_organisation_active_users(
    organisation_id: str,
    department_id: Optional[str] = Query(None, description="Filter by department ID"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of users per page"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get active users belonging to a specific organization with optional department filtering.
    Returns users list with metadata including their departments.
    """
    try:
        response = await admin_service.get_organisations_active_users(
            organisation_id=organisation_id,
            department_id=department_id,
            page=page,
            page_size=page_size
        )
        
        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)
        
        try:
            data = json.loads(response.message)
            return GetOrganisationActiveUsersResponse(
                success=True,
                message="Successfully retrieved organisation active users",
                users=data.get('users', []),
                total_count=data.get('totalCount', 0),
                page=data.get('page', page),
                page_size=data.get('page_size', page_size),
                dept_name=data.get('dept_name'),
                dept_desc=data.get('dept_desc')
            )
        except json.JSONDecodeError:
            return GetOrganisationActiveUsersResponse(
                success=response.success,
                message=response.message,
                users=[],
                total_count=0,
                page=page,
                page_size=page_size
            )
            
    except Exception as e:
        print(f"Error in get_organisation_active_users: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@admin_router.get("/organisations/{organisation_id}/agents")
async def get_all_agents_from_organisation(
    organisation_id: str,
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(12, ge=1, le=100, description="Number of agents per page"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all agents from all departments of an organization with pagination support.

    This endpoint retrieves all agents that belong to any department within
    the specified organization with pagination. Access is controlled based on user permissions.

    Args:
        organisation_id (str): The ID of the organization
        page (int): Page number (default: 1)
        page_size (int): Number of agents per page (default: 12)
        current_user (dict): The authenticated user

    Returns:
        dict: Response containing paginated list of agents and metadata

    Raises:
        HTTPException 403: If user doesn't have access to the organization
        HTTPException 404: If organization not found or no agents found
        HTTPException 500: If an unexpected server error occurs
    """
    try:
        # TODO: Add organization access validation
        # For now, we'll allow any authenticated user to access
        
        response = await admin_service.get_all_agents_from_organisation(
            organisation_id=organisation_id,
            page=page,
            page_size=page_size
        )

        if not response.success:
            raise HTTPException(
                status_code=404,
                detail="No agents found for organization or organization not found"
            )

        # Convert agents to Pydantic models
        agents = []
        for agent in response.agents:
            agent_dict = MessageToDict(agent, preserving_proto_field_name=True)
            
            # Convert enum values to their string representations
            if "visibility" in agent_dict:
                agent_dict["visibility"] = convert_enum_to_string(agent_dict["visibility"], "visibility")
            if "status" in agent_dict:
                agent_dict["status"] = convert_enum_to_string(agent_dict["status"], "status")
            if "creator_role" in agent_dict:
                agent_dict["creator_role"] = convert_enum_to_string(agent_dict["creator_role"], "creator_role")
            
            # Set owner_name to empty string if not present
            if "owner_name" not in agent_dict:
                agent_dict["owner_name"] = ""
                
            agents.append(AgentGraphInDB(**agent_dict))

        return {
            "success": True,
            "message": f"Found {response.total} agents in organization",
            "agents": agents,
            "total": response.total,
            "page": response.page,
            "page_size": response.page_size
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in get_all_agents_from_organisation: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@admin_router.get("/users/all", response_model=GetUsersResponse)
async def get_all_users(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(12, ge=1, le=100, description="Number of users per page"),
    company: Optional[str] = Query(None, description="Filter by company"),
    department: Optional[str] = Query(None, description="Filter by department"),
    role: Optional[str] = Query(None, description="Filter by role"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    is_email_verified: Optional[bool] = Query(None, description="Filter by email verification"),
    search: Optional[str] = Query(None, description="Search in name, email, department, company"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all users with filtering and pagination support.
    Returns users list with metadata.
    """
    try:
        response = await admin_service.get_all_users(
            page=page,
            page_size=page_size,
            company=company,
            department=department,
            role=role,
            is_active=is_active,
            is_email_verified=is_email_verified,
            search=search
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)
            
        # Parse the JSON string from the message field
        data = json.loads(response.message)

        # Return the parsed data in the expected format
        try:
            users_data = []
            for user in data.get("users", []):
                users_data.append(UserInfo(**user))
                
            return GetUsersResponse(
                success=True,
                message="Successfully retrieved users",
                users=users_data,
                total_count=data.get("total_count", 0),
                page=data.get("page", page),
                page_size=data.get("page_size", page_size),
                status="success"
            )
        except Exception as e:
            print(f"Error parsing user data: {str(e)}")
            raise HTTPException(status_code=500, detail="Error parsing user data")

    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@admin_router.get("/agents/all", response_model=GetAgentsResponse)
async def get_all_agents(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(12, ge=1, le=100, description="Number of agents per page"),
    visibility: Optional[str] = Query(None, description="Filter by visibility (PUBLIC/PRIVATE)"),
    agent_category: Optional[str] = Query(None, description="Filter by agent category"),
    department: Optional[str] = Query(None, description="Filter by department"),
    is_bench_employee: Optional[bool] = Query(None, description="Filter by bench employee status"),
    search: Optional[str] = Query(None, description="Search in name, description, department, category"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all agents with filtering and pagination support.
    Returns agents list with metadata.
    """
    try:
        response = await admin_service.get_all_agents(
            page=page,
            page_size=page_size,
            visibility=visibility,
            agent_category=agent_category,
            department=department,
            is_bench_employee=is_bench_employee,
            search=search
        )
        
        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)
        
        # Parse the JSON string from the message field
        data = json.loads(response.message)

        # Return the parsed data in the expected format
        return GetAgentsResponse(
            success=True,
            message="Successfully retrieved agents",
            agents=data.get('agents', []),
            total_count=data.get('total_count', 0),
            page=data.get('page', page),
            page_size=data.get('page_size', page_size),
            status=data.get('status', 'success')
        )
            
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@admin_router.get(
    "/workflows/all",
    summary="Get All Workflows",
    description="""
    This endpoint allows retrieving all workflows with filtering and pagination support.
    
    Returns workflows list with metadata including:
    - Basic workflow information (id, name, description)
    - Workflow configuration (nodes, URLs)
    - Ownership and visibility details
    - Status and categorization
    - Creation and modification timestamps
    
    Supports filtering by:
    - Visibility (PUBLIC/PRIVATE)
    - Status (ACTIVE/INACTIVE)
    - Category
    - Tags
    - Search term (matches name, description)
    """,
    responses={
        200: {
            "description": "Successfully retrieved workflows",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Successfully retrieved workflows",
                        "workflows": [
                            {
                                "id": "workflow_123",
                                "name": "Example Workflow",
                                "description": "A sample workflow",
                                "visibility": "PUBLIC",
                                "status": "ACTIVE",
                                "category": "General",
                                "tags": ["automation", "general"],
                                "created_at": "2024-01-01T00:00:00Z"
                            }
                        ],
                        "total_count": 1,
                        "page": 1,
                        "page_size": 10,
                        "status": "success"
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid parameters",
            "content": {"application/json": {"example": {"detail": "Invalid filter parameters"}}},
        },
        401: {
            "description": "Unauthorized - Invalid or missing authentication",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
    response_model=GetWorkflowsResponse
)
async def get_all_workflows(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of workflows per page"),
    visibility: Optional[str] = Query(None, description="Filter by visibility (PUBLIC/PRIVATE)"),
    status: Optional[str] = Query(None, description="Filter by status (ACTIVE/INACTIVE)"),
    category: Optional[str] = Query(None, description="Filter by category"),
    search: Optional[str] = Query(None, description="Search in name and description"),
    tags: Optional[str] = Query(None, description="Filter by comma-separated tags"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all workflows with filtering and pagination support.
    Returns workflows list with metadata.
    """
    try:
        response = await admin_service.get_all_workflows(
            page=page,
            page_size=page_size,
            visibility=visibility,
            status=status,
            category=category,
            search=search,
            tags=tags
        )
        
        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)
        
        # Parse the JSON string from the message field
        data = json.loads(response.message)

        # Return the parsed data in the expected format
        return GetWorkflowsResponse(
            success=True,
            message="Successfully retrieved agents",
            workflows=data.get('workflows', []),
            total_count=data.get('total_count', 0),
            page=data.get('page', page),
            page_size=data.get('page_size', page_size),
            status=data.get('status', 'success')
        )
            
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

# Role Management Routes
@role_router.post("", response_model=RoleResponse)
async def create_role(role_data: RoleCreate, current_user: dict = Depends(get_current_user)):
    response = await admin_service.create_role(
        name=role_data.name, description=role_data.description, permissions=role_data.permissions
    )
    return response


@role_router.get("/{role_id}", response_model=RoleResponse)
async def get_role(role_id: str, current_user: dict = Depends(get_current_user)):
    response = await admin_service.get_role(role_id)
    return response


@role_router.put("/{role_id}", response_model=RoleResponse)
async def update_role(
    role_id: str, role_data: RoleUpdate, current_user: dict = Depends(get_current_user)
):
    response = await admin_service.update_role(
        role_id=role_id,
        name=role_data.name,
        description=role_data.description,
        permissions=role_data.permissions,
    )
    return response


@role_router.delete("/{role_id}")
async def delete_role(role_id: str, current_user: dict = Depends(get_current_user)):
    response = await admin_service.delete_role(role_id)
    if not response.success:
        raise HTTPException(status_code=400, detail=response.message)
    return {"message": "Role deleted successfully"}


@role_router.get("", response_model=RoleList)
async def list_roles(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    current_user: dict = Depends(get_current_user),
):
    response = await admin_service.list_roles(page=page, page_size=page_size)
    return {
        "roles": response.roles,
        "total": response.total,
        "page": response.page,
        "total_pages": response.totalPages,
    }
