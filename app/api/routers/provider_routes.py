from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Path, Query
from app.services.provider_service import ProviderServiceClient, ModelServiceClient
from app.core.auth_guard import role_required
from app.schemas.provider import (
    PaginationInfo,
    ProviderCreate,
    ProviderInfo,
    ProviderUpdate,
    ProviderResponse,
    ProviderListResponse,
    ProviderDeleteResponse,
    ModelCreate,
    ModelUpdate,
    ModelResponse,
    ModelListResponse,
    ModelDeleteResponse,
    ListRequest,
    ModelInfo,
    SyncModelsResponse,
    SyncStats,
)
from app.utils.parse_error import parse_error
from google.protobuf.json_format import MessageToDict
from app.core.config import settings
# Create separate routers for providers and models
provider_router = APIRouter(prefix="/providers", tags=["providers"])
model_router = APIRouter(prefix="/models", tags=["models"])

provider_service = ProviderServiceClient()
model_service = ModelServiceClient()


# Provider Routes
@provider_router.post(
    "",
    response_model=ProviderResponse,
    summary="Create a new AI Model Provider",
    description="""
    Create a new AI Model Provider with the specified configuration.
    
    - Only users with admin role can create providers
    - Provider name must be unique
    - If is_default is True, it will become the only default provider
    - Base URL must be a valid HTTP/HTTPS URL
    """,
    responses={
        200: {"description": "Provider created successfully"},
        400: {"description": "Invalid input or provider name already exists"},
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden - Admin role required"},
        500: {"description": "Internal Server Error"},
    },
)
async def create_provider(
    provider_data: ProviderCreate, current_user: dict = Depends(role_required(["admin"]))
):
    try:
        response = await provider_service.create_provider(
            provider=provider_data.provider,
            description=provider_data.description,
            base_url=str(provider_data.base_url),
            is_active=provider_data.is_active,
            is_default=provider_data.is_default,
            platform=provider_data.platform,
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        return response
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@provider_router.get(
    "",
    response_model=ProviderListResponse,
    summary="List AI Model Providers",
    description="""
    Retrieve a paginated list of AI Model Providers.
    
    - Supports pagination with page and page_size parameters
    - Can filter by active status
    - Returns provider information including model count
    """,
    responses={
        200: {"description": "Providers retrieved successfully"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal Server Error"},
    },
)
async def list_providers(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    platform: Optional[str] = Query(settings.DEFAULT_PROVIDER_PLATFORM, description="Filter by platform (requesty/openrouter)"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    try:
        print(f"[DEBUG] Listing providers with page={page}, page_size={page_size}, is_active={is_active}, platform={platform}")
        response = await provider_service.list_providers(
            page=page, page_size=page_size, is_active=is_active, platform=platform
        )
        print(f"[DEBUG] Service response received: success={response.success}, message={response.message}")

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        return response
    except Exception as e:
        print(f"[ERROR] Exception occurred while listing providers: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@provider_router.get(
    "/{provider_id}",
    response_model=ProviderResponse,
    summary="Get AI Model Provider by ID",
    description="""
    Retrieve a specific AI Model Provider by its ID.
    
    - Returns detailed provider information including model count
    """,
    responses={
        200: {"description": "Provider retrieved successfully"},
        404: {"description": "Provider not found"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal Server Error"},
    },
)
async def get_provider(
    provider_id: str = Path(..., description="Provider ID"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    try:
        response = await provider_service.get_provider(provider_id)

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        return response
    except Exception as e:
        print(f"[ERROR] Exception occurred while getting provider: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@provider_router.put(
    "/{provider_id}",
    response_model=ProviderResponse,
    summary="Update AI Model Provider",
    description="""
    Update an existing AI Model Provider.
    
    - Only users with admin role can update providers
    - Provider name must be unique if changed
    - If is_default is set to True, it will become the only default provider
    """,
    responses={
        200: {"description": "Provider updated successfully"},
        400: {"description": "Invalid input or provider name already exists"},
        404: {"description": "Provider not found"},
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden - Admin role required"},
        500: {"description": "Internal Server Error"},
    },
)
async def update_provider(
    provider_id: str = Path(..., description="Provider ID"),
    provider_data: ProviderUpdate = None,
    current_user: dict = Depends(role_required(["admin"])),
):
    try:
        response = await provider_service.update_provider(
            provider_id=provider_id,
            provider=provider_data.provider,
            description=provider_data.description,
            base_url=str(provider_data.base_url) if provider_data.base_url else None,
            is_active=provider_data.is_active,
            is_default=provider_data.is_default,
            platform=provider_data.platform,
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        return response
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@provider_router.delete(
    "/{provider_id}",
    response_model=ProviderDeleteResponse,
    summary="Delete AI Model Provider",
    description="""
    Delete an AI Model Provider.
    
    - Only users with admin role can delete providers
    - Cannot delete provider if it has associated models
    """,
    responses={
        200: {"description": "Provider deleted successfully"},
        400: {"description": "Cannot delete provider with associated models"},
        404: {"description": "Provider not found"},
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden - Admin role required"},
        500: {"description": "Internal Server Error"},
    },
)
async def delete_provider(
    provider_id: str = Path(..., description="Provider ID"),
    current_user: dict = Depends(role_required(["admin"])),
):
    try:
        response = await provider_service.delete_provider(provider_id)

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        return ProviderDeleteResponse(success=response.success, message=response.message)
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@provider_router.post(
    "/sync-models",
    response_model=SyncModelsResponse,
    summary="Sync Models from External API",
    description="""
    Synchronize providers and models from external API sources.
    
    - Only users with admin role can trigger sync
    - Fetches latest provider and model information from external APIs
    - Returns statistics about what was added, updated, or removed
    - This operation may take some time to complete
    """,
    responses={
        200: {"description": "Models synchronized successfully"},
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden - Admin role required"},
        500: {"description": "Internal Server Error"},
    },
)
async def sync_models(
    current_user: dict = Depends(role_required(["user"]))
):
    try:
        response = await provider_service.sync_models()

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        # Convert the gRPC response to our schema format
        sync_response = SyncModelsResponse(
            success=response.success,
            message=response.message,
            stats=SyncStats(**MessageToDict(response.stats, preserving_proto_field_name=True)) if response.stats else None
        )

        return sync_response
    except Exception as e:
        print(f"[ERROR] Exception occurred while syncing models: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@provider_router.get(
    "/{provider_id}/models",
    response_model=ModelListResponse,
    summary="List Models by Provider",
    description="""
    Retrieve a paginated list of models for a specific provider.
    
    - Supports pagination with page and page_size parameters
    - Can filter by active status
    - Returns model information including provider details
    """,
    responses={
        200: {"description": "Models retrieved successfully"},
        404: {"description": "Provider not found"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal Server Error"},
    },
)
async def list_models_by_provider(
    provider_id: str = Path(..., description="Provider ID"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    platform: Optional[str] = Query(None, description="Filter by platform (requesty/openrouter)"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    try:
        response = await model_service.list_models_by_provider(
            provider_id=provider_id, page=page, page_size=page_size, is_active=is_active, platform=platform
        )

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        return response
    except Exception as e:
        print(f"[ERROR] Exception occurred while listing models by provider: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# Model Routes
@model_router.post(
    "",
    response_model=ModelResponse,
    summary="Create a new AI Model",
    description="""
    Create a new AI Model for a specific provider.
    
    - Only users with admin role can create models
    - Model name must be unique within the provider
    - If is_default is True, it will become the only default model for the provider
    """,
    responses={
        200: {"description": "Model created successfully"},
        400: {"description": "Invalid input, provider not found, or model name already exists"},
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden - Admin role required"},
        500: {"description": "Internal Server Error"},
    },
)
async def create_model(
    model_data: ModelCreate, current_user: dict = Depends(role_required(["admin"]))
):
    try:
        response = await model_service.create_model(
            provider_id=model_data.provider_id,
            model=model_data.model,
            model_id=model_data.model_id,
            description=model_data.description,
            input_price_per_token=model_data.input_price_per_token,
            output_price_per_token=model_data.output_price_per_token,
            max_tokens=model_data.max_tokens,
            context_window=model_data.context_window,
            temperature=model_data.temperature,
            provider_type=model_data.provider_type,
            is_active=model_data.is_active,
            is_default=model_data.is_default,
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        return response
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@model_router.get(
    "",
    response_model=ModelListResponse,
    summary="List AI Models",
    description="""
    Retrieve a paginated list of AI Models.
    
    - Supports pagination with page and page_size parameters
    - Can filter by active status
    - Can filter by provider ID (optional)
    - Returns model information including provider details
    """,
    responses={
        200: {"description": "Models retrieved successfully"},
        400: {"description": "Provider not found (when provider_id is specified)"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal Server Error"},
    },
)
async def list_models(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    provider_id: Optional[str] = Query(None, description="Filter by provider ID"),
    platform: Optional[str] = Query(None, description="Filter by platform (requesty/openrouter)"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    try:
        print(f"[DEBUG] Listing models with page={page}, page_size={page_size}, is_active={is_active}, provider_id={provider_id}, platform={platform}")
        response = await model_service.list_models(
            page=page, page_size=page_size, is_active=is_active, provider_id=provider_id, platform=platform
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        return ModelListResponse(
            success=response.success,
            message=response.message,
            models=[ModelInfo(**MessageToDict(m, preserving_proto_field_name=True)) for m in response.models],
            pagination=PaginationInfo(**MessageToDict(response.pagination, preserving_proto_field_name=True))
        )
    except Exception as e:
        print(f"[ERROR] Exception occurred while listing models: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@model_router.get(
    "/{model_id}",
    response_model=ModelResponse,
    summary="Get AI Model by ID",
    description="""
    Retrieve a specific AI Model by its ID.
    
    - Returns detailed model information including provider details
    """,
    responses={
        200: {"description": "Model retrieved successfully"},
        404: {"description": "Model not found"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal Server Error"},
    },
)
async def get_model(
    model_id: str = Path(..., description="Model ID"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    try:
        response = await model_service.get_model(model_id)

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        return ModelResponse(
            success=response.success,
            message=response.message,
            model=ModelInfo(**MessageToDict(response.model, preserving_proto_field_name=True)) if response.model else None,
        )
    except Exception as e:
        print(f"[ERROR] Exception occurred while getting model: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@model_router.put(
    "/{model_id}",
    response_model=ModelResponse,
    summary="Update AI Model",
    description="""
    Update an existing AI Model.
    
    - Only users with admin role can update models
    - Model name must be unique within the provider if changed
    - If is_default is set to True, it will become the only default model for the provider
    """,
    responses={
        200: {"description": "Model updated successfully"},
        400: {"description": "Invalid input, provider not found, or model name already exists"},
        404: {"description": "Model not found"},
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden - Admin role required"},
        500: {"description": "Internal Server Error"},
    },
)
async def update_model(
    model_id: str = Path(..., description="Model ID"),
    model_data: ModelUpdate = None,
    current_user: dict = Depends(role_required(["admin"])),
):
    try:
        response = await model_service.update_model(
            model_id=model_id,
            provider_id=model_data.provider_id,
            model=model_data.model,
            model_id_field=model_data.model_id,
            description=model_data.description,
            input_price_per_token=model_data.input_price_per_token,
            output_price_per_token=model_data.output_price_per_token,
            max_tokens=model_data.max_tokens,
            context_window=model_data.context_window,
            temperature=model_data.temperature,
            provider_type=model_data.provider_type,
            is_active=model_data.is_active,
            is_default=model_data.is_default,
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        return ModelResponse(
            success=response.success,
            message=response.message,
            model=ModelInfo(**MessageToDict(response.model, preserving_proto_field_name=True)) if response.model else None,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@model_router.delete(
    "/{model_id}",
    response_model=ModelDeleteResponse,
    summary="Delete AI Model",
    description="""
    Delete an AI Model.
    
    - Only users with admin role can delete models
    """,
    responses={
        200: {"description": "Model deleted successfully"},
        404: {"description": "Model not found"},
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden - Admin role required"},
        500: {"description": "Internal Server Error"},
    },
)
async def delete_model(
    model_id: str = Path(..., description="Model ID"),
    current_user: dict = Depends(role_required(["admin"])),
):
    try:
        response = await model_service.delete_model(model_id)

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        return ModelDeleteResponse(success=response.success, message=response.message)
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
