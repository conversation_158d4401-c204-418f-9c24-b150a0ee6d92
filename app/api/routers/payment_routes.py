from sqlite3.dbapi2 import Timestamp
from fastapi import APIRouter, Depends, HTTPException, status, Request, Header, Query
from typing import Optional
from datetime import datetime
from google.protobuf.timestamp_pb2 import Timestamp

from app.schemas.payment import (
    CreateCheckoutSessionRequestSchema,
    CreateCheckoutSessionResponseSchema,
    CreateCustomerPortalSessionRequestSchema,
    CreateCustomerPortalSessionResponseSchema,
    SubscriptionResponseSchema,
    SubscriptionDataSchema,
    CancelSubscriptionRequestSchema,
    GenericResponseSchema,
    DeductCreditsRequestSchema,
    DeductCreditsResponseSchema,
    DeductCreditsDataSchema,
    CreditBalanceResponseSchema,
    CreditBalanceDataSchema,
    GetTokenUsageResponseSchema,
    TokenUsageLogEntrySchema,
    UserTokenUsageSchema,
    GetAggregatedTokenUsageResponseSchema,
    PaymentPlanCreateSchema,
    PaymentPlanSchema,
    ListPlansResponseSchema,
    PaginationMetadataSchema,
    CalculateCreditsRequestSchema,
    CalculateCreditsResponseSchema,
    CalculateCreditsDataSchema,
    # Topup schemas
    TopupPlanSchema,
    CreateTopupCheckoutSessionRequestSchema,
    CreateTopupCheckoutSessionResponseSchema,
    ListTopupPlansResponseSchema,
    TopupPlanCreateSchema,
    # Transaction schemas
    PaymentTransactionSchema,
    GetPaymentTransactionsResponseSchema,
)
from app.services.payment_service import PaymentServiceClient
from app.core.auth_guard import role_required
from app.grpc_ import payment_pb2
from typing import List
from app.services.user_service import UserServiceClient

payment_service = PaymentServiceClient()
payment_router = APIRouter(prefix="/payments", tags=["Payments"])

def proto_timestamp_to_datetime(ts) -> Optional[datetime]:
    """Convert protobuf timestamp to datetime, handling None and empty timestamps"""
    if ts is None:
        return None
    
    # Check if timestamp has any meaningful value
    if not hasattr(ts, 'seconds') or not hasattr(ts, 'nanos'):
        return None
        
    # Check for the default "zero" timestamp
    if ts.seconds == 0 and ts.nanos == 0:
        return None
        
    try:
        # Convert to datetime and remove timezone info
        dt = ts.ToDatetime()
        return dt.replace(tzinfo=None)
    except Exception as e:
        # Handle potential conversion errors if the timestamp is invalid
        print(f"Error converting timestamp: {e}, ts: {ts}")
        return None

def proto_to_pydantic(proto_obj, pydantic_model):
    """Converts a protobuf object to a Pydantic model instance."""
    
    # Convert protobuf object to a dictionary
    proto_dict = {
        field.name: getattr(proto_obj, field.name)
        for field in proto_obj.DESCRIPTOR.fields
    }
    
    # Handle special cases, like Timestamps
    for key, value in proto_dict.items():
        if isinstance(value, Timestamp):
            # Convert timestamp to a string format that Pydantic can parse
            proto_dict[key] = value.ToDatetime().isoformat()

    # Create a Pydantic model instance from the dictionary
    return pydantic_model(**proto_dict)
@payment_router.post(
    "/plans",
    response_model=PaymentPlanSchema,
    summary="Create a new payment plan",
    dependencies=[Depends(role_required(["admin"]))],
)
async def create_plan(plan_data: PaymentPlanCreateSchema):
    try:
        grpc_response = await payment_service.create_plan(plan_data.dict())
        if not grpc_response.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=grpc_response.error_message or "Failed to create payment plan."
            )
        # Assuming the successful response contains the plan object, similar to get_subscription
        plan = grpc_response.plan
        return PaymentPlanSchema(
            id=plan.id,
            plan_id_code=plan.plan_id_code,
            name=plan.name,
            credit_amount=plan.credit_amount,
            price=plan.price,
            stripe_price_id=plan.stripe_price_id,
            is_default=plan.is_default,
        )
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred while creating the plan: {str(e)}"
        )

@payment_router.get(
    "/plans",
    response_model=ListPlansResponseSchema,
    summary="List all payment plans",
)
async def list_plans(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    current_user: dict = Depends(role_required(["user", "admin"]))
):
    try:
        grpc_response = await payment_service.list_plans(page=page, page_size=page_size)

        if not grpc_response.success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=grpc_response.message or "Failed to retrieve payment plans"
            )

        # Convert to schema format
        plans = [PaymentPlanSchema.from_orm(plan) for plan in grpc_response.plans]

        metadata = PaginationMetadataSchema(
            current_page=grpc_response.metadata.current_page,
            total_pages=grpc_response.metadata.total_pages,
            total_items=grpc_response.metadata.total_items,
            page_size=grpc_response.metadata.page_size
        )

        return ListPlansResponseSchema(
            success=grpc_response.success,
            message=grpc_response.message,
            plans=plans,
            metadata=metadata
        )
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving payment plans: {str(e)}"
        )

@payment_router.post(
    "/activate-default-plan/{org_id}",
    response_model=SubscriptionResponseSchema,
    summary="Activate Default Plan for Organisation",
)
async def activate_default_plan(
    org_id: str,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        grpc_response = await payment_service.activate_default_plan(org_id=org_id)

        if not grpc_response:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to activate default plan."
            )

        start_date = proto_timestamp_to_datetime(grpc_response.current_period_start)
        end_date = proto_timestamp_to_datetime(grpc_response.current_period_end)

        subscription_data = SubscriptionDataSchema(
            id=grpc_response.id,
            organisation_id=grpc_response.organisation_id,
            plan_id_code=grpc_response.plan_id_code.upper(),
            status=payment_pb2.SubscriptionStatus.Name(grpc_response.status),
            current_period_start=start_date.isoformat() if isinstance(start_date, datetime) else None,
            current_period_end=end_date.isoformat() if isinstance(end_date, datetime) else None,
            subscription_credits=grpc_response.subscription_credits,
            current_credits=grpc_response.current_credits,
        )

        return SubscriptionResponseSchema(
            success=True,
            message="Default plan activated successfully",
            subscription=subscription_data
        )
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        )

@payment_router.post(
    "/checkout",
    response_model=CreateCheckoutSessionResponseSchema,
    summary="Create Stripe Checkout Session",
)
async def create_checkout_session(
    request_data: CreateCheckoutSessionRequestSchema,
    current_user: dict = Depends(role_required(["user"])),
):
    grpc_response = await payment_service.create_checkout_session(
        org_id=request_data.organisation_id,
        plan_id_code=request_data.plan_id_code,
        success_url=request_data.success_url,
        cancel_url=request_data.cancel_url,
    )
    if not grpc_response.success:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=grpc_response.error_message)
    return grpc_response

@payment_router.post(
    "/customer-portal",
    response_model=CreateCustomerPortalSessionResponseSchema,
    summary="Create Stripe Customer Portal Session",
)
async def create_customer_portal(
    request_data: CreateCustomerPortalSessionRequestSchema,
    current_user: dict = Depends(role_required(["user"])),
):
    grpc_response = await payment_service.create_customer_portal_session(
        org_id=request_data.organisation_id,
        return_url=request_data.return_url,
    )
    if not grpc_response.success:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=grpc_response.error_message)
    return grpc_response

@payment_router.get(
    "/subscription/{organisation_id}",
    response_model=SubscriptionResponseSchema,
    summary="Get Subscription Details",
)
async def get_subscription(
    organisation_id: str,
    current_user: dict = Depends(role_required(["user"])),
):
    print(f"Debug - organisation_id: {organisation_id}")
    try:
        grpc_response = await payment_service.get_subscription(org_id=organisation_id)
        print(f"Debug - grpc_response: {grpc_response}")

        if not grpc_response.success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=grpc_response.error_message or "Subscription not found"
            )

        sub = grpc_response.subscription
        start_date = proto_timestamp_to_datetime(sub.current_period_start)
        end_date = proto_timestamp_to_datetime(sub.current_period_end)

        subscription_data = SubscriptionDataSchema(
            id=sub.id,
            organisation_id=sub.organisation_id,
            plan_id_code=sub.plan_id_code.upper(),
            status=payment_pb2.SubscriptionStatus.Name(sub.status),
            current_period_start=start_date.isoformat() if isinstance(start_date, datetime) else None,
            current_period_end=end_date.isoformat() if isinstance(end_date, datetime) else None,
            subscription_credits=sub.subscription_credits,
            current_credits=sub.current_credits,
        )

        return SubscriptionResponseSchema(
            success=True,
            message="Subscription details retrieved successfully",
            subscription=subscription_data
        )
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        print(f"Error retrieving subscription: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving subscription: {str(e)}"
        )

@payment_router.post(
    "/subscription/cancel",
    response_model=GenericResponseSchema,
    summary="Cancel Subscription",
)
async def cancel_subscription(
    request_data: CancelSubscriptionRequestSchema,
    current_user: dict = Depends(role_required(["user"])),
):
    grpc_response = await payment_service.cancel_subscription(org_id=request_data.organisation_id)
    if not grpc_response.success:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=grpc_response.error_message)
    return grpc_response

@payment_router.post(
    "/credits/deduct",
    response_model=DeductCreditsResponseSchema,
    summary="Deduct Credits for Token Usage",
)
async def deduct_credits(
    request_data: DeductCreditsRequestSchema,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        grpc_response = await payment_service.deduct_credits(
            org_id=request_data.organisation_id,
            user_id=current_user.get("user_id"),
            agent_id=request_data.agent_id,
            input_tokens=request_data.input_tokens,
            output_tokens=request_data.output_tokens,
            consumed_credits=request_data.consumed_credits,
            description=request_data.description,
        )
        if not grpc_response.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=grpc_response.error_message or "Failed to deduct credits"
            )

        deduct_data = DeductCreditsDataSchema(
            new_balance=grpc_response.new_balance_after_deduction
        )

        return DeductCreditsResponseSchema(
            success=True,
            message="Credits deducted successfully",
            data=deduct_data
        )
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deducting credits: {str(e)}"
        )

@payment_router.get(
    "/credits/{organisation_id}",
    response_model=CreditBalanceResponseSchema,
    summary="Get Credit Balance",
)
async def get_credit_balance(
    organisation_id: str,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        grpc_response = await payment_service.get_credit_balance(org_id=organisation_id)
        if not grpc_response.success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=grpc_response.error_message or "Credit balance not found"
            )

        credit_data = CreditBalanceDataSchema(
            credit_balance=grpc_response.credit_balance
        )

        return CreditBalanceResponseSchema(
            success=True,
            message="Credit balance retrieved successfully",
            data=credit_data
        )
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving credit balance: {str(e)}"
        )

@payment_router.get(
    "/token-usage/{organisation_id}",
    response_model=GetTokenUsageResponseSchema,
    summary="Get Token Usage Logs",
)
async def get_token_usage(
    organisation_id: str,
    user_id: Optional[str] = Query(None),
    agent_id: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    month: Optional[int] = Query(None, ge=1, le=12),
    year: Optional[int] = Query(None, ge=2000),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        grpc_response = await payment_service.get_token_usage(
            org_id=organisation_id,
            user_id=user_id,
            agent_id=agent_id,
            start_date=start_date,
            end_date=end_date,
            page=page,
            page_size=page_size,
            month=month,
            year=year,
        )
        if not grpc_response.success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=grpc_response.error_message or "Failed to retrieve token usage logs"
            )

        user_service = UserServiceClient()
        
        token_usage_logs_pydantic = []
        for log in grpc_response.token_usage_logs:
            user_name = "Unknown User"
            try:
                user_response = await user_service.get_user(log.user_id)
                # print(f"RPC: GetUser - User ID: {log.user_id}, Response: {user_response}")
                if user_response and user_response.user:
                    user_name = user_response.user.fullName
            except Exception:
                # Keep "Unknown User" if user not found or error occurs
                pass

            log_data = {
                "id": log.id,
                "organisation_id": log.organisation_id,
                "user_id": log.user_id,
                "user_name": user_name,
                "agent_id": log.agent_id,
                "input_tokens": log.input_tokens,
                "output_tokens": log.output_tokens,
                "total_cost": log.total_cost,
                "total_credits": log.total_credits,
                "date": log.date,
                "month": log.month if log.month != 0 else None,
                "year": log.year if log.year != 0 else None,
            }
            token_usage_logs_pydantic.append(TokenUsageLogEntrySchema(**log_data))

        metadata = PaginationMetadataSchema(
            current_page=grpc_response.metadata.current_page,
            total_pages=grpc_response.metadata.total_pages,
            total_items=grpc_response.metadata.total_items,
            page_size=grpc_response.metadata.page_size
        )

        return GetTokenUsageResponseSchema(
            success=True,
            message="Token usage logs retrieved successfully",
            token_usage_logs=token_usage_logs_pydantic,
            metadata=metadata
        )
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving token usage logs: {str(e)}"
        )

@payment_router.get(
    "/token-usage/aggregated/{organisation_id}",
    response_model=GetAggregatedTokenUsageResponseSchema,
    summary="Get Aggregated Token Usage Logs",
)
async def get_aggregated_token_usage(
    organisation_id: str,
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    month: Optional[int] = Query(None, ge=1, le=12),
    year: Optional[int] = Query(None, ge=2000),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        grpc_response = await payment_service.get_aggregated_token_usage(
            org_id=organisation_id,
            start_date=start_date,
            end_date=end_date,
            month=month,
            year=year,
            page=page,
            page_size=page_size,
        )
        if not grpc_response.success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=grpc_response.error_message or "Failed to retrieve aggregated token usage logs"
            )

        user_service = UserServiceClient()
        
        user_token_usage_pydantic = []
        for user_usage in grpc_response.user_token_usage:
            user_name = "Unknown User"
            try:
                user_response = await user_service.get_user(user_usage.user_id)
                if user_response and user_response.user:
                    user_name = user_response.user.fullName
            except Exception:
                pass

            user_token_usage_pydantic.append(
                UserTokenUsageSchema(
                    user_id=user_usage.user_id,
                    user_name=user_name,
                    total_input_tokens=user_usage.total_input_tokens,
                    total_output_tokens=user_usage.total_output_tokens,
                    total_credits=user_usage.total_credits,
                )
            )

        metadata = PaginationMetadataSchema(
            current_page=grpc_response.metadata.current_page,
            total_pages=grpc_response.metadata.total_pages,
            total_items=grpc_response.metadata.total_items,
            page_size=grpc_response.metadata.page_size
        )

        return GetAggregatedTokenUsageResponseSchema(
            success=True,
            message="Aggregated token usage logs retrieved successfully",
            user_token_usage=user_token_usage_pydantic,
            metadata=metadata
        )
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving aggregated token usage logs: {str(e)}"
        )

@payment_router.post(
    "/stripe/webhook",
    response_model=GenericResponseSchema,
    summary="Handle Stripe Webhooks",
)
async def handle_stripe_webhook(
    request: Request,
    stripe_signature: Optional[str] = Header(None, alias="Stripe-Signature"),
):
    if not stripe_signature:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Missing Stripe-Signature header.")

    payload = await request.body()
    grpc_response = await payment_service.handle_stripe_webhook(
        payload=payload,
        signature=stripe_signature,
    )
    if not grpc_response.success:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=grpc_response.error_message)
    return GenericResponseSchema(success=True, message="Webhook processed successfully.")

@payment_router.post(
    "/calculate-credits",
    response_model=CalculateCreditsResponseSchema,
    summary="Calculate Credits for Token Usage",
)
async def calculate_credits(
    request_data: CalculateCreditsRequestSchema,
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Calculate credits based on model pricing information.

    This endpoint accepts model information (model_name, provider_name) and token counts,
    fetches the pricing details from the provider service, and returns the calculated
    credits and cost in USD.
    """
    # Import provider service here to avoid circular imports
    from app.services.provider_service import ModelServiceClient

    # Create model service client to fetch pricing information
    model_service = ModelServiceClient()

    try:
        # Get all models to find the one matching model_name and provider_name
        models_response = await model_service.list_models(page=1, page_size=1000)

        if not models_response.success:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Failed to fetch models from provider service"
            )

        # Find the model that matches both model_name and provider_name
        target_model = None
        for model in models_response.models:
            if (model.model.lower() == request_data.model_name.lower() and
                model.provider.provider.lower() == request_data.provider_name.lower()):
                target_model = model
                break

        if not target_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Model '{request_data.model_name}' not found for provider '{request_data.provider_name}'"
            )

        # Extract pricing information
        input_price_per_token = target_model.inputPricePerToken
        output_price_per_token = target_model.outputPricePerToken

        # Call payment service to calculate credits
        grpc_response = await payment_service.calculate_credits(
            input_tokens=request_data.input_tokens,
            output_tokens=request_data.output_tokens,
            input_price_per_token=input_price_per_token,
            output_price_per_token=output_price_per_token,
        )

        if not grpc_response.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=grpc_response.message or "Failed to calculate credits"
            )

        calculate_data = CalculateCreditsDataSchema(
            credits=grpc_response.credits,
            cost_in_usd=grpc_response.cost_in_usd,
            input_price_per_token=grpc_response.input_price_per_token,
            output_price_per_token=grpc_response.output_price_per_token,
        )

        return CalculateCreditsResponseSchema(
            success=True,
            message="Credits calculated successfully",
            data=calculate_data
        )

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error calculating credits: {str(e)}"
        )
    finally:
        model_service.close()


@payment_router.get("/transactions/{organisation_id}", response_model=GetPaymentTransactionsResponseSchema)
async def get_payment_transactions(
    organisation_id: str,
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    request: Request = None,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Get all payment transactions for an organization.
    Only accessible by organization admins.
    """
    try:
        grpc_response = await payment_service.get_payment_transactions(
            organisation_id, page=page, page_size=page_size
        )

        if not grpc_response.success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=grpc_response.message or "Failed to retrieve payment transactions"
            )

        # Convert gRPC response to schema format
        transactions = []
        for transaction in grpc_response.transactions:
            transactions.append(PaymentTransactionSchema(
                id=transaction.id,
                organisation_id=transaction.organisation_id,
                transaction_type=transaction.transaction_type,
                status=transaction.status,
                amount=transaction.amount_currency / 100,
                currency=transaction.currency,
                description=transaction.description,
                stripe_charge_id=transaction.stripe_charge_id if transaction.stripe_charge_id else None,
                stripe_invoice_id=transaction.stripe_invoice_id if transaction.stripe_invoice_id else None,
                invoice_url=transaction.invoice_url if transaction.invoice_url else None,
                subscription_id=transaction.subscription_id if transaction.subscription_id else None,
                created_at=transaction.created_at
            ))

        metadata = PaginationMetadataSchema(
            current_page=grpc_response.metadata.current_page,
            total_pages=grpc_response.metadata.total_pages,
            total_items=grpc_response.metadata.total_items,
            page_size=grpc_response.metadata.page_size
        )

        return GetPaymentTransactionsResponseSchema(
            success=grpc_response.success,
            message=grpc_response.message,
            transactions=transactions,
            metadata=metadata
        )

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        print(f"Error fetching payment transactions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving payment transactions: {str(e)}"
        )



# --- Topup Plan Routes ---

@payment_router.get("/topup-plans", response_model=ListTopupPlansResponseSchema)
async def list_topup_plans(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    current_user: dict = Depends(role_required(["user", "admin"]))
):
    """List all available topup plans."""
    try:
        grpc_response = await payment_service.list_topup_plans(page=page, page_size=page_size)

        if not grpc_response.success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=grpc_response.message or "Failed to retrieve topup plans"
            )

        topup_plans = []
        for plan in grpc_response.topup_plans:
                topup_plans.append(TopupPlanSchema(
                id=plan.id,
                plan_id_code=plan.plan_id_code,
                name=plan.name,
                credit_amount=plan.credit_amount,
                price=plan.price,
                stripe_price_id=plan.stripe_price_id,
            ))

        metadata = PaginationMetadataSchema(
            current_page=grpc_response.metadata.current_page,
            total_pages=grpc_response.metadata.total_pages,
            total_items=grpc_response.metadata.total_items,
            page_size=grpc_response.metadata.page_size
        )

        return ListTopupPlansResponseSchema(
            success=grpc_response.success,
            message=grpc_response.message,
            topup_plans=topup_plans,
            metadata=metadata
        )

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        print(f"Error fetching topup plans: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving topup plans: {str(e)}"
        )


@payment_router.post("/topup/checkout", response_model=CreateTopupCheckoutSessionResponseSchema)
async def create_topup_checkout_session(
    request: CreateTopupCheckoutSessionRequestSchema,
    current_user: dict = Depends(role_required(["user", "admin"]))
):
    """Create a checkout session for topup purchase."""
    try:
        grpc_response = await payment_service.create_topup_checkout_session(
            org_id=request.organisation_id,
            topup_plan_id_code=request.topup_plan_id_code,
            success_url=request.success_url,
            cancel_url=request.cancel_url,
        )

        if not grpc_response.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=grpc_response.error_message or "Failed to create topup checkout session."
            )

        return CreateTopupCheckoutSessionResponseSchema(
            checkout_url=grpc_response.checkout_url,
            session_id=grpc_response.session_id,
            success=True,
            error_message=None,
        )

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal error: {str(e)}"
        )


@payment_router.post("/topup-plans", response_model=TopupPlanSchema)
async def create_topup_plan(
    request: TopupPlanCreateSchema,
    current_user: dict = Depends(role_required(["admin"]))
):
    """Create a new topup plan (admin only)."""
    try:
        plan_data = {
            "name": request.name,
            "credit_amount": request.credit_amount,
            "price": request.price,
            "stripe_price_id": request.stripe_price_id,
        }

        grpc_response = await payment_service.create_topup_plan(plan_data)
        
        if not grpc_response.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=grpc_response.error_message or "Failed to create topup plan."
            )
        
        # Assuming the successful response contains the plan object
        plan = grpc_response.topup_plan
        return TopupPlanSchema(
            id=plan.id,
            plan_id_code=plan.plan_id_code,
            name=plan.name,
            credit_amount=plan.credit_amount,
            price=plan.price,
            stripe_price_id=plan.stripe_price_id,
        )

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal error while creating topup plan: {str(e)}"
        )