import grpc
import json
import structlog
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import SessionL<PERSON>al
from app.models.agent import <PERSON>Config, AgentConfigVersion, AgentMarketplaceListing, AgentModelConfig, AgentKnowledgeBase
from app.utils.constants.constants import AgentStatusEnum, AgentVisibilityEnum

logger = structlog.get_logger()

def get_db(self) -> Session:
    db = SessionLocal()
    try:
        return db
    finally:
        db.close()


def _create_marketplace_listing_from_agent(
    db: Session, # Pass the session explicitly
    agent: AgentConfig,
) -> AgentMarketplaceListing | None:
    """
    Creates or updates a marketplace listing for an agent using its current version.
    If a listing for this agent_config_id by this owner already exists, it updates it
    to point to the agent's current_version_id. Otherwise, it creates a new listing.
    """
    if not agent.owner_id:
        logger.error(f"Agent {agent.id} has no owner_id, cannot create/update marketplace listing.")
        return None

    if not agent.current_version_id:
        logger.error(
            f"Agent {agent.id} has no current_version_id, cannot create/update marketplace listing."
        )
        return None

    # Get the current version details
    current_version = db.get(AgentConfigVersion, agent.current_version_id) # Simpler way to get by PK

    if not current_version:
        logger.error(
            f"Current version {agent.current_version_id} not found for agent {agent.id}"
        )
        return None

    # Check if a marketplace listing already exists for this agent_config_id by this owner
    existing_listing = (
        db.query(AgentMarketplaceListing)
        .filter(
            AgentMarketplaceListing.agent_config_id == agent.id,
            # AgentMarketplaceListing.listed_by_user_id == agent.owner_id, # Ensure it's the same lister
        )
        .first()
    )

    if existing_listing:
        # Update existing listing
        logger.info(
            f"Updating existing marketplace listing {existing_listing.id} for agent {agent.id} to version {current_version.id} ({current_version.version_number})."
        )
        existing_listing.agent_config_version_id = current_version.id
        existing_listing.version_number = current_version.version_number # From AgentMarketplaceListing model
        existing_listing.title = current_version.name # Or agent.name, depending on desired source
        existing_listing.description = current_version.description or "No description provided."
        existing_listing.image_url = current_version.avatar # Or agent.avatar
        existing_listing.tags = list(current_version.tags or []) # Ensure it's a list
        
        existing_listing.status = AgentStatusEnum.ACTIVE # Make sure it's active
        existing_listing.visibility = AgentVisibilityEnum.PUBLIC # Ensure it's public
        existing_listing.updated_at = datetime.now(timezone.utc)
        # listed_by_user_id should not change if we are updating the same owner's listing.
        # If a different user is trying to list, that's a different scenario (new listing or permission denied).
        # For this function, we assume the agent's owner is the one doing the listing.
        if existing_listing.listed_by_user_id != agent.owner_id:
            logger.warning(f"Marketplace listing {existing_listing.id} found for agent {agent.id}, but listed by a different user ({existing_listing.listed_by_user_id}) than current agent owner ({agent.owner_id}). Proceeding with update by current owner.")
            # Potentially, you might want to prevent this or handle it differently,
            # e.g., by creating a new listing if the owner changes.
            # For simplicity now, we'll update it, assuming the agent's current owner takes precedence.
            existing_listing.listed_by_user_id = agent.owner_id


        db.add(existing_listing)
        # db.flush() # Not strictly necessary here if only committing at the end of ToggleAgentVisibility
        return existing_listing
    else:
        # Create new marketplace listing
        logger.info(
            f"Creating new marketplace listing for agent {agent.id} version {current_version.id} ({current_version.version_number})."
        )
        new_listing = AgentMarketplaceListing(
            agent_config_id=agent.id,
            agent_config_version_id=current_version.id,
            version_number=current_version.version_number, # From AgentMarketplaceListing model
            listed_by_user_id=agent.owner_id,
            title=current_version.name, # Or agent.name
            description=current_version.description or "No description provided.",
            image_url=current_version.avatar, # Or agent.avatar
            tags=list(current_version.tags or []), # Ensure it's a list

            use_count=0, # Initial values for a new listing
            execution_count=0,
            average_rating=0.0,

            visibility=AgentVisibilityEnum.PUBLIC, # New public listings are public
            status=AgentStatusEnum.ACTIVE, # New public listings are active
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        db.add(new_listing)
        # db.flush() # Ensure ID is available if needed before commit
        return new_listing


def _create_new_version_from_agent(
    db,
    agent: AgentConfig,
) -> AgentConfigVersion | None:
    """
    Creates a new version from the current agent state.
    Automatically increments the version number.
    """
    try:
        # Get the current version to determine the next version number
        current_version = None
        if agent.current_version_id:
            current_version = (
                db.query(AgentConfigVersion)
                .filter(AgentConfigVersion.id == agent.current_version_id)
                .first()
            )

        # Determine the next version number
        if current_version:
            # Parse current version and increment
            current_version_parts = current_version.version_number.split(".")
            if len(current_version_parts) == 3:
                major, minor, patch = map(int, current_version_parts)
                # For now, increment minor version for updates
                new_version_number = f"{major}.{minor + 1}.0"
            else:
                # Fallback if version format is unexpected
                new_version_number = "1.1.0"
        else:
            # No current version found, start with 1.1.0 (since 1.0.0 should have been created)
            new_version_number = "1.1.0"

        # Create new version with current timestamp to ensure proper ordering
        current_time = datetime.now(timezone.utc)

        # Handle model config
        model_config_id = None
        # Get the current model config from the agent's current version
        if agent.current_version_id and current_version and current_version.model_config_id:
            current_model_config = db.query(AgentModelConfig).filter(
                AgentModelConfig.id == current_version.model_config_id
            ).first()
            
            if current_model_config:
                # Create a new model config for this version
                model_config = AgentModelConfig(
                    model_provider=current_model_config.model_provider,
                    model_name=current_model_config.model_name,
                    temperature= current_model_config.temperature,
                    max_tokens=current_model_config.max_tokens,
                )
                db.add(model_config)
                db.flush()
                model_config_id = model_config.id

        # Handle knowledge base
        knowledge_base_id = None
        # Get the current knowledge base from the agent's current version
        if agent.current_version_id and current_version and current_version.knowledge_base_id:
            current_knowledge_base = db.query(AgentKnowledgeBase).filter(
                AgentKnowledgeBase.id == current_version.knowledge_base_id
            ).first()
            
            if current_knowledge_base:
                # Create a new knowledge base for this version
                knowledge_base = AgentKnowledgeBase(
                    files=current_knowledge_base.files if current_knowledge_base.files else [],
                    urls=current_knowledge_base.urls if current_knowledge_base.urls else [],
                )
                db.add(knowledge_base)
                db.flush()
                knowledge_base_id = knowledge_base.id

        new_version = AgentConfigVersion(
            agent_config_id=agent.id,
            model_config_id=model_config_id,
            knowledge_base_id=knowledge_base_id,
            version_number=new_version_number,
            name=agent.name,
            description=agent.description,
            avatar=agent.avatar,
            agent_category=agent.agent_category,
            system_message=agent.system_message,
            workflow_ids=agent.workflow_ids if agent.workflow_ids else [],
            mcp_server_ids=agent.mcp_server_ids if agent.mcp_server_ids else [],
            agent_topic_type=agent.agent_topic_type,
            department=agent.department,
            organization_id=agent.organization_id,
            tone=agent.tone,
            is_bench_employee=agent.is_bench_employee,
            is_changes_marketplace=agent.is_changes_marketplace,
            is_a2a=agent.is_a2a,
            is_customizable=agent.is_customizable,
            capabilities_id=agent.capabilities_id,
            example_prompts=agent.example_prompts if agent.example_prompts else [],
            category=agent.category,
            tags=agent.tags if agent.tags else [],
            status=agent.status,
            version_notes=f"Updated agent - version {new_version_number}",
            created_at=current_time,  # Explicitly set creation time
        )

        db.add(new_version)
        db.flush()  # Get the ID

        logger.info(
            f"Created new version {new_version_number} for agent {agent.id} at {current_time}"
        )
        return new_version

    except Exception as e:
        logger.error(f"Failed to create new version for agent {agent.id}: {str(e)}")
        return None


def _update_derived_agents_change_status(db: Session, source_agent: AgentConfig):
    """
    Update is_updated flag for all agents derived from the source agent.
    This is called when the source agent is updated with template-relevant changes.

    Uses multiple comparison methods to detect meaningful changes:
    1. Timestamp comparison (primary)
    2. Content hash comparison (future enhancement)
    3. Version comparison (if available)
    """
    logger.info(
        f"Starting _update_derived_agents_change_status for source agent {source_agent.id}"
    )
    try:
        # Get all agents that were cloned from this source agent
        derived_agents = (
            db.query(AgentConfig)
            .filter(
                AgentConfig.agent_template_id == source_agent.id,
                AgentConfig.is_imported == True,
            )
            .all()
        )

        logger.info(
            f"Found {len(derived_agents)} derived agents for source {source_agent.id}"
        )

        # For each derived agent, compare with source and set is_updated
        for derived_agent in derived_agents:
            has_changes = _detect_agent_changes(derived_agent, source_agent)

            if has_changes:
                derived_agent.is_updated = True
                logger.info(
                    f"Marked derived agent {derived_agent.id} as having pending updates"
                )
            else:
                logger.info(
                    f"No significant changes detected for derived agent {derived_agent.id}"
                )

        db.commit()

    except Exception as e:
        logger.error(f"Failed to update derived agents change status: {str(e)}")
        db.rollback()


def _detect_agent_changes(derived_agent: AgentConfig, source_agent: AgentConfig) -> bool:
    """
    Detect if there are meaningful changes between a derived agent and its source.

    This method uses multiple comparison strategies to determine if the source agent
    has changes that should trigger an update notification for the derived agent.

    Args:
        derived_agent: The cloned agent
        source_agent: The source agent

    Returns:
        True if changes are detected, False otherwise
    """
    try:
        # Strategy 1: Timestamp comparison (primary method)
        # If source was updated after the derived agent was last synced
        # Add null safety checks for timestamps
        if (
            source_agent.updated_at is not None
            and derived_agent.updated_at is not None
            and source_agent.updated_at > derived_agent.updated_at
        ):
            logger.info(
                f"Timestamp change detected: source updated at {source_agent.updated_at}, "
                f"derived last updated at {derived_agent.updated_at}"
            )
            return True
        elif source_agent.updated_at is not None and derived_agent.updated_at is None:
            # If derived agent has no update timestamp but source does, assume changes
            logger.info(
                f"Timestamp change detected: source has update timestamp but derived agent doesn't"
            )
            return True

        # Strategy 2: Content comparison for key fields
        # Compare important fields that would affect the agent functionality
        content_fields_changed = (
            derived_agent.description != source_agent.description
            or derived_agent.system_message != source_agent.system_message
            or derived_agent.workflow_ids != source_agent.workflow_ids
            or derived_agent.mcp_server_ids != source_agent.mcp_server_ids
            or derived_agent.category != source_agent.category
            or derived_agent.tags != source_agent.tags
            or derived_agent.tone != source_agent.tone
            or derived_agent.capabilities_id != source_agent.capabilities_id
        )

        if content_fields_changed:
            logger.info(f"Content changes detected between agents")
            return True

        # Strategy 3: Version comparison (if versions are tracked)
        # This could be enhanced with semantic versioning in the future
        # Note: Version comparison is now handled through AgentConfigVersion table
        # and current_version_id relationship

        # No significant changes detected
        return False

    except Exception as e:
        logger.error(f"Error detecting agent changes: {str(e)}")
        # In case of error, assume changes exist to be safe
        return True


def _generate_content_hash(agent: AgentConfig) -> str:
    """
    Generate a hash of the agent's content for comparison purposes.

    This method creates a hash based on the agent's key content fields
    that would affect functionality when changed.

    Args:
        agent: The agent to hash

    Returns:
        SHA256 hash of the agent content
    """
    import hashlib
    import json

    try:
        # Create a dictionary of the key content fields
        content_data = {
            "description": agent.description or "",
            "system_message": agent.system_message or "",
            "workflow_ids": agent.workflow_ids or [],
            "mcp_server_ids": agent.mcp_server_ids or [],
            "category": agent.category.value if agent.category else "",
            "tags": agent.tags or [],
            "tone": agent.tone.value if agent.tone else "",
        }

        # Convert to JSON string for consistent hashing
        content_json = json.dumps(content_data, sort_keys=True)

        # Generate SHA256 hash
        return hashlib.sha256(content_json.encode("utf-8")).hexdigest()

    except Exception as e:
        logger.error(f"Error generating content hash: {str(e)}")
        # Return a timestamp-based hash as fallback
        return hashlib.sha256(str(agent.updated_at).encode("utf-8")).hexdigest()