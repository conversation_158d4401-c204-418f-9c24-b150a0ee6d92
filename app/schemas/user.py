from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime
from enum import Enum


class UserBase(BaseModel):
    email: EmailStr
    full_name: str
    organizationId: Optional[str] = None


class UserCreate(UserBase):
    password: str


class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    phone_number: Optional[str] = None
    profile_image: Optional[str] = None
    organizationId: Optional[str] = None


class UserInDB(UserBase):
    id: str
    created_at: datetime
    updated_at: datetime


class UserResponse(UserBase):
    id: str
    phoneNumber: Optional[str] = None
    profileImage: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class LoginRequestData(BaseModel):
    email: EmailStr
    password: str
    fcm_token: Optional[str] = None  # Make it optional to maintain backward compatibility

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str

class OrganisationTokenRequest(BaseModel):
    organisation_id: str

class OrganisationTokenResponse(TokenResponse):
    success: bool = True
    message: str = "Token generated successfully"

class GoogleAuthRequest(BaseModel):
    auth_code: str


class UserList(BaseModel):
    users: List[UserResponse]
    total: int
    page: int
    total_pages: int


class EmailOTP(BaseModel):
    token: str


class UpdatePassword(BaseModel):
    token: str
    new_password: str
    confirm_new_password: str


class ResetPassword(BaseModel):
    current_password: str
    new_password: str
    confirm_new_password: str


class ResetPasswordResponse(BaseModel):
    success: bool
    message: str


class GetAllUsersRequest(BaseModel):
    page: int = Field(..., gt=0, description="Page number (1-based)")
    page_size: int = Field(..., gt=0, description="Number of items per page")
    sort_by: Optional[str] = None
    sort_order: Optional[str] = None
    is_email_verified: Optional[bool] = None
    role: Optional[str] = None
    is_active: Optional[bool] = None
    search: Optional[str] = None


class UserProfileUpdateRequest(BaseModel):
    company: Optional[str] = None
    department: Optional[str] = None
    job_role: Optional[str] = None


class UserInfoResponse(BaseModel):
    userId: str
    email: str
    fullName: str
    createdAt: str
    updatedAt: str
    role: str  # System Role
    company: Optional[str] = None
    department: Optional[str] = None
    jobRole: Optional[str] = None


class ProfileUpdateResponse(BaseModel):
    success: bool
    message: str
    user: Optional[UserInfoResponse] = None  # Include updated user info


class WaitlistCreate(BaseModel):
    email: EmailStr = Field(..., example="<EMAIL>")


class ApproveWaitlistUserRequest(BaseModel):
    user_id: str


class ApproveMultipleWaitlistUsersRequest(BaseModel):
    user_ids: List[str]


# Response Models
class WaitlistEntryResponse(BaseModel):
    id: str
    email: str
    joined_at: datetime
    status: str


class PaginationMetadata(BaseModel):
    total: int
    totalPages: int
    currentPage: int
    pageSize: int
    hasNextPage: bool
    hasPreviousPage: bool


class PaginatedWaitlistResponse(BaseModel):
    data: List[WaitlistEntryResponse]
    metadata: PaginationMetadata


class MessageResponse(BaseModel):
    success: bool
    message: str


class ApproveMultipleResponse(BaseModel):
    success: bool
    message: str
    approved_count: int
    failed_ids: List[str] = []


class StripeCustomerIdUpdate(BaseModel):
    stripe_customer_id: str


class StripeCustomerIdResponse(BaseModel):
    success: bool
    message: str


class StripeCustomerIdFetchResponse(BaseModel):
    stripe_customer_id: str


class PublicKeyCreate(BaseModel):
    name: str = Field(..., description="Name of the API key")
    description: Optional[str] = Field(None, description="Description of the API key")


class PublicKeyResponse(BaseModel):
    success: bool
    message: str
    api_key: Optional['PublicKeyInfo'] = None


class PublicKeyInfo(BaseModel):
    id: str
    name: str
    description: str
    is_active: bool
    user_id: str
    created_at: str
    public_key: Optional[str] = None
    organization_id: Optional[str] = None
    counter: Optional[int] = None


class PublicKeyListResponse(BaseModel):
    success: bool
    message: str
    api_keys: list[PublicKeyInfo]


class PublicKeyValidateRequest(BaseModel):
    public_key: str = Field(..., description="Public key to validate")


class PublicKeyValidateResponse(BaseModel):
    success: bool
    message: str
    is_validated: bool


class PublicKeyDeleteResponse(BaseModel):
    success: bool
    message: str


class PublicKeyDetailsResponse(BaseModel):
    success: bool
    message: str
    api_key: Optional[PublicKeyInfo] = None


# Keep old schemas for backward compatibility
class APIKeyCreate(BaseModel):
    name: str = Field(..., description="Name of the API key")
    project: Optional[str] = Field(None, description="Project associated with the API key")


class APIKeyResponse(BaseModel):
    success: bool
    message: str
    public_key: Optional[str] = None
    private_key: Optional[str] = None


class APIKeyInfo(BaseModel):
    id: str
    name: str
    public_key: str
    private_key: str
    project: Optional[str] = None
    created_at: str


class APIKeyListResponse(BaseModel):
    success: bool
    message: str
    api_keys: list[APIKeyInfo]


class APIKeyDelete(BaseModel):
    key_id: str


class APIKeyDeleteResponse(BaseModel):
    success: bool
    message: str


class APIKeyDetailsResponse(BaseModel):
    success: bool
    message: str
    api_key: Optional[APIKeyInfo] = None


class CredentialCreate(BaseModel):
    key_name: str = Field(..., description="Name of the credential", min_length=1, max_length=20)
    value: str = Field(
        ..., description="Value of the credential (API key, token, etc.)", min_length=1
    )
    description: Optional[str] = Field(None, description="Description of the credential")


class CredentialInfo(BaseModel):
    id: str
    key_name: str
    description: Optional[str]
    value: str
    created_at: str
    updated_at: str
    last_used_at: str


class CredentialResponse(BaseModel):
    success: bool
    message: str
    id: Optional[str] = None
    key_name: Optional[str] = None


class CredentialListResponse(BaseModel):
    success: bool
    message: str
    credentials: list[CredentialInfo]


# Unified Variable Schemas

class VariableType(str, Enum):
    CREDENTIAL = "credential"
    GLOBAL_VARIABLE = "global-variable"


class VariableCreate(BaseModel):
    key_name: str = Field(..., description="Name of the variable", min_length=1, max_length=50)
    value: str = Field(..., description="Value of the variable", min_length=1)
    description: Optional[str] = Field(None, description="Description of the variable")


class VariableUpdate(BaseModel):
    key_name: Optional[str] = Field(None, description="Updated name of the variable", min_length=1, max_length=50)
    value: Optional[str] = Field(None, description="Updated value of the variable", min_length=1)
    description: Optional[str] = Field(None, description="Updated description of the variable")


class VariableInfo(BaseModel):
    id: str
    key_name: str
    description: Optional[str]
    type: VariableType
    value: Optional[str]  # Null for credentials, actual value for global variables
    has_value: bool
    created_at: str
    updated_at: str
    last_used_at: str


class VariableResponse(BaseModel):
    success: bool
    message: str
    id: Optional[str] = None
    key_name: Optional[str] = None


class VariableListResponse(BaseModel):
    success: bool
    message: str
    variables: list[VariableInfo]


class VariableDetailsResponse(BaseModel):
    success: bool
    message: str
    variable: Optional[VariableInfo] = None


class VariableUpdateResponse(BaseModel):
    success: bool
    message: str
    id: Optional[str] = None
    key_name: Optional[str] = None


class VariableDeleteResponse(BaseModel):
    success: bool
    message: str


class CredentialDetailsResponse(BaseModel):
    success: bool
    message: str
    credential: Optional[CredentialInfo] = None


class CredentialDeleteResponse(BaseModel):
    success: bool
    message: str


class CredentialUpdate(BaseModel):
    key_name: Optional[str] = Field(
        None, description="Updated name of the credential", min_length=1, max_length=20
    )
    value: Optional[str] = Field(None, description="Updated value of the credential", min_length=1)
    description: Optional[str] = Field(None, description="Updated description of the credential")


class CredentialUpdateResponse(BaseModel):
    success: bool
    message: str
    credential: Optional[CredentialInfo] = None


class ResetPassword(BaseModel):
    current_password: str
    new_password: str
    confirm_new_password: str


class ResetPasswordResponse(BaseModel):
    success: bool

class PreferenceInfoResponse(BaseModel):
    id: str
    user_id: str
    provider: str
    model: str
    temperature: float
    max_output_tokens: int
    created_at: str
    updated_at: str

class CreatePreferenceRequest(BaseModel):
    provider: str
    model: str
    temperature: float
    max_output_tokens: int

class CreatePreferenceResponse(BaseModel):
    success: bool
    message: str
    preference: Optional[PreferenceInfoResponse] = None

class GetPreferenceResponse(BaseModel):
    success: bool
    message: str
    preference: Optional[PreferenceInfoResponse] = None

class UpdatePreferenceRequest(BaseModel):
    provider: Optional[str] = None
    model: Optional[str] = None
    temperature: Optional[float] = None
    max_output_tokens: Optional[int] = None

class UpdatePreferenceResponse(BaseModel):
    success: bool
    message: str
    preference: Optional[PreferenceInfoResponse] = None

class ListPreferencesResponse(BaseModel):
    success: bool
    message: str
    preferences: List[PreferenceInfoResponse] = []
    message: str
