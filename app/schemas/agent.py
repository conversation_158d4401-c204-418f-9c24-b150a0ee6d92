from enum import Enum
from typing import Optional, Any, List
from pydantic import BaseModel, <PERSON>, validator
from datetime import datetime
import json
from app.schemas.mcp import MCPInDB
from app.schemas.workflow import WorkflowInDB


class ModelData(BaseModel):
    model_id: Optional[str] = None
    context_window: Optional[int] = None
    model_provider: Optional[str] = None
    model_name: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None


class FileEntry(BaseModel):
    file: str
    created_at: str
    size: Optional[int] = None


class UrlEntry(BaseModel):
    url: str
    created_at: str


class ActionTypeEnum(str, Enum):
    POST_CALL = "POST_CALL"
    PRE_CALL = "PRE_CALL"


class ExecutionTypeEnum(str, Enum):
    WORKFLOW = "WORKFLOW"
    MCP = "MCP"


class CallAction(BaseModel):
    """Schema for call action configuration"""
    action_type: ActionTypeEnum
    execution_type: ExecutionTypeEnum
    id: str

    class Config:
        from_attributes = True


class VisibilityEnum(str, Enum):
    PRIVATE = "private"
    PUBLIC = "public"


class StatusEnum(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DRAFT = "draft"
    BENCH = "bench"  # Added to match proto definition


class CreatorRoleEnum(str, Enum):
    MEMBER = "member"
    CREATOR = "creator"
    VIEWER = "viewer"


# New schema that matches the Agent message from agent_graph.proto
class AgentGraphInDB(BaseModel):
    """
    Agent schema that matches the Agent message from agent_graph.proto.
    Used specifically for agent graph service responses.
    """

    id: str
    name: str
    description: str
    department: str
    owner_id: str
    owner_name: str
    user_ids: List[str] = Field(default_factory=list)
    created_at: str  # String format as per proto
    updated_at: str  # String format as per proto
    visibility: VisibilityEnum
    status: StatusEnum
    creator_role: CreatorRoleEnum

    class Config:
        from_attributes = True


class OwnerTypeEnum(str, Enum):
    USER = "user"
    ENTERPRISE = "enterprise"
    PLATFORM = "platform"


class CategoryEnum(str, Enum):
    ENGINEERING = "engineering"
    MARKETING = "marketing"
    SALES = "sales"
    CUSTOMER_SUPPORT = "customer_support"
    HUMAN_RESOURCES = "human_resources"
    FINANCE = "finance"
    OPERATIONS = "operations"
    GENERAL = "general"
    PRODUCTIVITY = "productivity"
    ANALYTICS = "analytics"
    CONTENT = "content"
    COLLABORATION = "collaboration"
    DEVELOPER = "developer"
    DATA_SCIENCE = "data_science"
    AUTOMATION = "automation"
    DATA_PIPELINE = "data_pipeline"
    INTEGRATION = "integration"
    WEB_SCRAPING = "web_scraping"
    API = "api"
    EMAIL = "email"
    LLM_ORCHESTRATION = "llm_orchestration"
    DATABASE = "database"
    FILE_MANAGEMENT = "file_management"
    SCHEDULING = "scheduling"
    MONITORING = "monitoring"
    CRM = "crm"
    NOTIFICATIONS = "notifications"
    DOCUMENT_PROCESSING = "document_processing"
    DEVOPS = "devops"
    HR = "hr"


class AgentCategoryEnum(str, Enum):
    USER_PROXY = "user_proxy"
    ASSISTANT = "assistant"
    AI_AGENT = "ai_agent"


class AgentToneEnum(str, Enum):
    PROFESSIONAL = "professional"
    FRIENDLY = "friendly"
    CASUAL = "casual"
    FORMAL = "formal"
    ENTHUSIASTIC = "enthusiastic"


class InputModeEnum(str, Enum):
    TEXT = "text"
    VOICE = "voice"
    IMAGE = "image"
    FILE_UPLOAD = "file_upload"


class OutputModeEnum(str, Enum):
    TEXT = "text"
    VOICE = "voice"
    IMAGE = "image"
    FILE_GENERATION = "file_generation"


class ResponseModelEnum(str, Enum):
    STREAMING_RESPONSE = "streaming_response"
    PUSH_NOTIFICATION = "push_notification"
    STATE_TRANSITION_HISTORY = "state_transition_history"


class Capability(BaseModel):
    title: str
    description: str


class TaskCounts(BaseModel):
    total: int = 0
    completed: int = 0
    inProgress: int = 0


class AgentCapabilitiesDataBase(BaseModel):
    capabilities: Optional[List[Capability]] = None  # For JSON
    input_modes: Optional[List[InputModeEnum]] = None
    output_modes: Optional[List[OutputModeEnum]] = None
    response_model: Optional[List[ResponseModelEnum]] = None  # Moved here


class AgentCapabilitiesCreate(AgentCapabilitiesDataBase):
    pass


class AgentCapabilitiesInDB(AgentCapabilitiesDataBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

    @validator("capabilities", pre=True)  # Add this validator
    def validate_capabilities_json(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                # Handle error or return v if it's not meant to be JSON
                # For now, let's assume it should be parsable if string
                raise ValueError("Invalid JSON string for capabilities")
        return v


class VariableTypeEnum(str, Enum):
    TEXT = "text"
    NUMBER = "number"
    JSON = "json"


class AgentVariableBase(BaseModel):
    name: str = Field(..., min_length=1)
    description: Optional[str] = None
    type: VariableTypeEnum
    default_value: Optional[Any] = None


class AgentVariableCreate(AgentVariableBase):
    pass


class AgentVariableInDB(AgentVariableBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True  # Pydantic v2 from_attributes

    @validator("default_value", pre=True, always=True)
    def parse_db_default_value(cls, v, values):
        if v is None:
            return None
        var_type = values.get("type")
        if isinstance(v, str):  # Only attempt parsing if it's a string from DB
            if var_type == VariableTypeEnum.JSON:
                try:
                    return json.loads(v)
                except json.JSONDecodeError:
                    return v  # Or raise
            elif var_type == VariableTypeEnum.NUMBER:
                try:
                    return float(v)  # Or int
                except ValueError:
                    return v  # Or raise
        return v


class AgentBase(BaseModel):
    name: str = Field(..., min_length=1)
    description: Optional[str] = None
    avatar: Optional[str] = None
    system_message: str
    model_provider: Optional[str] = None
    model_name: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    workflow_ids: Optional[List[str]] = None
    mcp_server_ids: Optional[List[str]] = None
    agent_topic_type: Optional[str] = None
    tags: Optional[List[str]] = None
    department: Optional[str] = None
    organization_id: Optional[str] = None
    tone: Optional[AgentToneEnum] = None
    files: Optional[List[str]] = None
    urls: Optional[List[str]] = None
    is_a2a: Optional[bool] = False
    is_customizable: Optional[bool] = False
    agent_capabilities: Optional[AgentCapabilitiesCreate] = None
    example_prompts: Optional[List[str]] = None
    category: Optional[CategoryEnum] = None
    variables: Optional[List[AgentVariableCreate]] = Field(default_factory=list)


class AgentVariablesUpdatePayload(BaseModel):
    variables: List[AgentVariableCreate] = Field(default_factory=list)


class AgentCreate(AgentBase):
    visibility: VisibilityEnum
    status: Optional[StatusEnum] = StatusEnum.ACTIVE.value


class AgentInDB(BaseModel):
    id: Optional[str] = None
    name: str
    description: str
    avatar: Optional[str] = None
    owner_id: str
    user_ids: List[str] = Field(default_factory=list)
    owner_type: str
    template_id: Optional[str] = None
    template_owner_id: Optional[str] = None
    is_imported: Optional[bool] = False
    is_bench_employee: Optional[bool] = False
    is_changes_marketplace: Optional[bool] = False
    is_updated: Optional[bool] = False
    is_a2a: Optional[bool] = False
    is_customizable: Optional[bool] = False
    agent_category: str
    system_message: str
    model_provider: Optional[str] = None
    model_name: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    workflow_ids: Optional[List[str]] = Field(default_factory=list)
    mcp_server_ids: Optional[List[str]] = Field(default_factory=list)
    agent_topic_type: Optional[str] = None
    visibility: str
    tags: Optional[List[str]] = None
    status: str
    department: Optional[str] = None
    organization_id: Optional[str] = None
    tone: Optional[str] = None
    files: Optional[List[FileEntry]] = Field(default_factory=list)
    urls: Optional[List[UrlEntry]] = Field(default_factory=list)
    capabilities: Optional[AgentCapabilitiesInDB] = Field(default=None, alias="agent_capabilities")
    example_prompts: Optional[List[str]] = None
    category: Optional[CategoryEnum] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    variables: List[AgentVariableInDB] = Field(default_factory=list)
    task_counts: Optional[TaskCounts] = None
    model_data: Optional[ModelData] = None

    class Config:
        from_attributes = True

    @validator("workflow_ids", "mcp_server_ids", "variables", pre=True)
    def validate_list_fields(cls, v):
        if v is None:
            return []
        return v


class AgentResponse(BaseModel):
    success: bool
    message: str
    agent: Optional[AgentInDB] = None


class DeleteAgentResponse(BaseModel):
    success: bool
    message: str


class ListAgentsResponse(BaseModel):
    agents: List[AgentInDB]
    total: int
    page: int
    total_pages: int


# --- Paginated  Model ---
class PaginationMetadata(BaseModel):
    total: int
    totalPages: int
    currentPage: int
    pageSize: int
    hasNextPage: bool
    hasPreviousPage: bool


class PaginatedWorkflowResponse(BaseModel):
    data: List[AgentInDB]
    metadata: PaginationMetadata


class PaginatedAgentGraphResponse(BaseModel):
    """
    Paginated response for agent graph service endpoints.
    Uses AgentGraphInDB which matches the proto Agent message structure.
    """

    data: List[AgentGraphInDB]
    metadata: PaginationMetadata


class PreSignedUrlRequest(BaseModel):
    fileName: str
    fileType: str
    filePath: str


class PreSignedUrlResponse(BaseModel):
    success: bool
    url: str


class AgentTemplateInDB(BaseModel):
    id: str
    name: str
    description: str
    avatar: str
    agent_category: str
    system_message: str
    model_provider: Optional[str] = None
    model_name: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    workflow_ids: Optional[List[str]] = None
    mcp_server_ids: Optional[List[str]] = None
    agent_topic_type: Optional[str] = None
    tags: Optional[List[str]] = None
    created_at: datetime
    updated_at: datetime
    department: Optional[str] = None
    organization_id: Optional[str] = None
    tone: Optional[str] = None
    files: Optional[List[FileEntry]] = None
    urls: Optional[List[UrlEntry]] = None
    owner_id: str
    use_count: Optional[int] = 0
    is_a2a: Optional[bool] = False
    is_customizable: Optional[bool] = False
    capabilities: Optional[AgentCapabilitiesInDB] = Field(default=None, alias="agent_capabilities")
    example_prompts: Optional[List[str]] = None
    category: Optional[CategoryEnum] = None
    task_counts: Optional[TaskCounts] = None

    class Config:
        from_attributes = True


class AgentTemplateResponse(BaseModel):
    success: bool
    message: str
    template: AgentTemplateInDB


class ListTemplatesResponse(BaseModel):
    templates: List[AgentTemplateInDB]
    total: int
    page: int
    total_pages: int


class CreateAgentFromTemplateResponse(BaseModel):
    success: bool
    message: str


class AgentWithMCPsInDB(AgentInDB):
    mcps: Optional[List[MCPInDB]] = Field(default_factory=list)
    workflows: Optional[List[WorkflowInDB]] = Field(default_factory=list)


class AgentWithMCPsResponse(BaseModel):
    success: bool
    message: str
    agent: AgentWithMCPsInDB


class AgentCoreDetailsUpdatePayload(BaseModel):
    name: Optional[str] = Field(None, min_length=1)
    description: Optional[str] = Field(None)
    avatar: Optional[str] = Field(None)
    system_message: Optional[str] = Field(None)
    model_provider: Optional[str] = Field(None)
    model_name: Optional[str] = Field(None)
    temperature: Optional[float] = Field(None)
    max_tokens: Optional[int] = None
    department: Optional[str] = Field(None)
    tone: Optional[AgentToneEnum] = Field(None)
    agent_topic_type: Optional[str] = Field(None)
    category: Optional[CategoryEnum] = Field(None)


class AgentAdvanceSettingsUpdatePayload(BaseModel):
    name: Optional[str] = Field(None, min_length=1)
    description: Optional[str] = Field(None)
    avatar: Optional[str] = Field(None)
    system_message: Optional[str] = Field(None)
    department: Optional[str] = Field(None)
    tone: Optional[AgentToneEnum] = Field(None)
    agent_topic_type: Optional[str] = Field(None)
    category: Optional[CategoryEnum] = Field(None)


class AgentKnowledgeUpdatePayload(BaseModel):
    files: Optional[List[str]] = Field(None)  # Keep as List[str] for request payload
    urls: Optional[List[str]] = Field(None)  # Keep as List[str] for request payload


class AgentMcpServersUpdatePayload(BaseModel):
    mcp_server_ids: List[str]


class AgentWorkflowsUpdatePayload(BaseModel):
    workflow_ids: List[str]


class AgentSettingsUpdatePayload(BaseModel):
    user_ids: Optional[List[str]] = Field(None)
    tags: Optional[List[str]] = None
    status: Optional[StatusEnum] = Field(None)
    is_changes_marketplace: Optional[bool] = Field(None)
    is_updated: Optional[bool] = Field(None)
    is_bench_employee: Optional[bool] = Field(None)
    is_a2a: Optional[bool] = Field(None)
    is_customizable: Optional[bool] = Field(None)
    example_prompts: Optional[List[str]] = None


class AgentPartUpdateResponseAPI(BaseModel):
    success: bool
    message: str
    agent: Optional[AgentInDB] = None


class ToggleAgentVisibilityResponseAPI(BaseModel):
    success: bool
    message: str
    agent: Optional[AgentInDB] = None


class AgentCapabilitiesUpdatePayload(BaseModel):
    capabilities: Optional[List[Capability]] = Field(None)
    input_modes: Optional[List[InputModeEnum]] = Field(None)
    output_modes: Optional[List[OutputModeEnum]] = Field(None)
    response_model: Optional[List[ResponseModelEnum]] = Field(None)


class AgentCombinedUpdatePayload(BaseModel):
    """
    Comprehensive payload for updating all agent fields in a single request.
    This combines all fields from individual update payloads:
    - Core Details: name, description, avatar, system_message, model settings, etc.
    - Knowledge: files, urls
    - Capabilities: capabilities, input_modes, output_modes, response_model
    - MCP Servers: mcp_server_ids
    - Variables: variables
    - Workflows: workflow_ids
    - Settings: user_ids, tags, status, flags, example_prompts
    """

    # Core Details fields (from AgentCoreDetailsUpdatePayload)
    name: Optional[str] = Field(None, min_length=1)
    description: Optional[str] = Field(None)
    avatar: Optional[str] = Field(None)
    system_message: Optional[str] = Field(None)
    model_provider: Optional[str] = Field(None)
    model_name: Optional[str] = Field(None)
    temperature: Optional[float] = Field(None)
    max_tokens: Optional[int] = None
    department: Optional[str] = Field(None)
    tone: Optional[AgentToneEnum] = Field(None)
    agent_topic_type: Optional[str] = Field(None)
    category: Optional[CategoryEnum] = Field(None)

    # Knowledge fields (from AgentKnowledgeUpdatePayload)
    files: Optional[List[str]] = Field(None)
    urls: Optional[List[str]] = Field(None)

    # Capabilities fields (from AgentCapabilitiesUpdatePayload)
    capabilities: Optional[List[Capability]] = Field(None)
    input_modes: Optional[List[InputModeEnum]] = Field(None)
    output_modes: Optional[List[OutputModeEnum]] = Field(None)
    response_model: Optional[List[ResponseModelEnum]] = Field(None)

    # MCP Servers fields (from AgentMcpServersUpdatePayload)
    mcp_server_ids: Optional[List[str]] = Field(None)

    # Variables fields (from AgentVariablesUpdatePayload)
    variables: Optional[List[AgentVariableCreate]] = Field(None)

    # Workflows fields (from AgentWorkflowsUpdatePayload)
    workflow_ids: Optional[List[str]] = Field(None)

    # Settings fields (from AgentSettingsUpdatePayload)
    user_ids: Optional[List[str]] = Field(None)
    tags: Optional[List[str]] = Field(None)
    status: Optional[StatusEnum] = Field(None)
    is_changes_marketplace: Optional[bool] = Field(None)
    is_updated: Optional[bool] = Field(None)
    is_bench_employee: Optional[bool] = Field(None)
    is_a2a: Optional[bool] = Field(None)
    is_customizable: Optional[bool] = Field(None)
    example_prompts: Optional[List[str]] = Field(None)


class AgentAvatarBase(BaseModel):
    url: str


class AgentAvatarCreate(AgentAvatarBase):
    pass


class AgentAvatarInDB(AgentAvatarBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AgentAvatarResponse(BaseModel):
    success: bool
    message: str
    avatar: Optional[AgentAvatarInDB] = None


class ListAgentAvatarsResponse(BaseModel):
    avatars: List[AgentAvatarInDB]
    metadata: PaginationMetadata


# Agent Version Management Schemas
class AgentModelConfigInDB(BaseModel):
    id: str
    model_provider: Optional[str] = None
    model_name: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AgentKnowledgeBaseInDB(BaseModel):
    id: str
    files: Optional[List[FileEntry]] = Field(default_factory=list)
    urls: Optional[List[UrlEntry]] = Field(default_factory=list)
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AgentMarketplaceListingInDB(BaseModel):
    id: str
    agent_config_id: str
    agent_version_id: str
    listing_title: Optional[str] = None
    listing_description: Optional[str] = None
    is_featured: Optional[bool] = False
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AgentVersionInDB(BaseModel):
    id: str
    agent_config_id: str
    version_number: str
    name: str
    description: Optional[str] = None
    avatar: Optional[str] = None
    system_message: str
    agent_topic_type: Optional[str] = None
    visibility: str
    tags: Optional[List[str]] = Field(default_factory=list)
    status: str
    department: Optional[str] = None
    organization_id: Optional[str] = None
    tone: Optional[str] = None
    is_a2a: Optional[bool] = False
    is_customizable: Optional[bool] = False
    example_prompts: Optional[List[str]] = Field(default_factory=list)
    category: Optional[str] = None
    workflow_ids: Optional[List[str]] = Field(default_factory=list)
    mcp_server_ids: Optional[List[str]] = Field(default_factory=list)
    agent_model_config: Optional[AgentModelConfigInDB] = None
    knowledge_base: Optional[AgentKnowledgeBaseInDB] = None
    marketplace_listing: Optional[AgentMarketplaceListingInDB] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ListAgentVersionsResponse(BaseModel):
    success: bool
    message: str
    versions: List[AgentVersionInDB]
    total: int
    page: int
    total_pages: int
    current_version_id: Optional[str] = None


class GetAgentVersionResponse(BaseModel):
    success: bool
    message: str
    version: AgentVersionInDB


class SwitchAgentVersionResponse(BaseModel):
    success: bool
    message: str
    new_current_version: AgentVersionInDB


class CreateAgentVersionRequest(BaseModel):
    publish_to_marketplace: Optional[bool] = False


class CreateAgentVersionResponse(BaseModel):
    success: bool
    message: str
    version_created: bool
    marketplace_updated: bool
    version_number: Optional[str] = None
    version_id: Optional[str] = None
    marketplace_listing_id: Optional[str] = None


# Agent Platform By IDs Schemas
class AgentsByIdsRequest(BaseModel):
    ids: List[str] = Field(..., description="List of agent IDs to retrieve")


class AgentsByIdsResponse(BaseModel):
    success: bool
    message: str
    agents: List[AgentInDB]


# Call Actions Schemas
class UpdateCallActionsRequest(BaseModel):
    """Request schema for updating agent call actions"""
    call_actions: List[CallAction] = Field(default_factory=list)

    class Config:
        from_attributes = True

    @validator("call_actions")
    def validate_unique_call_actions(cls, v):
        """
        Validate that there is only one call action for each combination of action_type and execution_type.
        Maximum of 4 call actions allowed (one for each combination).
        """
        if not v:
            return v

        seen_combinations = set()
        for action in v:
            combination = (action.action_type, action.execution_type)
            if combination in seen_combinations:
                raise ValueError(
                    f"Duplicate call action found for action_type='{action.action_type}' "
                    f"and execution_type='{action.execution_type}'. Only one call action "
                    f"is allowed per combination."
                )
            seen_combinations.add(combination)

        # Optional: Validate maximum number of call actions
        if len(v) > 4:
            raise ValueError(
                f"Maximum of 4 call actions allowed (one for each combination of "
                f"action_type and execution_type). Found {len(v)} call actions."
            )

        return v


class UpdateCallActionsResponse(BaseModel):
    """Response schema for updating agent call actions"""
    success: bool
    message: str

    class Config:
        from_attributes = True


class GetCallActionsResponse(BaseModel):
    """Response schema for getting agent call actions"""
    success: bool
    message: str
    call_actions: List[CallAction] = Field(default_factory=list)

    class Config:
        from_attributes = True
