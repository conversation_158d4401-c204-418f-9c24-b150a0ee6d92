from pydantic import BaseModel, Field
from typing import List, Optional

# Simple folder info model
class FolderInfo(BaseModel):
    id: str
    name: str

# Disconnect Drive models
class DisconnectDriveRequest(BaseModel):
    organisation_id: str

class DisconnectDriveResponse(BaseModel):
    success: bool
    message: str

# Sync Drive models
class SyncDriveRequest(BaseModel):
    user_id: str
    organisation_id: str
    full_sync: bool = False

class SyncDriveResponse(BaseModel):
    success: bool
    message: str
    files_synced: int
    folders_synced: int
    sync_status: str

# List Files models
class ListFilesRequest(BaseModel):
    user_id: str
    folder_id: Optional[str] = None
    page: int = 1
    page_size: int = 10

class DriveFileModel(BaseModel):
    id: str
    name: str
    mime_type: str
    web_view_link: str
    created_time: str
    modified_time: str
    parent_folder_id: str
    size: int
    shared_with: List[str] = []
    is_folder: bool
    child_count: int = 0

class ListFilesResponse(BaseModel):
    success: bool
    message: str
    files: List[DriveFileModel] = []
    total_count: int
    page: int
    page_size: int

# File Details models
class GetFileDetailsRequest(BaseModel):
    user_id: str
    file_id: str

class GetFileDetailsResponse(BaseModel):
    success: bool
    message: str
    file: Optional[DriveFileModel] = None

# Get Folder By ID models
class GetFolderByIdRequest(BaseModel):
    organisation_id: str
    folder_id: str

class GetFolderByIdResponse(BaseModel):
    success: bool
    message: str
    folder: Optional[DriveFileModel] = None
    children: List[DriveFileModel] = []

# Sync Folder By IDs models
class SyncFolderByIdsRequest(BaseModel):
    organisation_id: str
    folder_ids: List[str]

class SyncFolderByIdsResponse(BaseModel):
    success: bool
    message: str
    files_synced: int
    folders_synced: int
    sync_status: str
    synced_folders: List[FolderInfo] = []

# Check File Access models
class CheckFileAccessRequest(BaseModel):
    user_id: str
    file_id: str

class CheckFileAccessResponse(BaseModel):
    success: bool
    message: str
    has_access: bool

# Search Similar Documents models
class SearchSimilarDocumentsRequest(BaseModel):
    user_id: str
    query_text: str
    top_k: int = 5
    agent_id: Optional[str] = None
    organisation_id: str
    file_ids: Optional[List[str]] = None
    least_score: Optional[float] = None  # Optional minimum score threshold for results

# Entity information from knowledge graph
class EntityInfo(BaseModel):
    id: str
    name: str
    type: str
    properties: dict = {}  # Additional entity properties
    relevance_score: float = 0.0  # Relevance score for this entity

# Relationship information from knowledge graph
class RelationshipInfo(BaseModel):
    id: str
    type: str
    source_entity_id: str
    target_entity_id: str
    source_entity_name: str = ""  # Name of source entity
    target_entity_name: str = ""  # Name of target entity
    properties: dict = {}  # Additional relationship properties
    confidence_score: float = 0.0  # Confidence score for this relationship
    relevance_score: float = 0.0  # Relevance score for this relationship
    context: str = ""  # Context information about the relationship

# Graph context containing all knowledge graph discoveries
class GraphContext(BaseModel):
    all_entities: List[EntityInfo] = []  # All entities discovered (chunk-based + graph-discovered)
    all_relationships: List[RelationshipInfo] = []  # All relationships discovered

class SearchResultItem(BaseModel):
    file_id: str
    file_name: str
    mime_type: str
    web_view_link: str
    created_time: str
    modified_time: str
    score: float
    vector_id: str
    chunk_text: str
    search_type: Optional[str] = None  # Type of search performed (e.g., "hybrid", "semantic", "graph")

class SearchSimilarDocumentsResponse(BaseModel):
    success: bool
    message: str
    results: List[SearchResultItem] = []
    graph_context: Optional[GraphContext] = None  # Separate graph context with all discoveries

# Batch Search Similar Documents models
class BatchSearchSimilarDocumentsRequest(BaseModel):
    user_id: str
    query_texts: List[str]
    top_k: int = 5
    agent_id: Optional[str] = None
    organisation_id: str
    file_ids: Optional[List[str]] = None
    least_score: Optional[float] = None  # Optional minimum score threshold for results

class QueryResults(BaseModel):
    query_text: str
    results: List[SearchResultItem] = []

class BatchSearchSimilarDocumentsResponse(BaseModel):
    success: bool
    message: str
    query_results: List[QueryResults] = []

# Sync File By URL models
class SyncFileByUrlRequest(BaseModel):
    drive_url: List[str]  # Changed from str to List[str] to match proto (repeated string)
    agent_id: str
    user_id: Optional[str] = None
    organisation_id: str

# Information about a synced file
class SyncedFileInfo(BaseModel):
    file_id: str
    file_name: str
    drive_url: str
    sync_status: str  # "completed" or "failed"
    error_message: str = ""  # Error message if sync failed

class SyncFileByUrlResponse(BaseModel):
    success: bool
    message: str
    synced_files: List[SyncedFileInfo] = []  # Information about all synced files
    total_files: int  # Total number of URLs processed
    successful_syncs: int  # Number of successful syncs
    failed_syncs: int  # Number of failed syncs

# List Top Level Folders models
class ListTopLevelFoldersRequest(BaseModel):
    organisation_id: str

class ListTopLevelFoldersResponse(BaseModel):
    success: bool
    message: str
    folders: List[FolderInfo] = []

# OAuth-related models for Google Drive

# Initiate OAuth models
class InitiateOAuthRequest(BaseModel):
    organisation_id: Optional[str] = "" 
    source_name: Optional[str] = ""  # Optional name for the OAuth source

class InitiateOAuthResponse(BaseModel):
    success: bool
    message: str
    oauth_url: str  # OAuth authorization URL to redirect user to
    state: str  # State parameter for CSRF protection

# Complete OAuth models
class CompleteOAuthRequest(BaseModel):
    code: str  # Authorization code from Google OAuth callback
    state: str  # State parameter for CSRF protection

# Google Drive source information
class DriveSourceInfo(BaseModel):
    id: str  # Source ID
    organisation_id: str  # Organisation ID
    name: str  # Source name
    auth_type: str  # Authentication type ('oauth' or 'service_account')
    user_email: str  # OAuth user email (for OAuth sources)
    created_at: str  # Creation timestamp
    updated_at: str  # Last update timestamp

class CompleteOAuthResponse(BaseModel):
    success: bool
    message: str
    source: Optional[DriveSourceInfo] = None  # Created OAuth source information
    synced_files: List[SyncedFileInfo] = []  # Files synced during source creation

# Refresh Token models
class RefreshTokenRequest(BaseModel):
    organisation_id: str

class RefreshTokenResponse(BaseModel):
    success: bool
    message: str
    expires_at: str  # New token expiry time (ISO format)

# Revoke Token models
class RevokeTokenRequest(BaseModel):
    organisation_id: str

class RevokeTokenResponse(BaseModel):
    success: bool
    message: str