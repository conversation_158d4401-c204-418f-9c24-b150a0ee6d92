import enum
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

# Common Pagination Schema
class PaginationMetadataSchema(BaseModel):
    current_page: int
    total_pages: int
    total_items: int
    page_size: int

class PlanType(str, enum.Enum):
    FREE = "FREE"
    PRO = "PRO"

class SubscriptionStatus(str, enum.Enum):
    ACTIVE = "ACTIVE"
    CANCELED = "CANCELED"
    INCOMPLETE = "INCOMPLETE"
    PAST_DUE = "PAST_DUE"
    TRIALING = "TRIALING"

class CreateCheckoutSessionRequestSchema(BaseModel):
    organisation_id: str
    plan_id_code: str
    success_url: str
    cancel_url: str

class CreateCheckoutSessionResponseSchema(BaseModel):
    checkout_url: str
    session_id: str
    success: bool
    message: Optional[str] = None

class CreateCustomerPortalSessionRequestSchema(BaseModel):
    organisation_id: str
    return_url: str

class CreateCustomerPortalSessionResponseSchema(BaseModel):
    portal_url: str
    success: bool
    message: Optional[str] = None

class SubscriptionDataSchema(BaseModel):
    id: str
    organisation_id: str
    plan_id_code: PlanType
    status: SubscriptionStatus
    current_period_start: Optional[datetime] = None
    current_period_end: Optional[datetime] = None
    subscription_credits: float
    current_credits: float

class SubscriptionResponseSchema(BaseModel):
    success: bool
    message: str
    subscription: Optional[SubscriptionDataSchema] = None

class CancelSubscriptionRequestSchema(BaseModel):
    organisation_id: str

class GenericResponseSchema(BaseModel):
    success: bool
    message: Optional[str] = None

class ModelCostSchema(BaseModel):
    input_cost_per_million_tokens: float
    output_cost_per_million_tokens: float

class DeductCreditsRequestSchema(BaseModel):
    organisation_id: str
    agent_id: Optional[str] = None
    input_tokens: int
    output_tokens: int
    consumed_credits: float
    description: Optional[str] = None

class DeductCreditsDataSchema(BaseModel):
    new_balance: float

class DeductCreditsResponseSchema(BaseModel):
    success: bool
    message: str
    data: Optional[DeductCreditsDataSchema] = None

class CreditBalanceDataSchema(BaseModel):
    credit_balance: float

class CreditBalanceResponseSchema(BaseModel):
    success: bool
    message: str
    data: Optional[CreditBalanceDataSchema] = None

class TokenUsageLogEntrySchema(BaseModel):
    id: str
    organisation_id: str
    user_id: str
    input_tokens: int
    output_tokens: int
    total_cost: float
    total_credits:float
    date: str

class GetTokenUsageResponseSchema(BaseModel):
    success: bool
    message: str
    token_usage_logs: List[TokenUsageLogEntrySchema]

class PaymentPlanCreateSchema(BaseModel):
    name: str
    credit_amount: float
    price: float
    stripe_price_id: Optional[str] = None
    is_default: bool = False

class PaymentPlanSchema(BaseModel):
    id: int
    plan_id_code: str
    name: str
    credit_amount: float
    price: float
    stripe_price_id: Optional[str] = None
    is_default: bool

    class Config:
        from_attributes = True

class ListPlansResponseSchema(BaseModel):
    success: bool
    message: str
    plans: List[PaymentPlanSchema]
    metadata: PaginationMetadataSchema

class CalculateCreditsRequestSchema(BaseModel):
    model_name: str
    provider_name: str
    input_tokens: int
    output_tokens: int

class CalculateCreditsDataSchema(BaseModel):
    credits: float
    cost_in_usd: float
    input_price_per_token: float
    output_price_per_token: float

class CalculateCreditsResponseSchema(BaseModel):
    success: bool
    message: str
    data: Optional[CalculateCreditsDataSchema] = None

# --- Topup Plan Schemas ---

class TopupPlanSchema(BaseModel):
    id: int
    plan_id_code: str
    name: str
    credit_amount: float
    price: float
    stripe_price_id: Optional[str] = None

    class Config:
        from_attributes = True

class CreateTopupCheckoutSessionRequestSchema(BaseModel):
    organisation_id: str
    topup_plan_id_code: str
    success_url: str
    cancel_url: str

class CreateTopupCheckoutSessionResponseSchema(BaseModel):
    checkout_url: str
    session_id: str
    success: bool
    error_message: Optional[str] = None

class ListTopupPlansResponseSchema(BaseModel):
    success: bool
    message: str
    topup_plans: List[TopupPlanSchema]
    metadata: PaginationMetadataSchema

class TopupPlanCreateSchema(BaseModel):
    name: str
    credit_amount: float
    price: float
    stripe_price_id: Optional[str] = None

class PaymentTransactionSchema(BaseModel):
    id: str
    organisation_id: str
    transaction_type: str
    status: str
    amount_currency: int
    currency: str
    description: str
    stripe_charge_id: Optional[str] = None
    stripe_invoice_id: Optional[str] = None
    invoice_url: Optional[str] = None
    subscription_id: Optional[str] = None
    created_at: str

class GetPaymentTransactionsResponseSchema(BaseModel):
    success: bool
    message: str
    transactions: List[PaymentTransactionSchema]
    metadata: PaginationMetadataSchema