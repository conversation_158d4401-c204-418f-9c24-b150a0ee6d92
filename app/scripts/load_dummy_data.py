"""
Script to load dummy data into the workflow service database.
"""

import sys
import os

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.workflow import WorkflowTemplate, Workflow
from app.models.workflow_rating import WorkflowRating
from app.utils.dummy_data.workflow_dummy_data import (
    WORKFLOW_TEMPLATE_DUMMY_DATA,
    WORKFLOW_DUMMY_DATA,
    WORKFLOW_RATING_DUMMY_DATA,
)


def load_workflow_templates(db: Session):
    """Load workflow template dummy data into the database."""
    print("Loading workflow templates...")
    for template_data in WORKFLOW_TEMPLATE_DUMMY_DATA:
        # Check if template already exists
        existing_template = db.query(WorkflowTemplate).filter(
            WorkflowTemplate.id == template_data["id"]
        ).first()
        
        if existing_template:
            print(f"Template {template_data['name']} already exists, skipping.")
            continue
        
        # Create new template
        template = WorkflowTemplate(**template_data)
        db.add(template)
        print(f"Added template: {template_data['name']}")
    
    db.commit()
    print("Workflow templates loaded successfully.")


def load_workflows(db: Session):
    """Load workflow dummy data into the database."""
    print("Loading workflows...")
    for workflow_data in WORKFLOW_DUMMY_DATA:
        # Check if workflow already exists
        existing_workflow = db.query(Workflow).filter(
            Workflow.id == workflow_data["id"]
        ).first()
        
        if existing_workflow:
            print(f"Workflow {workflow_data['name']} already exists, skipping.")
            continue
        
        # Create new workflow
        workflow = Workflow(**workflow_data)
        db.add(workflow)
        print(f"Added workflow: {workflow_data['name']}")
    
    db.commit()
    print("Workflows loaded successfully.")


def load_workflow_ratings(db: Session):
    """Load workflow rating dummy data into the database."""
    print("Loading workflow ratings...")
    for rating_data in WORKFLOW_RATING_DUMMY_DATA:
        # Check if rating already exists
        existing_rating = db.query(WorkflowRating).filter(
            WorkflowRating.id == rating_data["id"]
        ).first()
        
        if existing_rating:
            print(f"Rating {rating_data['id']} already exists, skipping.")
            continue
        
        # Create new rating
        rating = WorkflowRating(**rating_data)
        db.add(rating)
        print(f"Added rating for workflow: {rating_data['workflow_id']}")
    
    db.commit()
    print("Workflow ratings loaded successfully.")


def load_all_dummy_data():
    """Load all dummy data into the database."""
    db = SessionLocal()
    try:
        load_workflow_templates(db)
        load_workflows(db)
        load_workflow_ratings(db)
        print("All workflow service dummy data loaded successfully.")
    except Exception as e:
        print(f"Error loading dummy data: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    load_all_dummy_data()
