import json
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.agent import AgentConfig, AgentKnowledgeBase, AgentConfigVersion, Base

def extract_agent_knowledge_base_data():
    """
    Extracts agent knowledge base data (agent_id, urls, files) from the database
    and saves it to a JSON file.
    """
    # Ensure the database URI is set
    if not settings.SQLALCHEMY_DATABASE_URI:
        raise ValueError("SQLALCHEMY_DATABASE_URI is not configured in settings.")

    # Create engine and session
    engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    # Create tables if they don't exist (for script execution, not for production app)
    # Base.metadata.create_all(bind=engine)

    data_to_extract = []

    with SessionLocal() as db:
        # Query AgentConfigVersion and join with AgentKnowledgeBase
        # We need AgentConfigVersion to link to AgentConfig (for agent_id)
        # and to AgentKnowledgeBase (for urls and files)
        results = (
            db.query(
                AgentConfig.id,
                AgentKnowledgeBase.urls,
                AgentKnowledgeBase.files
            )
            .outerjoin(AgentConfigVersion, AgentConfig.current_version_id == AgentConfigVersion.id)
            .outerjoin(AgentKnowledgeBase, AgentConfigVersion.knowledge_base_id == AgentKnowledgeBase.id)
            .all()
        )

        for agent_id, urls, files in results:
            data_to_extract.append({
                "agent_id": agent_id,
                "urls": urls if urls is not None else [],
                "files": files if files is not None else []
            })

    output_dir = "extracted_data"
    os.makedirs(output_dir, exist_ok=True)
    output_file = os.path.join(output_dir, "agent_knowledge_base_data.json")

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(data_to_extract, f, indent=4)

    print(f"Extracted {len(data_to_extract)} agent knowledge base entries to {output_file}")

if __name__ == "__main__":
    # This part is for local testing/execution of the script.
    # In a real application, you'd typically run this via a CLI command or similar.
    try:
        extract_agent_knowledge_base_data()
    except Exception as e:
        print(f"An error occurred: {e}")
        print("Please ensure your .env file is correctly configured with database credentials.")
        print("Example .env configuration:")
        print("DB_HOST=localhost")
        print("DB_PORT=5432")
        print("DB_USER=your_user")
        print("DB_PASSWORD=your_password")
        print("DB_NAME=your_database_name")