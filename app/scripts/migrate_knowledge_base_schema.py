import json
import os
from datetime import datetime, timezone
import requests
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.agent import AgentConfig, AgentKnowledgeBase, AgentConfigVersion, Base
from dotenv import load_dotenv # Import load_dotenv
load_dotenv() # Load environment variables from .env file

# Replicate _get_gcs_file_size from agent_functions.py
def _get_gcs_file_size(gcs_url: str) -> int:
    """
    Retrieves the size of a file from a GCS URL using a HEAD request.
    Returns 0 if the size cannot be determined or on error.
    """
    try:
        response = requests.head(gcs_url, allow_redirects=True, timeout=5)
        response.raise_for_status()
        content_length = response.headers.get('Content-Length')
        if content_length:
            return int(content_length)
        return 0
    except requests.exceptions.RequestException as e:
        print(f"WARNING: Could not get size for GCS URL {gcs_url}: {e}")
        return 0
    except ValueError:
        print(f"WARNING: Invalid Content-Length header for GCS URL {gcs_url}")
        return 0

def migrate_knowledge_base_schema():
    """
    Migrates existing agent knowledge base data from array of strings to JSON objects.
    Reads data from 'extracted_data/agent_knowledge_base_data.json' and updates the database.
    """
    # Directly get database credentials from environment variables
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_user = os.getenv("DB_USER")
    db_password = os.getenv("DB_PASSWORD")
    db_name = os.getenv("DB_NAME")

    if not all([db_host, db_port, db_user, db_password, db_name]):
        raise ValueError("Missing one or more database environment variables (DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME).")

    # Construct the database URI manually
    db_uri = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    engine = create_engine(db_uri)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    input_file = "extracted_data/agent_knowledge_base_data.json"

    if not os.path.exists(input_file):
        print(f"Error: Input file not found at {input_file}")
        print("Please ensure you have run the extraction script first to generate this file.")
        return

    with open(input_file, "r", encoding="utf-8") as f:
        existing_data = json.load(f)

    print(f"Loaded {len(existing_data)} entries from {input_file}")

    with SessionLocal() as db:
        for entry in existing_data:
            agent_id = entry["agent_id"]
            old_urls = entry.get("urls", [])
            old_files = entry.get("files", [])

            print(f"Processing agent_id: {agent_id}")

            # Find the AgentConfig and its current version
            agent_config = db.query(AgentConfig).filter(AgentConfig.id == agent_id).first()

            if not agent_config:
                print(f"  AgentConfig with ID {agent_id} not found. Skipping.")
                continue

            current_version = db.query(AgentConfigVersion).filter(
                AgentConfigVersion.id == agent_config.current_version_id
            ).first()

            if not current_version or not current_version.knowledge_base_id:
                print(f"  Agent {agent_id} has no current version or knowledge base. Skipping.")
                continue

            knowledge_base = db.query(AgentKnowledgeBase).filter(
                AgentKnowledgeBase.id == current_version.knowledge_base_id
            ).first()

            if not knowledge_base:
                print(f"  KnowledgeBase entry for agent {agent_id} not found. Skipping.")
                continue

            # Transform URLs
            new_urls_data = []
            for url_str in old_urls:
                if url_str: # Ensure URL string is not empty
                    new_urls_data.append({
                        "url": url_str,
                        "created_at": datetime.now(timezone.utc).isoformat() # Placeholder
                    })

            # Transform Files
            new_files_data = []
            for file_path in old_files:
                if file_path: # Ensure file path is not empty
                    file_size = _get_gcs_file_size(file_path)
                    new_files_data.append({
                        "file": file_path,
                        "created_at": datetime.now(timezone.utc).isoformat(), # Placeholder
                        "size": file_size
                    })
            
            # Update the knowledge_base object
            knowledge_base.urls = new_urls_data
            knowledge_base.files = new_files_data
            
            db.add(knowledge_base)
            print(f"  Updated knowledge base for agent {agent_id}")
        
        db.commit()
        print("Migration complete. All relevant agent knowledge base entries updated.")

if __name__ == "__main__":
    try:
        migrate_knowledge_base_schema()
    except Exception as e:
        print(f"An error occurred during migration: {e}")
        print("Please ensure your .env file is correctly configured with database credentials.")
        print("And that the 'extracted_data/agent_knowledge_base_data.json' file exists.")