apiVersion: v1
kind: ServiceAccount
metadata:
  name: workflow-service-ai-sa
  namespace: ruh-dev
  labels:
    name: workflow-service-ai-sa
    namespace: ruh-dev
    app: workflow-service-ai
    deployment: workflow-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow-service-ai-dp
  namespace: ruh-dev
  labels:
    name: workflow-service-ai-dp
    namespace: ruh-dev
    app: workflow-service-ai
    serviceaccount: workflow-service-ai-sa
    deployment: workflow-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: workflow-service-ai
      deployment: workflow-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-dev
        app: workflow-service-ai
        deployment: workflow-service-ai-dp
    spec:
      serviceAccountName: workflow-service-ai-sa      
      containers:
      - name: workflow-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50056
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: workflow-service-ai-svc
  namespace: ruh-dev
spec:
  selector:
    app: workflow-service-ai
    deployment: workflow-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50056
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:workflow-service-workflow-hpa
#   namespace:ruh-dev
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:workflow-service-workflow-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
