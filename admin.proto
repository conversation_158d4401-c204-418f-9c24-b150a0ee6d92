syntax = "proto3";

package admin;

enum SourceType {
  GOOGLE_DRIVE = 0;
  SLACK = 1;
}

message SourceModel {
  string id = 1;
  string organisation_id = 2;
  SourceType type = 3;
  string name = 4;
  string created_at = 5;
  string updated_at = 6;
}

service AdminService {
  // Authentication
  rpc login(LoginRequest) returns (LoginResponse);
  rpc accessToken(AccessTokenRequest) returns (AccessTokenResponse);

  // Admin Management
  rpc createAdmin(CreateAdminRequest) returns (AdminResponse);
  rpc getAdmin(GetAdminRequest) returns (AdminResponse);
  rpc updateAdmin(UpdateAdminRequest) returns (AdminResponse);
  rpc deleteAdmin(DeleteAdminRequest) returns (DeleteAdminResponse);
  rpc listAdmins(ListAdminsRequest) returns (ListAdminsResponse);

  // Role Management
  rpc createRole(CreateRoleRequest) returns (RoleResponse);
  rpc getRole(GetRoleRequest) returns (RoleResponse);
  rpc updateRole(UpdateRoleRequest) returns (RoleResponse);
  rpc deleteRole(DeleteRoleRequest) returns (DeleteRoleResponse);
  rpc listRoles(ListRolesRequest) returns (ListRolesResponse);
  rpc assignRole(AssignRoleRequest) returns (AdminResponse);

  // Organisation Management
  rpc getAllOrganisations(GetAllOrganisationsRequest) returns (GetAllOrganisationsResponse);
  rpc getOrganisationDetails(GetOrganisationDetailsRequest) returns (GetOrganisationDetailsResponse);
  rpc getActiveUsersInOrganisation(GetOrganisationActiveUsersRequest) returns (GetOrganisationActiveUsersResponse);

  // Source Management
  rpc listSources (ListSourcesRequest) returns (ListSourcesResponse) {}

  // User Management
  rpc getAllUsers(GetUsersRequest) returns (GetUsersResponse);

  // Agent Management
  rpc getAllAgents(GetAgentsRequest) returns (GetAgentsResponse);
  rpc getAllAgentsFromOrganisation (GetAllAgentsFromOrganisationRequest) returns (ListAgentsResponse);

  // Workflow Management
  rpc getAllWorkflows(GetWorkflowsRequest) returns (GetWorkflowsResponse);
}

// Workflow Management Messages
message GetWorkflowsRequest {
  optional int32 page = 1;                    // Page number (default: 1)
  optional int32 page_size = 2;               // Page size (default: 10)
  optional string visibility = 3;              // Filter by visibility (PUBLIC/PRIVATE)
  optional string status = 4;                  // Filter by status (ACTIVE/INACTIVE)
  optional string category = 5;                // Filter by category
  optional string search = 6;                  // Search term
  optional string tags = 7;                    // Comma-separated tags
}

message GetWorkflowsResponse {
  bool success = 1;
  string message = 2;  // JSON string containing workflows data with pagination info
}

// Existing message definitions...
message LoginRequest {
  string email = 1;
  string password = 2;
}

message LoginResponse {
  bool success = 1;
  string message = 2;
  string accessToken = 3;
  string refreshToken = 4;
  AdminInfo admin = 5;
}

message AccessTokenRequest {
  string refreshToken = 1;
}

message AccessTokenResponse {
  bool success = 1;
  string message = 2;
  string accessToken = 3;
  string refreshToken = 4;
}

message CreateAdminRequest {
  string email = 1;
  string password = 2;
  string fullName = 3;
  repeated string roles = 4;
}

message GetAdminRequest {
  string adminId = 1;
}

message UpdateAdminRequest {
  string adminId = 1;
  optional string fullName = 2;
  optional string email = 3;
  optional string password = 4;
  repeated string roles = 5;
}

message DeleteAdminRequest {
  string adminId = 1;
}

message DeleteAdminResponse {
  bool success = 1;
  string message = 2;
}

message ListAdminsRequest {
  int32 page = 1;
  int32 pageSize = 2;
}

message ListAdminsResponse {
  repeated AdminInfo admins = 1;
  int32 total = 2;
  int32 page = 3;
  int32 totalPages = 4;
}

message AdminResponse {
  bool success = 1;
  string message = 2;
  AdminInfo admin = 3;
}

message AdminInfo {
  string adminId = 1;
  string email = 2;
  string fullName = 3;
  repeated RoleInfo roles = 4;
  string createdAt = 5;
  string updatedAt = 6;
}

message RoleInfo {
  string roleId = 1;
  string name = 2;
  string description = 3;
  repeated string permissions = 4;
  string createdAt = 5;
  string updatedAt = 6;
}

message CreateRoleRequest {
  string name = 1;
  string description = 2;
  repeated string permissions = 3;
}

message GetRoleRequest {
  string roleId = 1;
}

message UpdateRoleRequest {
  string roleId = 1;
  optional string name = 2;
  optional string description = 3;
  repeated string permissions = 4;
}

message DeleteRoleRequest {
  string roleId = 1;
}

message DeleteRoleResponse {
  bool success = 1;
  string message = 2;
}

message ListRolesRequest {
  int32 page = 1;
  int32 pageSize = 2;
}

message ListRolesResponse {
  repeated RoleInfo roles = 1;
  int32 total = 2;
  int32 page = 3;
  int32 totalPages = 4;
}

message RoleResponse {
  bool success = 1;
  string message = 2;
  RoleInfo role = 3;
}

message AssignRoleRequest {
  string adminId = 1;
  repeated string roleIds = 2;
}

// Organisation Management Messages
message GetAllOrganisationsRequest {
  optional int32 page = 1;       // Page number (default: 1)
  optional int32 page_size = 2;  // Page size (default: 12)
}

message GetAllOrganisationsResponse {
  bool success = 1;
  string message = 2;  // JSON string containing organisations data with pagination info
}

message GetOrganisationDetailsRequest {
  string organisation_id = 1;  // Required organisation ID
}

message GetOrganisationDetailsResponse {
  bool success = 1;
  string message = 2;  // JSON string containing complete organisation details
}

// Agent Management Messages
message GetAgentsRequest {
  optional int32 page = 1;                    // Page number (default: 1)
  optional int32 page_size = 2;               // Page size (default: 12)
  optional string visibility = 3;              // Filter by visibility (PUBLIC/PRIVATE)
  optional string agent_category = 4;          // Filter by agent category
  optional string department = 5;              // Filter by department
  optional bool is_bench_employee = 6;         // Filter by bench employee status
  optional string search = 7;                  // Search term
}

message GetAgentsResponse {
  bool success = 1;
  string message = 2;  // JSON string containing agents data with pagination info
}

// Organisation Data Structures
message OrganisationInfo {
  string id = 1;
  string name = 2;
  string description = 3;
  string logo = 4;
  string industry = 5;
  string created_at = 6;
  string status = 7;
  int32 total_departments = 8;
  int32 total_users = 9;
  string type = 10;
  int32 total_payments = 11;
  int32 total_rcu = 12;
  int32 rcu_used = 13;
}

message OrganisationDetails {
  string id = 1;
  string name = 2;
  string description = 3;
  string logo = 4;
  string website_url = 5;
  string industry = 6;
  string status = 7;
  string type = 8;
  string address = 9;
  string phone = 10;
  string email = 11;
  string size = 12;
  string created_by = 13;
  string created_at = 14;
  string updated_at = 15;
  string mcp_key = 16;
  bool is_key_revoked = 17;
  AdminInfo admin = 18;
  repeated DepartmentInfo departments = 19;
  OrganisationStatistics statistics = 20;
}

message DepartmentInfo {
  string id = 1;
  string name = 2;
  string description = 3;
  string created_at = 4;
  string updated_at = 5;
  int32 member_count = 6;
}

message OrganisationStatistics {
  int32 total_users = 1;
  int32 total_departments = 2;
  int32 total_department_members = 3;
  double avg_members_per_dept = 4;
  int32 departments_with_members = 5;
  int32 empty_departments = 6;
}

// User Management

message GetUsersRequest {
  optional int32 page = 1;                   // Page number (default: 1)
  optional int32 page_size = 2;              // Page size (default: 12)
  optional string company = 3;               // Filter by company
  optional string department = 4;            // Filter by department
  optional string role = 5;                  // Filter by role
  optional bool is_active = 6;               // Filter by active status
  optional bool is_email_verified = 7;       // Filter by email verification status
  optional string search = 8;                // Search by name, email, department, etc.
}

message UserInfo {
  string id = 1;
  string full_name = 2;
  string email = 3;
  string profile_image = 4;
  bool is_active = 5;
  bool is_email_verified = 6;
  string default_organization = 7;
  string company = 8;
  string role = 9;
  string department = 10;
  string job_role = 11;
  string created_at = 12;
  string updated_at = 13;
}

message GetUsersResponse {
  bool success = 1;
  string message = 2;  // JSON string containing agents data with pagination info
}

// Request to list sources
message ListSourcesRequest {
  string organisation_id = 1;
}

// Response for list sources operation
message ListSourcesResponse {
  bool success = 1;
  string message = 2;
  repeated SourceModel sources = 3;
  bool isInitialMapping = 4;  // True if at least one department other than general has access to at least one folder
}

// Organisation Users Messages
message GetOrganisationActiveUsersRequest {
  string organisation_id = 1;  // Required organisation ID
  optional int32 page_size = 2;  // Page size (default: 10)
  optional int32 page_number = 3;  // Page number (default: 1)
  optional string department_id = 4; // Optional department ID to filter users
}

message UserDepartmentDetail {
  string name = 1;
  string role = 2;
  string permission = 3;
}

message DepartmentUser {
  string id = 1;
  string name = 2;
  string email = 3;
  repeated UserDepartmentDetail departments = 4;
}

message GetOrganisationActiveUsersResponse {
  string department_name = 1;
  string department_desc = 2;
  repeated DepartmentUser users = 3;
  int32 total_count = 4;
  int32 page = 5;
  int32 page_size = 6;
  string message = 7;
  bool success = 8;
}

enum Visibility {
  DEFAULT = 0; // Default visibility, can be used for future expansion
  PRIVATE = 1;
  PUBLIC = 2;
}

enum Status {
  INVALID = 0; // Default status, can be used for future expansion
  ACTIVE = 1;
  INACTIVE = 2;
  BENCH = 3;  // New value
}

enum CreatorRole {
  CREATOR_ROLE_UNSPECIFIED = 0; // Default value, can be used for future expansion
  MEMBER = 1;
  CREATOR = 2;
  VIEWER = 3;
}

// Request message for getting all agents from an organization
message GetAllAgentsFromOrganisationRequest {
  string organisation_id = 1;
  optional int32 page = 2;       // Page number (default: 1)
  optional int32 page_size = 3;  // Page size (default: 12)
}

message Agent {
  string id = 1;
  string name = 2;
  string description = 3;
  string department = 4;
  string owner_id = 5;
  string owner_name = 6;
  repeated string user_ids = 7;
  string created_at = 8;
  string updated_at = 9;
  Visibility visibility = 10;
  Status status = 11;
  CreatorRole creator_role = 12;
}

// Response containing a list of agents
message ListAgentsResponse {
  bool success = 1;
  repeated Agent agents = 2;
  int32 total = 3;
  int32 page = 4;
  int32 page_size = 5;
}