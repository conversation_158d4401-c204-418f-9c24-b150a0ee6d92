#!/usr/bin/env python3
"""
Database Data Copy Script

This script copies all Provider and Model data from a source database to a destination database.
It handles the foreign key relationships and ensures data integrity during the transfer.

Usage:
    python copy_database_data.py

Make sure to set the SOURCE_DATABASE_URL and DEST_DATABASE_URL variables before running.
"""

import sys
import os
from datetime import datetime
import uuid
from sqlalchemy import create_engine, Column, String, DateTime, Boolean, Float, Integer, Text, ForeignKey
from sqlalchemy.orm import declarative_base, relationship, sessionmaker
from sqlalchemy.exc import SQLAlchemyError
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database URLs - Replace these with your actual database URLs
SOURCE_DATABASE_URL = "postgresql://user:password@localhost:5432/source_db"
DEST_DATABASE_URL = "postgresql://user:password@localhost:5432/dest_db"

# Table names
PROVIDERS_TABLE = "providers"
MODELS_TABLE = "models"

# Define the models (same as in your app/models/provider.py)
Base = declarative_base()


class Provider(Base):
    __tablename__ = PROVIDERS_TABLE

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    provider = Column(String(255), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    base_url = Column(String(500), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationship to models
    models = relationship("Model", back_populates="provider", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Provider id={self.id} provider='{self.provider}' is_active={self.is_active}>"


class Model(Base):
    __tablename__ = MODELS_TABLE

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    provider_id = Column(String, ForeignKey(f"{PROVIDERS_TABLE}.id"), nullable=False, index=True)
    model = Column(String(255), nullable=False)
    model_id = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    input_price_per_token = Column(Float, nullable=True)
    output_price_per_token = Column(Float, nullable=True)
    max_tokens = Column(Integer, nullable=True)
    context_window = Column(Integer, nullable=True)
    temperature = Column(Float, nullable=True, default=0.7)
    provider_type = Column(String(100), nullable=False, default="chat")
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationship to provider
    provider = relationship("Provider", back_populates="models")

    def __repr__(self):
        return f"<Model id={self.id} model='{self.model}' provider_id='{self.provider_id}' is_active={self.is_active}>"


def create_database_engines(source_url: str, dest_url: str):
    """Create database engines for source and destination databases."""
    try:
        source_engine = create_engine(source_url, echo=False)
        dest_engine = create_engine(dest_url, echo=False)
        
        logger.info("Database engines created successfully")
        return source_engine, dest_engine
    except Exception as e:
        logger.error(f"Failed to create database engines: {e}")
        raise


def create_sessions(source_engine, dest_engine):
    """Create database sessions for source and destination databases."""
    try:
        SourceSession = sessionmaker(bind=source_engine)
        DestSession = sessionmaker(bind=dest_engine)
        
        source_session = SourceSession()
        dest_session = DestSession()
        
        logger.info("Database sessions created successfully")
        return source_session, dest_session
    except Exception as e:
        logger.error(f"Failed to create database sessions: {e}")
        raise


def copy_providers(source_session, dest_session):
    """Copy all providers from source to destination database."""
    try:
        # Fetch all providers from source database
        providers = source_session.query(Provider).all()
        logger.info(f"Found {len(providers)} providers in source database")
        
        if not providers:
            logger.warning("No providers found in source database")
            return []
        
        # Copy providers to destination database
        copied_providers = []
        for provider in providers:
            new_provider = Provider(
                id=provider.id,
                provider=provider.provider,
                description=provider.description,
                base_url=provider.base_url,
                is_active=provider.is_active,
                is_default=provider.is_default,
                created_at=provider.created_at,
                updated_at=provider.updated_at
            )
            dest_session.add(new_provider)
            copied_providers.append(new_provider)
        
        dest_session.commit()
        logger.info(f"Successfully copied {len(copied_providers)} providers")
        return copied_providers
        
    except SQLAlchemyError as e:
        dest_session.rollback()
        logger.error(f"Database error while copying providers: {e}")
        raise
    except Exception as e:
        dest_session.rollback()
        logger.error(f"Unexpected error while copying providers: {e}")
        raise


def copy_models(source_session, dest_session):
    """Copy all models from source to destination database."""
    try:
        # Fetch all models from source database
        models = source_session.query(Model).all()
        logger.info(f"Found {len(models)} models in source database")
        
        if not models:
            logger.warning("No models found in source database")
            return []
        
        # Copy models to destination database
        copied_models = []
        for model in models:
            new_model = Model(
                id=model.id,
                provider_id=model.provider_id,
                model=model.model,
                model_id=model.model_id,
                description=model.description,
                input_price_per_token=model.input_price_per_token,
                output_price_per_token=model.output_price_per_token,
                max_tokens=model.max_tokens,
                context_window=model.context_window,
                temperature=model.temperature,
                provider_type=model.provider_type,
                is_active=model.is_active,
                is_default=model.is_default,
                created_at=model.created_at,
                updated_at=model.updated_at
            )
            dest_session.add(new_model)
            copied_models.append(new_model)
        
        dest_session.commit()
        logger.info(f"Successfully copied {len(copied_models)} models")
        return copied_models
        
    except SQLAlchemyError as e:
        dest_session.rollback()
        logger.error(f"Database error while copying models: {e}")
        raise
    except Exception as e:
        dest_session.rollback()
        logger.error(f"Unexpected error while copying models: {e}")
        raise


def verify_data_integrity(source_session, dest_session):
    """Verify that data was copied correctly."""
    try:
        # Count records in both databases
        source_providers_count = source_session.query(Provider).count()
        source_models_count = source_session.query(Model).count()
        
        dest_providers_count = dest_session.query(Provider).count()
        dest_models_count = dest_session.query(Model).count()
        
        logger.info(f"Source database - Providers: {source_providers_count}, Models: {source_models_count}")
        logger.info(f"Destination database - Providers: {dest_providers_count}, Models: {dest_models_count}")
        
        if source_providers_count == dest_providers_count and source_models_count == dest_models_count:
            logger.info("✅ Data integrity verification passed!")
            return True
        else:
            logger.error("❌ Data integrity verification failed!")
            return False
            
    except Exception as e:
        logger.error(f"Error during data integrity verification: {e}")
        return False


def create_tables_if_not_exist(engine):
    """Create tables in the destination database if they don't exist."""
    try:
        Base.metadata.create_all(engine)
        logger.info("Tables created/verified in destination database")
    except Exception as e:
        logger.error(f"Error creating tables: {e}")
        raise


def main():
    """Main function to orchestrate the data copy process."""
    logger.info("Starting database data copy process...")
    
    # Validate database URLs
    if not SOURCE_DATABASE_URL or not DEST_DATABASE_URL:
        logger.error("Please set SOURCE_DATABASE_URL and DEST_DATABASE_URL variables")
        sys.exit(1)
    
    if SOURCE_DATABASE_URL == DEST_DATABASE_URL:
        logger.error("Source and destination database URLs cannot be the same")
        sys.exit(1)
    
    source_session = None
    dest_session = None
    
    try:
        # Create database engines
        source_engine, dest_engine = create_database_engines(SOURCE_DATABASE_URL, DEST_DATABASE_URL)
        
        # Create tables in destination database if they don't exist
        create_tables_if_not_exist(dest_engine)
        
        # Create database sessions
        source_session, dest_session = create_sessions(source_engine, dest_engine)
        
        # Copy providers first (due to foreign key relationship)
        logger.info("Copying providers...")
        copied_providers = copy_providers(source_session, dest_session)
        
        # Copy models
        logger.info("Copying models...")
        copied_models = copy_models(source_session, dest_session)
        
        # Verify data integrity
        logger.info("Verifying data integrity...")
        if verify_data_integrity(source_session, dest_session):
            logger.info("🎉 Data copy completed successfully!")
        else:
            logger.error("⚠️ Data copy completed but integrity verification failed")
            
    except Exception as e:
        logger.error(f"Fatal error during data copy process: {e}")
        sys.exit(1)
        
    finally:
        # Close database sessions
        if source_session:
            source_session.close()
        if dest_session:
            dest_session.close()
        logger.info("Database sessions closed")


if __name__ == "__main__":
    # You can also pass database URLs as command line arguments
    if len(sys.argv) == 3:
        SOURCE_DATABASE_URL = sys.argv[1]
        DEST_DATABASE_URL = sys.argv[2]
    
    main()