#!/usr/bin/env python3
"""
Script to fetch and add OpenRouter providers and models to the database.
This script fetches data from https://openrouter.ai/api/v1/models and adds them to the database.
"""

import sys
import os
import requests
import json
from datetime import datetime
from typing import Dict, Any, Optional

# Add the parent directory to the Python path to import app modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings
from app.models.provider import Provider, Model
from app.db.session import SessionLocal

def safe_float_conversion(value: Any) -> Optional[float]:
    """Safely convert a value to float."""
    if value is None or value == "0":
        return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def extract_provider_and_model_name(model_id: str) -> tuple[str, str]:
    """
    Extract provider name and model name from OpenRouter model ID.
    Example: "moonshotai/kimi-k2:free" -> ("moonshotai", "kimi-k2:free")
    """
    if "/" in model_id:
        parts = model_id.split("/", 1)
        return parts[0], parts[1]
    else:
        # If no slash, treat the whole thing as model name with unknown provider
        return "unknown", model_id

def fetch_openrouter_models() -> Dict[str, Any]:
    """Fetch models from OpenRouter API."""
    url = "https://openrouter.ai/api/v1/models"
    
    try:
        print(f"Fetching models from OpenRouter API: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        if "data" not in data:
            raise ValueError("Invalid response format: missing 'data' field")
        
        print(f"Successfully fetched {len(data['data'])} models from OpenRouter API")
        return data
    
    except requests.RequestException as e:
        print(f"❌ Failed to fetch data from OpenRouter API: {str(e)}")
        raise
    except json.JSONDecodeError as e:
        print(f"❌ Failed to parse JSON response: {str(e)}")
        raise

def check_existing_platform_data(session):
    """Check existing providers and models by platform for debugging."""
    print("\n🔍 Checking existing platform data...")

    # Check providers by platform
    providers_by_platform = {}
    all_providers = session.query(Provider).all()

    for provider in all_providers:
        platform = provider.platform
        if platform not in providers_by_platform:
            providers_by_platform[platform] = []
        providers_by_platform[platform].append(provider.provider)

    for platform, provider_names in providers_by_platform.items():
        print(f"📊 Platform '{platform}': {len(provider_names)} providers")
        if len(provider_names) <= 10:  # Show details for small lists
            print(f"   Providers: {', '.join(sorted(set(provider_names)))}")
        else:
            unique_providers = sorted(set(provider_names))
            print(f"   Providers: {', '.join(unique_providers[:5])}... (+{len(unique_providers)-5} more)")

    # Check for potential duplicates across platforms
    all_provider_names = set()
    duplicate_providers = set()

    for provider in all_providers:
        if provider.provider in all_provider_names:
            duplicate_providers.add(provider.provider)
        all_provider_names.add(provider.provider)

    if duplicate_providers:
        print(f"⚠️  Providers that exist on multiple platforms: {', '.join(sorted(duplicate_providers))}")
    else:
        print("✅ No provider name conflicts across platforms")

    print()

def add_openrouter_providers_and_models():
    """Main function to add OpenRouter providers and models to the database."""
    session = SessionLocal()
    
    try:
        # Check existing platform data first
        check_existing_platform_data(session)

        # Fetch data from OpenRouter API
        api_data = fetch_openrouter_models()
        models_data = api_data["data"]
        
        # Constants for OpenRouter
        OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
        OPENROUTER_PLATFORM = "openrouter"
        
        # Track providers to avoid duplicates
        providers_seen = {}
        
        # Statistics
        stats = {
            "providers_added": 0,
            "providers_skipped": 0,
            "models_added": 0,
            "models_skipped": 0,
            "errors": 0
        }
        
        print(f"\n🔄 Processing {len(models_data)} models from OpenRouter...")
        
        for item in models_data:
            try:
                # Extract basic information
                model_id = item.get("id", "")
                if not model_id:
                    print(f"⚠️  Skipping model with missing ID")
                    stats["errors"] += 1
                    continue
                
                # Extract provider and model names
                provider_name, model_name = extract_provider_and_model_name(model_id)
                
                # Get model information
                model_description = item.get("description", f"OpenRouter model {model_name}")
                context_length = item.get("context_length", 4096)
                
                # Get pricing information
                pricing = item.get("pricing", {})
                input_price = safe_float_conversion(pricing.get("prompt"))
                output_price = safe_float_conversion(pricing.get("completion"))
                
                # Get top provider information for max tokens and context window
                top_provider = item.get("top_provider", {})
                max_completion_tokens = top_provider.get("max_completion_tokens")
                provider_context_length = top_provider.get("context_length", context_length)
                
                # Use the larger context window value
                final_context_window = max(context_length, provider_context_length) if provider_context_length else context_length
                
                # Create or get provider (check by provider name AND platform)
                provider_key = f"{provider_name}_{OPENROUTER_PLATFORM}"

                if provider_key not in providers_seen:
                    # Check if provider already exists for this specific platform
                    existing_provider = session.query(Provider).filter(
                        Provider.provider == provider_name,
                        Provider.platform == OPENROUTER_PLATFORM
                    ).first()

                    if existing_provider:
                        providers_seen[provider_key] = existing_provider
                        stats["providers_skipped"] += 1
                        print(f"📋 Provider '{provider_name}' (platform: {OPENROUTER_PLATFORM}) already exists, reusing...")
                    else:
                        # Check if same provider exists for different platforms
                        other_platform_providers = session.query(Provider).filter(
                            Provider.provider == provider_name,
                            Provider.platform != OPENROUTER_PLATFORM
                        ).all()

                        if other_platform_providers:
                            platforms = [p.platform for p in other_platform_providers]
                            print(f"ℹ️  Provider '{provider_name}' exists for platform(s): {', '.join(platforms)}. Creating separate OpenRouter entry...")

                        # Create new provider specifically for OpenRouter platform
                        new_provider = Provider(
                            provider=provider_name,
                            description=f"{provider_name.capitalize()} is an AI model provider available through OpenRouter platform.",
                            base_url=OPENROUTER_BASE_URL,
                            platform=OPENROUTER_PLATFORM,
                            is_active=True,
                            is_default=False,
                            created_at=datetime.utcnow(),
                            updated_at=datetime.utcnow()
                        )
                        session.add(new_provider)
                        session.flush()  # Get the ID
                        providers_seen[provider_key] = new_provider
                        stats["providers_added"] += 1
                        print(f"✅ Added new provider: {provider_name} (platform: {OPENROUTER_PLATFORM})")
                else:
                    # Provider already processed in this session
                    pass
                
                provider = providers_seen[provider_key]

                # Check if model already exists for this specific provider (same provider name + platform)
                existing_model = session.query(Model).filter(
                    Model.provider_id == provider.id,
                    Model.model == model_name
                ).first()

                if existing_model:
                    stats["models_skipped"] += 1
                    print(f"📋 Model '{model_name}' already exists for provider '{provider_name}' (platform: {OPENROUTER_PLATFORM}), skipping...")
                    continue

                # Check if same model exists for same provider but different platform
                other_platform_models = session.query(Model).join(Provider).filter(
                    Provider.provider == provider_name,
                    Provider.platform != OPENROUTER_PLATFORM,
                    Model.model == model_name
                ).all()

                if other_platform_models:
                    other_platforms = [m.provider.platform for m in other_platform_models]
                    print(f"ℹ️  Model '{model_name}' exists for provider '{provider_name}' on platform(s): {', '.join(set(other_platforms))}. Adding OpenRouter version...")
                
                # Create new model
                new_model = Model(
                    provider_id=provider.id,
                    model=model_name,
                    model_id=model_id,  # Use the full OpenRouter model ID
                    description=model_description,
                    input_price_per_token=input_price,
                    output_price_per_token=output_price,
                    max_tokens=max_completion_tokens,
                    context_window=final_context_window,
                    temperature=0.7,  # Default temperature
                    provider_type="chat",  # Default to chat type
                    is_active=True,
                    is_default=False,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                session.add(new_model)
                stats["models_added"] += 1
                print(f"✅ Added model: {provider_name}/{model_name} (platform: {OPENROUTER_PLATFORM})")
                
            except Exception as e:
                print(f"❌ Error processing model {item.get('id', 'unknown')}: {str(e)}")
                stats["errors"] += 1
                continue
        
        # Commit all changes
        session.commit()
        
        # Print final statistics
        print(f"\n🎉 OpenRouter import completed successfully!")
        print(f"📊 Import Statistics:")
        print(f"   • Providers added (OpenRouter): {stats['providers_added']}")
        print(f"   • Providers skipped (already exist): {stats['providers_skipped']}")
        print(f"   • Models added (OpenRouter): {stats['models_added']}")
        print(f"   • Models skipped (already exist): {stats['models_skipped']}")
        print(f"   • Errors: {stats['errors']}")
        print(f"   • Total models processed: {len(models_data)}")

        # Show final platform data summary
        print(f"\n📊 Final Platform Summary:")
        openrouter_providers = session.query(Provider).filter_by(platform=OPENROUTER_PLATFORM).count()
        openrouter_models = session.query(Model).join(Provider).filter(Provider.platform == OPENROUTER_PLATFORM).count()

        requesty_providers = session.query(Provider).filter_by(platform="requesty").count()
        requesty_models = session.query(Model).join(Provider).filter(Provider.platform == "requesty").count()

        print(f"   • OpenRouter platform: {openrouter_providers} providers, {openrouter_models} models")
        print(f"   • Requesty platform: {requesty_providers} providers, {requesty_models} models")

        # Check for providers that exist on both platforms
        openrouter_provider_names = set(p.provider for p in session.query(Provider).filter_by(platform=OPENROUTER_PLATFORM).all())
        requesty_provider_names = set(p.provider for p in session.query(Provider).filter_by(platform="requesty").all())
        common_providers = openrouter_provider_names.intersection(requesty_provider_names)

        if common_providers:
            print(f"   • Providers available on both platforms: {len(common_providers)}")
            if len(common_providers) <= 10:
                print(f"     {', '.join(sorted(common_providers))}")

        return True
        
    except Exception as e:
        print(f"❌ Fatal error during import: {str(e)}")
        session.rollback()
        return False
        
    finally:
        session.close()

if __name__ == "__main__":
    print("🚀 Starting OpenRouter providers and models import...")
    print("=" * 60)
    
    success = add_openrouter_providers_and_models()
    
    if success:
        print("\n✅ Import completed successfully!")
        print("You can now use the OpenRouter providers and models in your application.")
    else:
        print("\n❌ Import failed. Please check the error messages above.")
        exit(1)