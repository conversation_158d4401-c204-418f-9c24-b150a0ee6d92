#!/usr/bin/env python3
"""
Simple Database Data Copy Script for Provider and Model tables

Usage:
    python simple_copy_data.py "source_db_url" "dest_db_url"

Example:
    python simple_copy_data.py "postgresql://user:pass@localhost:5432/source" "postgresql://user:pass@localhost:5432/dest"
"""

import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def copy_table_data(source_engine, dest_engine, table_name, columns):
    """Copy data from source table to destination table."""
    try:
        # Create sessions
        SourceSession = sessionmaker(bind=source_engine)
        DestSession = sessionmaker(bind=dest_engine)
        
        with SourceSession() as source_session, DestSession() as dest_session:
            # Get all data from source table
            select_query = f"SELECT {', '.join(columns)} FROM {table_name}"
            result = source_session.execute(text(select_query))
            rows = result.fetchall()
            
            if not rows:
                logger.info(f"No data found in {table_name}")
                return 0
            
            logger.info(f"Found {len(rows)} records in {table_name}")
            
            # Prepare insert statement
            placeholders = ', '.join([f':{col}' for col in columns])
            insert_query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
            
            # Insert data into destination table
            for row in rows:
                row_dict = dict(zip(columns, row))
                dest_session.execute(text(insert_query), row_dict)
            
            dest_session.commit()
            logger.info(f"Successfully copied {len(rows)} records to {table_name}")
            return len(rows)
            
    except Exception as e:
        logger.error(f"Error copying {table_name}: {e}")
        raise


def main():
    """Main function to copy data between databases."""
    if len(sys.argv) != 3:
        print("Usage: python simple_copy_data.py 'source_db_url' 'dest_db_url'")
        print("Example: python simple_copy_data.py 'postgresql://user:pass@localhost:5432/source' 'postgresql://user:pass@localhost:5432/dest'")
        sys.exit(1)
    
    source_url = sys.argv[1]
    dest_url = sys.argv[2]
    
    if source_url == dest_url:
        logger.error("Source and destination URLs cannot be the same")
        sys.exit(1)
    
    try:
        # Create engines
        source_engine = create_engine(source_url)
        dest_engine = create_engine(dest_url)
        
        logger.info("Connected to both databases successfully")
        
        # Define table structures
        providers_columns = [
            'id', 'provider', 'description', 'base_url', 'is_active', 
            'is_default', 'created_at', 'updated_at'
        ]
        
        models_columns = [
            'id', 'provider_id', 'model', 'model_id', 'description',
            'input_price_per_token', 'output_price_per_token', 'max_tokens',
            'context_window', 'temperature', 'provider_type', 'is_active',
            'is_default', 'created_at', 'updated_at'
        ]
        
        # Copy providers first (due to foreign key relationship)
        logger.info("Copying providers table...")
        providers_count = copy_table_data(source_engine, dest_engine, 'providers', providers_columns)
        
        # Copy models
        logger.info("Copying models table...")
        models_count = copy_table_data(source_engine, dest_engine, 'models', models_columns)
        
        logger.info(f"✅ Data copy completed successfully!")
        logger.info(f"Total records copied: {providers_count} providers, {models_count} models")
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()