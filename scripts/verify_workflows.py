import sys
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Add the current directory to the path so we can import the app modules
sys.path.append(os.getcwd())

from app.core.config import settings
from app.models.workflow import Workflow


def verify_workflows():
    # Connect to the database
    engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    try:
        # Query all workflows
        workflows = db.query(Workflow).all()

        print(f"Found {len(workflows)} workflows in the database:")

        # Print workflow details
        for i, workflow in enumerate(workflows, 1):
            print(f"\nWorkflow {i}:")
            print(f"  ID: {workflow.id}")
            print(f"  Name: {workflow.name}")
            print(f"  Description: {workflow.description}")
            print(f"  Owner ID: {workflow.owner_id}")
            print(f"  User ID: {workflow.user_id}")
            print(f"  Owner Type: {workflow.owner_type}")
            print(f"  Start Nodes: {workflow.start_nodes}")
            print(f"  Visibility: {workflow.visibility}")
            print(f"  Status: {workflow.status}")
            print(f"  Created At: {workflow.created_at}")
            print(f"  Updated At: {workflow.updated_at}")

    except Exception as e:
        print(f"Error querying workflows: {str(e)}")
    finally:
        db.close()


if __name__ == "__main__":
    verify_workflows()
