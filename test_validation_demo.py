#!/usr/bin/env python3
"""
Demo script to show the call actions validation in action.
This demonstrates the unique combination validation for action_type and execution_type.
"""

from app.schemas.agent import UpdateCallActionsRequest, CallAction, ActionTypeEnum, ExecutionTypeEnum
from pydantic import ValidationError

def test_valid_combinations():
    """Test that all 4 valid combinations work."""
    print("✅ Testing valid combinations...")
    
    valid_call_actions = [
        CallAction(action_type=ActionTypeEnum.POST_CALL, execution_type=ExecutionTypeEnum.WORKFLOW, id="workflow-1"),
        CallAction(action_type=ActionTypeEnum.POST_CALL, execution_type=ExecutionTypeEnum.MCP, id="mcp-1"),
        CallAction(action_type=ActionTypeEnum.PRE_CALL, execution_type=ExecutionTypeEnum.WORKFLOW, id="workflow-2"),
        CallAction(action_type=ActionTypeEnum.PRE_CALL, execution_type=ExecutionTypeEnum.MCP, id="mcp-2"),
    ]
    
    try:
        request = UpdateCallActionsRequest(call_actions=valid_call_actions)
        print(f"   ✓ Successfully created request with {len(request.call_actions)} call actions")
        for action in request.call_actions:
            print(f"     - {action.action_type} + {action.execution_type} → {action.id}")
    except ValidationError as e:
        print(f"   ✗ Unexpected validation error: {e}")

def test_duplicate_combination():
    """Test that duplicate combinations are rejected."""
    print("\n❌ Testing duplicate combinations...")
    
    duplicate_call_actions = [
        CallAction(action_type=ActionTypeEnum.POST_CALL, execution_type=ExecutionTypeEnum.WORKFLOW, id="workflow-1"),
        CallAction(action_type=ActionTypeEnum.POST_CALL, execution_type=ExecutionTypeEnum.WORKFLOW, id="workflow-2"),  # Duplicate!
    ]
    
    try:
        request = UpdateCallActionsRequest(call_actions=duplicate_call_actions)
        print(f"   ✗ Validation should have failed but didn't!")
    except ValidationError as e:
        print(f"   ✓ Correctly rejected duplicate combination:")
        print(f"     Error: {str(e).split('validation error')[1] if 'validation error' in str(e) else str(e)}")

def test_empty_list():
    """Test that empty list is valid."""
    print("\n✅ Testing empty list...")
    
    try:
        request = UpdateCallActionsRequest(call_actions=[])
        print(f"   ✓ Successfully created request with empty call actions list")
    except ValidationError as e:
        print(f"   ✗ Unexpected validation error: {e}")

def test_partial_combinations():
    """Test that partial combinations work."""
    print("\n✅ Testing partial combinations...")
    
    partial_call_actions = [
        CallAction(action_type=ActionTypeEnum.POST_CALL, execution_type=ExecutionTypeEnum.WORKFLOW, id="workflow-1"),
        CallAction(action_type=ActionTypeEnum.PRE_CALL, execution_type=ExecutionTypeEnum.MCP, id="mcp-1"),
    ]
    
    try:
        request = UpdateCallActionsRequest(call_actions=partial_call_actions)
        print(f"   ✓ Successfully created request with {len(request.call_actions)} call actions")
        for action in request.call_actions:
            print(f"     - {action.action_type} + {action.execution_type} → {action.id}")
    except ValidationError as e:
        print(f"   ✗ Unexpected validation error: {e}")

if __name__ == "__main__":
    print("🧪 Call Actions Validation Demo")
    print("=" * 50)
    
    test_valid_combinations()
    test_duplicate_combination()
    test_empty_list()
    test_partial_combinations()
    
    print("\n" + "=" * 50)
    print("📋 Summary:")
    print("   • Maximum 4 call actions allowed (one per combination)")
    print("   • Valid combinations:")
    print("     - POST_CALL + WORKFLOW")
    print("     - POST_CALL + MCP") 
    print("     - PRE_CALL + WORKFLOW")
    print("     - PRE_CALL + MCP")
    print("   • Duplicate combinations are rejected")
    print("   • Empty list is valid")
    print("   • Partial combinations are valid")
